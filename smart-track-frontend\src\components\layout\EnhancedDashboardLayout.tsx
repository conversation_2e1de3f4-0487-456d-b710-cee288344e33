'use client';

import { useState } from 'react';
import { User } from '@/types';
import { Layout } from 'antd';
import DashboardLayout from './DashboardLayout';
import NavigationSidebar from './NavigationSidebar';

const { Content } = Layout;

interface EnhancedDashboardLayoutProps {
  children: React.ReactNode;
  user: User;
  title: string;
  showSidebar?: boolean;
}

export default function EnhancedDashboardLayout({ 
  children, 
  user, 
  title, 
  showSidebar = true 
}: EnhancedDashboardLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [darkMode, setDarkMode] = useState(false);

  if (!showSidebar) {
    // Use original dashboard layout for pages that don't need sidebar
    return (
      <DashboardLayout user={user} title={title}>
        {children}
      </DashboardLayout>
    );
  }

  return (
    <Layout className="h-screen">
      <NavigationSidebar
        user={user}
        collapsed={sidebarCollapsed}
        onCollapse={setSidebarCollapsed}
        darkMode={darkMode}
      />
      
      <Layout className="flex-1">
        <DashboardLayout user={user} title={title}>
          {children}
        </DashboardLayout>
      </Layout>
    </Layout>
  );
}
