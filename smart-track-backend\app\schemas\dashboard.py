from pydantic import BaseModel
from typing import List, Optional

class DashboardStats(BaseModel):
    """Basic dashboard statistics"""
    total_visits: int
    completed_visits: int
    pending_visits: int
    total_customers: int
    active_sales_reps: int
    completion_rate: float

class DashboardKPIs(BaseModel):
    completed_visits: int
    target_visits: int
    target_percentage: float
    monthly_target: int
    revenue: float
    revenue_target: float
    clients_visited: int
    total_clients: int

class DashboardUpcomingVisit(BaseModel):
    id: int
    client: str
    time: str
    location: str
    type: str
    date: str
    priority: str
    duration: str
    contact: str

class DashboardTask(BaseModel):
    id: str
    title: str
    priority: str
    due_date: str
    category: str
    status: str

class DashboardData(BaseModel):
    kpis: DashboardKPIs
    upcoming_visits: List[DashboardUpcomingVisit]
    pending_tasks: List[DashboardTask]
