# Smart Track Frontend Dashboard

A comprehensive role-based dashboard for the Smart Track Sales Force Automation system.

## Features

### Role-Based Access Control

1. **Admin Dashboard**
   - Complete CRUD operations on all models
   - User management
   - Customer management
   - Visit management
   - System settings and monitoring

2. **Supervisor Dashboard**
   - Team overview and management
   - Weekly plan creation and management
   - Visit tracking for sales reps
   - Performance monitoring

3. **Manager Dashboard**
   - Multi-supervisor oversight
   - Region performance analytics
   - Comprehensive reporting
   - Strategic planning tools

## Technology Stack

- **Framework**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Data Fetching**: TanStack Query (React Query)
- **Forms**: React Hook Form with Zod validation
- **Authentication**: JWT with automatic token refresh
- **Container**: Docker

## Project Structure

```
src/
├── app/                    # Next.js App Router
├── components/            # Reusable components
│   ├── dashboards/       # Role-specific dashboards
│   ├── admin/            # Admin-specific components
│   ├── supervisor/       # Supervisor-specific components
│   ├── manager/          # Manager-specific components
│   ├── layout/           # Layout components
│   └── ui/               # Basic UI components
├── providers/            # Context providers
├── services/             # API services
├── stores/               # Zustand stores
└── types/                # TypeScript type definitions
```

## Getting Started

### Prerequisites

- Node.js 18 or higher
- Docker (for containerized deployment)

### Development Setup

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env.local
```

3. Configure your environment variables:
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NODE_ENV=development
```

4. Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:3000`

### Docker Deployment

The frontend is automatically built and deployed when using the backend's docker-compose setup:

```bash
cd ../smart-track-backend
docker-compose up
```

This will start:
- PostgreSQL database on port 5433
- Redis on port 6379
- Backend API on port 8000
- Frontend dashboard on port 3000

## API Integration

The frontend integrates with the Smart Track backend API with the following features:

- **Authentication**: JWT-based login with automatic token refresh
- **Role-based routing**: Different dashboards based on user roles
- **Real-time data**: Live updates for visits and team activities
- **Offline support**: Caching with React Query for offline functionality

## User Roles & Permissions

### Admin
- Full system access
- User management (create, read, update, delete)
- Customer management
- Visit management
- System configuration

### Supervisor
- Team management for assigned sales reps
- Weekly plan creation and approval
- Visit tracking and monitoring
- Performance reporting for team

### Manager
- Multi-team oversight
- Supervisor performance monitoring
- Regional analytics
- Strategic reporting

## Development Guidelines

### Component Structure
- Use functional components with TypeScript
- Implement proper error boundaries
- Follow React best practices for performance

### State Management
- Use Zustand for global state (auth, user data)
- Use React Query for server state
- Keep component state local when possible

### API Calls
- All API calls go through the centralized `apiService`
- Use React Query for caching and background updates
- Implement proper error handling and loading states

## Security Features

- JWT token management with automatic refresh
- Role-based route protection
- Secure API communication
- Input validation with Zod schemas

## Performance Optimizations

- Code splitting with Next.js dynamic imports
- Image optimization
- Bundle size optimization
- Proper caching strategies

## Future Enhancements

- Real-time notifications
- Offline functionality
- Mobile responsive design improvements
- Advanced analytics dashboard
- Export functionality for reports
