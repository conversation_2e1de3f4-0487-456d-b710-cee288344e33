
services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.prod
      # Uncomment the line below to use Alpine version instead of distroless
      # dockerfile: Dockerfile.prod.alpine
      args:
        NEXT_PUBLIC_API_BASE_URL: https://smarttrack-api-dev.acisio.com/api/v1
        NEXT_PUBLIC_API_TIMEOUT: 30000
        NEXT_PUBLIC_APP_NAME: "Smart Track Dashboard"
        NEXT_PUBLIC_APP_VERSION: "1.0.0"
        NEXT_PUBLIC_SESSION_TIMEOUT: 3600
        NEXT_PUBLIC_ENVIRONMENT: production
        NEXT_PUBLIC_DEBUG_MODE: false
    environment:
      - NODE_ENV=production
      - PORT=3000
      - NEXT_PUBLIC_API_BASE_URL=https://smarttrack-api-dev.acisio.com/api/v1
    ports:
      - "3000:3000"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  smart-track-network:
