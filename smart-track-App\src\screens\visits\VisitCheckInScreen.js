import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  Paragraph,
  Button,
  ActivityIndicator,
  Chip,
  Surface,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { visitService } from '../../services/visitService.js';
import locationService from '../../services/locationService.js';
import { useNavigation, useRoute } from '@react-navigation/native';

const VisitCheckInScreen = ({ navigation, route }) => {
  const { visitId } = route.params;
  const [visit, setVisit] = useState(null);
  const [loading, setLoading] = useState(true);
  const [checkingIn, setCheckingIn] = useState(false);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [locationValidation, setLocationValidation] = useState(null);

  useEffect(() => {
    loadVisitDetails();
    getCurrentLocation();
  }, []);

  const loadVisitDetails = async () => {
    try {
      const visitData = await visitService.getVisit(visitId);
      setVisit(visitData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load visit details');
      console.error('Load visit error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const location = await locationService.getCurrentLocation();
      setCurrentLocation(location);
      
      if (visit?.customer) {
        validateLocation(location);
      }
    } catch (error) {
      Alert.alert('Location Error', 'Unable to get current location. Please enable location services.');
      console.error('Location error:', error);
    }
  };

  const validateLocation = async (location) => {
    if (!visit?.customer?.latitude || !visit?.customer?.longitude) {
      setLocationValidation({
        isValid: false,
        distance: 0,
        message: 'Customer location not available for validation',
      });
      return;
    }

    try {
      const distance = locationService.calculateDistance(
        location.latitude,
        location.longitude,
        visit.customer.latitude,
        visit.customer.longitude
      );

      const isValid = distance <= 500; // 500 meters threshold
      
      setLocationValidation({
        isValid,
        distance,
        message: isValid 
          ? `You are ${Math.round(distance)}m from customer location`
          : `You are ${Math.round(distance)}m from customer location (too far)`,
      });
    } catch (error) {
      console.error('Location validation error:', error);
      setLocationValidation({
        isValid: false,
        distance: 0,
        message: 'Unable to validate location',
      });
    }
  };

  const handleCheckIn = async () => {
    if (!currentLocation) {
      Alert.alert('Error', 'Location not available. Please wait for location to load.');
      return;
    }

    if (locationValidation && !locationValidation.isValid) {
      Alert.alert(
        'Location Warning',
        'You are not at the customer location. Do you want to check in anyway?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Check In Anyway', onPress: performCheckIn },
        ]
      );
      return;
    }

    performCheckIn();
  };

  const performCheckIn = async () => {
    if (!currentLocation) return;

    try {
      setCheckingIn(true);
      
      const checkInData = {
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
      };

      await visitService.checkInVisit(visitId, checkInData);
      
      Alert.alert(
        'Check In Successful',
        'You have successfully checked in to this visit.',
        [
          {
            text: 'Continue',
            onPress: () => {
              navigation.replace('VisitDetail', { visitId });
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to check in. Please try again.');
      console.error('Check in error:', error);
    } finally {
      setCheckingIn(false);
    }
  };

  const getLocationStatusColor = () => {
    if (!locationValidation) return '#999';
    return locationValidation.isValid ? '#4CAF50' : '#F44336';
  };

  const getLocationStatusIcon = () => {
    if (!locationValidation) return 'location-searching';
    return locationValidation.isValid ? 'location-on' : 'location-off';
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Loading visit details...</Text>
      </View>
    );
  }

  if (!visit) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Visit not found</Text>
        <Button onPress={() => navigation.goBack()}>Go Back</Button>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
      {/* Visit Info Card */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.customerName}>
            {visit.customer?.name || 'Unknown Customer'}
          </Title>
          <Paragraph style={styles.customerAddress}>
            {visit.customer?.address ? 
              `${visit.customer.address}, ${visit.customer.city}` : 
              visit.customer?.city || 'Address not available'
            }
          </Paragraph>
          {visit.purpose && (
            <Paragraph style={styles.purpose}>{visit.purpose}</Paragraph>
          )}
        </Card.Content>
      </Card>

      {/* Location Status Card */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.cardTitle}>Location Status</Title>
          
          <View style={styles.locationStatus}>
            <MaterialIcons 
              name={getLocationStatusIcon()} 
              size={24} 
              color={getLocationStatusColor()} 
            />
            <View style={styles.locationInfo}>
              <Text style={[styles.locationText, { color: getLocationStatusColor() }]}>
                {locationValidation?.message || 'Getting location...'}
              </Text>
              {currentLocation && (
                <Text style={styles.coordinatesText}>
                  {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
                </Text>
              )}
            </View>
          </View>

          {currentLocation && currentLocation.accuracy && (
            <View style={styles.accuracyInfo}>
              <Text style={styles.accuracyText}>
                Accuracy: {locationService.getLocationAccuracyDescription(currentLocation.accuracy)}
                {' '}(±{Math.round(currentLocation.accuracy)}m)
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Check In Instructions */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.cardTitle}>Check In Instructions</Title>
          
          <View style={styles.instructionItem}>
            <MaterialIcons name="location-on" size={20} color="#2196F3" />
            <Text style={styles.instructionText}>
              Ensure you are at the customer location
            </Text>
          </View>

          <View style={styles.instructionItem}>
            <MaterialIcons name="signal-cellular-4-bar" size={20} color="#2196F3" />
            <Text style={styles.instructionText}>
              Check your internet connection
            </Text>
          </View>

          <View style={styles.instructionItem}>
            <MaterialIcons name="schedule" size={20} color="#2196F3" />
            <Text style={styles.instructionText}>
              Your check-in time will be recorded
            </Text>
          </View>
        </Card.Content>
      </Card>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <Button
          mode="contained"
          onPress={handleCheckIn}
          loading={checkingIn}
          disabled={checkingIn || !currentLocation}
          style={styles.checkInButton}
        >
          {checkingIn ? 'Checking In...' : 'Check In'}
        </Button>

        <Button
          mode="outlined"
          onPress={() => navigation.goBack()}
          disabled={checkingIn}
          style={styles.cancelButton}
        >
          Cancel
        </Button>
      </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    marginBottom: 20,
    textAlign: 'center',
  },
  card: {
    margin: 16,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  customerName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  customerAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  purpose: {
    fontSize: 14,
    color: '#555',
    fontStyle: 'italic',
  },
  locationStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationInfo: {
    marginLeft: 12,
    flex: 1,
  },
  locationText: {
    fontSize: 16,
    fontWeight: '500',
  },
  coordinatesText: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  accuracyInfo: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
  },
  accuracyText: {
    fontSize: 12,
    color: '#666',
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  instructionText: {
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
  },
  actionContainer: {
    padding: 16,
    gap: 12,
  },
  checkInButton: {
    backgroundColor: '#4CAF50',
  },
  cancelButton: {
    borderColor: '#666',
  },
});

export default VisitCheckInScreen;
