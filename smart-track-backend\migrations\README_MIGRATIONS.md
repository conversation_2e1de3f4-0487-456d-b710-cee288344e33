# Smart Track Database Migrations

This directory contains Alembic migrations for the Smart Track application database schema.

## Migration Order and Dependencies

The migrations have been carefully ordered to avoid foreign key constraint conflicts:

### 1. Migration 001 - Base Tables
**File:** `001_create_base_tables.py`
**Tables Created:**
- `roles` - User roles (manager, supervisor, sales_rep, etc.)
- `permissions` - System permissions
- `distribution_channels` - Business distribution channels

**Dependencies:** None (base tables)

### 2. Migration 002 - Users and Customers
**File:** `002_create_users_customers.py`
**Tables Created:**
- `users` - System users with authentication info
- `role_permissions` - Many-to-many relationship between roles and permissions
- `customers` - Customer information and GPS coordinates

**Dependencies:** Requires `roles` and `permissions` tables

### 3. Migration 003 - Profiles and Contacts
**File:** `003_create_profiles_contacts.py`
**Tables Created:**
- `user_profiles` - Extended user information and hierarchy
- `customer_contacts` - Customer contact persons
- `branches` - Customer branch locations

**Dependencies:** Requires `users`, `customers`, and `distribution_channels` tables

### 4. Migration 004 - Customer Distribution Channels
**File:** `004_create_customer_distribution_channels.py`
**Tables Created:**
- `customer_distribution_channels` - Relationship between customers, distribution channels, and sales reps

**Dependencies:** Requires `customers`, `distribution_channels`, and `users` tables

### 5. Migration 005 - Visits
**File:** `005_create_visits.py`
**Tables Created:**
- `visits` - Sales visits with GPS tracking and status

**Dependencies:** Requires `users` and `customers` tables

### 6. Migration 006 - Visit Photos and Notes
**File:** `006_create_visit_photos_notes.py`
**Tables Created:**
- `visit_photos` - Photos taken during visits
- `visit_notes` - Notes and observations from visits

**Dependencies:** Requires `visits` table

## Running Migrations

## Database Configuration

### Configuration Source
The database URL is now read from the application configuration (`app/core/config.py`) instead of being hardcoded in `alembic.ini`. This ensures consistency between the application and migration database connections.

**Configuration Hierarchy:**
1. Environment variables (highest priority)
2. `.env` file
3. Default values in `config.py` (lowest priority)

### Setting Database URL

**Option 1: Environment Variable**
```bash
export DATABASE_URL="postgresql://username:password@localhost:5432/smart_track_db"
```

**Option 2: .env File**
Create a `.env` file in the backend directory:
```env
DATABASE_URL=postgresql://username:password@localhost:5432/smart_track_db
```

**Option 3: Direct Configuration**
Modify `DATABASE_URL` in `app/core/config.py` (not recommended for production)

### Prerequisites
1. Ensure you have Python environment activated
2. Install required dependencies: `pip install alembic sqlalchemy`
3. Configure your database URL using one of the methods above
4. Test configuration: `python test_config.py`

### Option 1: Run All Migrations (Recommended)
Use the provided scripts to run all migrations in the correct order:

**Windows:**
```cmd
run_migrations.bat
```

**Linux/macOS:**
```bash
chmod +x run_migrations.sh
./run_migrations.sh
```

### Option 2: Manual Migration
Run migrations manually in order:

```bash
# Navigate to backend directory
cd smart-track-backend

# Run each migration in order
alembic upgrade 001  # Base tables
alembic upgrade 002  # Users and customers
alembic upgrade 003  # Profiles and contacts
alembic upgrade 004  # Customer distribution channels
alembic upgrade 005  # Visits
alembic upgrade 006  # Visit photos and notes

# Or upgrade to latest
alembic upgrade head
```

## Configuration Verification

Test your database configuration before running migrations:
```bash
python test_config.py
```

This will verify:
- Database connection using config.py settings
- Alembic can properly read the configuration
- All models can be imported correctly

## Verification Commands

Check migration status:
```bash
alembic current
```

View migration history:
```bash
alembic history
```

Show current database schema:
```bash
alembic show current
```

## Database Schema Overview

### Core Tables Structure:
```
roles (id, name, description)
├── users (role_id -> roles.id)
│   ├── user_profiles (user_id -> users.id, supervisor_id -> users.id)
│   └── visits (user_id -> users.id)
│       ├── visit_photos (visit_id -> visits.id)
│       └── visit_notes (visit_id -> visits.id)
├── permissions (id, name, resource, action)
│   └── role_permissions (role_id -> roles.id, permission_id -> permissions.id)
└── distribution_channels (id, name, manager_id -> users.id)
    ├── user_profiles (distribution_channel_id -> distribution_channels.id)
    └── customer_distribution_channels (distribution_channel_id -> distribution_channels.id)

customers (id, code, name, location_data)
├── customer_contacts (customer_id -> customers.id)
├── branches (customer_id -> customers.id)
├── visits (customer_id -> customers.id)
└── customer_distribution_channels (customer_id -> customers.id, sales_rep_id -> users.id)
```

## Rollback Information

To rollback migrations, use:
```bash
alembic downgrade <revision_id>
```

For example, to rollback to migration 003:
```bash
alembic downgrade 003
```

To rollback all migrations:
```bash
alembic downgrade base
```

## Troubleshooting

### Common Issues:

1. **Foreign key constraint errors**
   - Ensure migrations are run in the correct order
   - Check that referenced tables exist before creating foreign keys

2. **Table already exists errors**
   - Check if database already has some tables
   - Use `alembic current` to see current migration state

3. **Database connection errors**
   - Verify database URL in `alembic.ini`
   - Ensure database server is running
   - Check database permissions

### Recovery Steps:

If migrations fail mid-process:
1. Check the error message for specific table/constraint issues
2. Use `alembic current` to see current state
3. Fix any data issues manually if needed
4. Continue with remaining migrations

## Environment Configuration

Make sure to update the database URL in `alembic.ini`:
```ini
sqlalchemy.url = postgresql://username:password@localhost/smart_track_db
```

Or use environment variables in your application configuration.

## Notes

- All timestamps use timezone-aware DateTime columns
- GPS coordinates are stored as Float columns (latitude, longitude)
- String lengths are optimized for expected data sizes
- Boolean columns have appropriate defaults
- Indexes are created on frequently queried columns
- Foreign key constraints ensure data integrity
