'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/stores/auth';
import { useTokenRefresh } from '@/hooks/useTokenRefresh';

export default function AuthProvider({ children }: { children: React.ReactNode }) {
  const { initializeAuth } = useAuthStore();
  
  // Use the token refresh hook for automatic token management
  useTokenRefresh();

  useEffect(() => {
    // Initialize authentication on app load
    initializeAuth();
  }, [initializeAuth]);

  return <>{children}</>;
}

// Named export for backward compatibility
export { AuthProvider };
