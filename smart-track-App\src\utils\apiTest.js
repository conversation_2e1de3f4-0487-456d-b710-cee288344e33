/**
 * API Connectivity Test Utility
 * Use this to test if the backend is accessible from the mobile app
 */

import { api } from '../services/api';

export const testApiConnectivity = async () => {
  const results = {
    baseConnection: false,
    authEndpoint: false,
    loginTest: false,
    error: null
  };

  try {
    // Test 1: Basic connectivity to root endpoint
    console.log('🔍 Testing basic API connectivity...');
    const rootResponse = await fetch(`${api.baseURL.replace('/api/v1', '')}`, {
      method: 'GET',
      timeout: 5000
    });
    
    if (rootResponse.ok) {
      results.baseConnection = true;
      console.log('✅ Basic API connectivity successful');
    }

    // Test 2: Test auth endpoint availability
    console.log('🔍 Testing auth endpoint...');
    try {
      // This should return 422 for missing credentials, which means endpoint exists
      await api.login('', '');
    } catch (error) {
      if (error.response && (error.response.status === 422 || error.response.status === 401)) {
        results.authEndpoint = true;
        console.log('✅ Auth endpoint is accessible');
      }
    }

    // Test 3: Test with valid demo credentials
    console.log('🔍 Testing login with demo credentials...');
    try {
      const loginResponse = await api.login('admin', 'admin123');
      if (loginResponse && loginResponse.access_token) {
        results.loginTest = true;
        console.log('✅ Login test successful');
      }
    } catch (error) {
      console.log('❌ Login test failed:', error.message);
    }

  } catch (error) {
    results.error = error.message;
    console.log('❌ API connectivity test failed:', error);
  }

  return results;
};

export const getBackendStatus = async () => {
  try {
    const response = await fetch(`${api.baseURL.replace('/api/v1', '')}`, {
      method: 'GET',
      timeout: 5000
    });
    
    if (response.ok) {
      const data = await response.json();
      return {
        status: 'online',
        version: data.version,
        message: data.message
      };
    }
  } catch (error) {
    return {
      status: 'offline',
      error: error.message
    };
  }
};
