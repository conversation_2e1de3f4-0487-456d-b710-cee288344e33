import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  Caption,
  Button,
  TextInput,
  Chip,
  Text,
  Subheading,
  Searchbar,
  SegmentedButtons,
  Divider,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { visitService } from '../services/visitService';

const OldPlansScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterDateRange, setFilterDateRange] = useState('last7days');
  const [plans, setPlans] = useState([]);
  const [filteredPlans, setFilteredPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fallback data for offline scenarios
  const fallbackPlans = [
    {
      id: 1,
      date: '2025-07-30',
      customerName: 'ABC Supermarket',
      customerAddress: '123 Main St, Downtown',
      plannedTime: '09:00',
      actualTime: '09:15',
      visitType: 'Regular',
      status: 'Completed',
      notes: 'Successful visit, placed large order for snacks',
      orderValue: 1250.00,
      visitDuration: 45,
      photos: 3,
    },
    {
      id: 2,
      date: '2025-07-29',
      customerName: 'XYZ Grocery Store',
      customerAddress: '456 Oak Ave, Midtown',
      plannedTime: '11:30',
      actualTime: null,
      visitType: 'Follow-up',
      status: 'Missed',
      notes: 'Customer was closed unexpectedly',
      orderValue: 0,
      visitDuration: 0,
      photos: 0,
    },
    {
      id: 3,
      date: '2025-07-28',
      customerName: 'Corner Mart',
      customerAddress: '789 Pine St, Eastside',
      plannedTime: '14:00',
      actualTime: '14:30',
      visitType: 'New Customer',
      status: 'Completed',
      notes: 'Great first meeting, established partnership',
      orderValue: 800.00,
      visitDuration: 60,
      photos: 2,
    },
    {
      id: 4,
      date: '2025-07-27',
      customerName: 'Fresh Market',
      customerAddress: '321 Elm St, Westside',
      plannedTime: '10:00',
      actualTime: '10:05',
      visitType: 'Regular',
      status: 'Partially Completed',
      notes: 'Visit cut short due to emergency, need follow-up',
      orderValue: 350.00,
      visitDuration: 20,
      photos: 1,
    },
    {
      id: 5,
      date: '2025-07-26',
      customerName: 'City Convenience',
      customerAddress: '654 Maple Ave, Downtown',
      plannedTime: '16:00',
      actualTime: '16:10',
      visitType: 'Follow-up',
      status: 'Completed',
      notes: 'Resolved previous issues, renewed contract',
      orderValue: 950.00,
      visitDuration: 35,
      photos: 4,
    },
  ];

  const statusOptions = [
    { value: 'all', label: 'All' },
    { value: 'completed', label: 'Completed' },
    { value: 'missed', label: 'Missed' },
    { value: 'partially', label: 'Partial' },
  ];

  const dateRangeOptions = [
    { value: 'last7days', label: 'Last 7 Days' },
    { value: 'last30days', label: 'Last 30 Days' },
    { value: 'last90days', label: 'Last 90 Days' },
  ];

  useEffect(() => {
    loadHistoricalPlans();
  }, [filterDateRange]);

  useEffect(() => {
    applyFilters();
  }, [searchQuery, filterStatus, plans]);

  const loadHistoricalPlans = async () => {
    try {
      setLoading(true);
      setError(null);

      // Calculate date range based on filter
      const today = new Date();
      let startDate;

      switch (filterDateRange) {
        case 'last7days':
          startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'last30days':
          startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case 'last90days':
          startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      const params = {
        start_date: startDate.toISOString().split('T')[0],
        end_date: today.toISOString().split('T')[0],
        status: 'completed,cancelled,missed', // Only historical visits
      };

      const response = await visitService.getVisits(params);
      
      if (response && Array.isArray(response)) {
        // Transform API response to match expected format
        const transformedPlans = response.map(visit => ({
          id: visit.id,
          date: visit.planned_date,
          customerName: visit.customer?.name || 'Unknown Customer',
          customerAddress: visit.customer?.address || 'Unknown Address',
          plannedTime: visit.planned_time || 'TBD',
          actualTime: visit.actual_checkin_time || visit.checkin_time || null,
          visitType: visit.visit_type || 'Regular',
          status: capitalizeStatus(visit.status),
          notes: visit.notes || 'No notes available',
          orderValue: visit.order_value || 0,
          visitDuration: calculateDuration(visit.checkin_time, visit.checkout_time),
          photos: visit.photo_count || 0,
        }));

        setPlans(transformedPlans);
      } else {
        console.warn('No historical plans data available from API');
        setError('Unable to load historical data from server');
        setPlans([]);
      }
    } catch (error) {
      console.error('Failed to load historical plans:', error);
      setError(error.message);
      setPlans([]);
    } finally {
      setLoading(false);
    }
  };

  const capitalizeStatus = (status) => {
    if (!status) return 'Unknown';
    return status.replace('_', ' ').split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const calculateDuration = (checkin, checkout) => {
    if (!checkin || !checkout) return 0;
    const checkinTime = new Date(checkin);
    const checkoutTime = new Date(checkout);
    return Math.round((checkoutTime - checkinTime) / (1000 * 60)); // minutes
  };

  const applyFilters = () => {
    let filtered = [...plans];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(plan =>
        plan.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        plan.customerAddress.toLowerCase().includes(searchQuery.toLowerCase()) ||
        plan.notes.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(plan => {
        switch (filterStatus) {
          case 'completed':
            return plan.status === 'Completed';
          case 'missed':
            return plan.status === 'Missed';
          case 'partially':
            return plan.status === 'Partially Completed';
          default:
            return true;
        }
      });
    }

    // Apply date range filter
    const today = new Date();
    const daysBack = filterDateRange === 'last7days' ? 7 : 
                    filterDateRange === 'last30days' ? 30 : 90;
    const cutoffDate = new Date(today.getTime() - (daysBack * 24 * 60 * 60 * 1000));

    filtered = filtered.filter(plan => {
      const planDate = new Date(plan.date);
      return planDate >= cutoffDate;
    });

    setFilteredPlans(filtered);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed':
        return { backgroundColor: '#e8f5e8', color: '#2e7d32' };
      case 'Missed':
        return { backgroundColor: '#ffebee', color: '#c62828' };
      case 'Partially Completed':
        return { backgroundColor: '#fff3e0', color: '#ef6c00' };
      default:
        return { backgroundColor: '#f5f5f5', color: '#757575' };
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Completed':
        return 'check-circle';
      case 'Missed':
        return 'close-circle';
      case 'Partially Completed':
        return 'clock-alert';
      default:
        return 'help-circle';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderPlanItem = ({ item }) => {
    const statusStyle = getStatusColor(item.status);
    const statusIcon = getStatusIcon(item.status);

    return (
      <Card style={styles.planCard}>
        <Card.Content>
          <View style={styles.planHeader}>
            <View style={styles.planInfo}>
              <Title style={styles.customerName}>{item.customerName}</Title>
              <Caption style={styles.planDate}>{formatDate(item.date)}</Caption>
            </View>
            <Chip
              icon={statusIcon}
              style={[styles.statusChip, { backgroundColor: statusStyle.backgroundColor }]}
              textStyle={{ color: statusStyle.color }}
            >
              {item.status}
            </Chip>
          </View>

          <Text style={styles.customerAddress}>{item.customerAddress}</Text>

          <View style={styles.planDetails}>
            <View style={styles.detailRow}>
              <MaterialCommunityIcons name="clock-outline" size={16} color="#666" />
              <Text style={styles.detailText}>
                Planned: {item.plannedTime}
                {item.actualTime && ` | Actual: ${item.actualTime}`}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <MaterialCommunityIcons name="tag-outline" size={16} color="#666" />
              <Text style={styles.detailText}>{item.visitType}</Text>
            </View>

            {item.visitDuration > 0 && (
              <View style={styles.detailRow}>
                <MaterialCommunityIcons name="timer-outline" size={16} color="#666" />
                <Text style={styles.detailText}>{item.visitDuration} minutes</Text>
              </View>
            )}
          </View>

          <View style={styles.planMetrics}>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>{formatCurrency(item.orderValue)}</Text>
              <Caption style={styles.metricLabel}>Order Value</Caption>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>{item.photos}</Text>
              <Caption style={styles.metricLabel}>Photos</Caption>
            </View>
          </View>

          {item.notes && (
            <View style={styles.notesContainer}>
              <Text style={styles.notesLabel}>Notes:</Text>
              <Text style={styles.notesText}>{item.notes}</Text>
            </View>
          )}
        </Card.Content>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <MaterialCommunityIcons name="calendar-search" size={60} color="#ccc" />
      <Title style={styles.emptyTitle}>No plans found</Title>
      <Caption style={styles.emptySubtitle}>
        Try adjusting your search criteria or date range
      </Caption>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Title style={styles.headerTitle}>Old Plans</Title>
        <Caption style={styles.headerSubtitle}>
          View and analyze past visits
        </Caption>
        {error && (
          <Text style={styles.errorText}>
            {error} - Using offline data
          </Text>
        )}
      </View>

      {/* Search and Filters */}
      <View style={styles.filtersContainer}>
        <Searchbar
          placeholder="Search by customer, address, or notes..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />

        <View style={styles.filterRow}>
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>Status</Text>
            <SegmentedButtons
              value={filterStatus}
              onValueChange={setFilterStatus}
              buttons={statusOptions}
              style={styles.segmentedButtons}
            />
          </View>
        </View>

        <View style={styles.filterRow}>
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>Date Range</Text>
            <SegmentedButtons
              value={filterDateRange}
              onValueChange={setFilterDateRange}
              buttons={dateRangeOptions}
              style={styles.segmentedButtons}
            />
          </View>
        </View>
      </View>

      <Divider />

      {/* Results Summary */}
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryText}>
          {filteredPlans.length} {filteredPlans.length === 1 ? 'plan' : 'plans'} found
        </Text>
        <Button
          mode="text"
          onPress={() => {
            setSearchQuery('');
            setFilterStatus('all');
            setFilterDateRange('last7days');
          }}
          compact
        >
          Clear Filters
        </Button>
      </View>

      {/* Plans List */}
      <FlatList
        data={filteredPlans}
        renderItem={renderPlanItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.plansList}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  errorText: {
    fontSize: 12,
    color: '#f44336',
    marginTop: 4,
    fontStyle: 'italic',
  },
  filtersContainer: {
    backgroundColor: '#fff',
    padding: 16,
  },
  searchBar: {
    marginBottom: 16,
    elevation: 0,
    backgroundColor: '#f9f9f9',
  },
  filterRow: {
    marginBottom: 16,
  },
  filterSection: {
    flex: 1,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  segmentedButtons: {
    marginBottom: 8,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  summaryText: {
    fontSize: 14,
    color: '#666',
  },
  plansList: {
    padding: 16,
  },
  planCard: {
    marginBottom: 16,
    elevation: 2,
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  planInfo: {
    flex: 1,
  },
  customerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  planDate: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  statusChip: {
    marginLeft: 12,
  },
  customerAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  planDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  planMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginBottom: 12,
  },
  metricItem: {
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2196f3',
  },
  metricLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  notesContainer: {
    marginTop: 8,
    padding: 12,
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196f3',
  },
  notesLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#2196f3',
    marginBottom: 4,
  },
  notesText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyTitle: {
    marginTop: 16,
    color: '#666',
  },
  emptySubtitle: {
    marginTop: 8,
    textAlign: 'center',
    color: '#999',
  },
});

export default OldPlansScreen;
