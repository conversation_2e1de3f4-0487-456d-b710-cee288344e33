# Use multi-stage build to reduce final image size
FROM ghcr.io/astral-sh/uv:latest AS uv

FROM python:3.13-alpine3.21 AS builder

# Set environment variables
ENV USER=fastapi \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    UV_PROJECT_ENVIRONMENT=/usr/local

# Install necessary packages and create non root user
RUN apk add --no-cache curl netcat-openbsd postgresql-client && \
    adduser -D -s /bin/sh $USER

# Copy uv from the previous stage
COPY --from=uv /uv /uvx /bin/

# Set working directory
ENV APP_DIR=/home/<USER>/src
WORKDIR $APP_DIR

# Copy dependency files
COPY ./pyproject.toml ./uv.lock ./alembic.ini ./startup.sh ${APP_DIR}/

# Create uploads directory
RUN mkdir -p uploads

# Install dependencies using uv cache
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-install-project

# Copy application code
COPY ./migrations ${APP_DIR}/migrations
COPY ./app ${APP_DIR}/app

# Set Python path
ENV PYTHONPATH=${APP_DIR}

# Change ownership of the application directory and make startup script executable
RUN chown -R "$USER:$USER" $APP_DIR && \
    chmod +x $APP_DIR/startup.sh

# Switch to the non-root user
USER $USER

# Expose port
EXPOSE 8000

# Run the application
CMD ["/home/<USER>/src/startup.sh"]