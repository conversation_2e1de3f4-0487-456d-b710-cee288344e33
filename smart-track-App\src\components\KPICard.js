import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Title, Text, ProgressBar } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';

const KPICard = ({
  title,
  value,
  subtitle,
  icon,
  color = '#2196F3',
  backgroundColor = '#E3F2FD',
  progress,
  onPress,
}) => {
  return (
    <Card style={[styles.card, { backgroundColor }]} onPress={onPress}>
      <Card.Content style={styles.content}>
        {icon && (
          <Ionicons name={icon} size={24} color={color} style={styles.icon} />
        )}
        
        <Text style={[styles.value, { color }]}>{value}</Text>
        <Text style={styles.title}>{title}</Text>
        
        {subtitle && (
          <Text style={styles.subtitle}>{subtitle}</Text>
        )}
        
        {progress !== undefined && (
          <ProgressBar
            progress={progress}
            color={color}
            style={styles.progressBar}
          />
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    elevation: 3,
    borderRadius: 12,
    marginBottom: 15,
  },
  content: {
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  icon: {
    marginBottom: 8,
  },
  value: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  title: {
    fontSize: 12,
    textAlign: 'center',
    color: '#666',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 10,
    textAlign: 'center',
    color: '#999',
    marginBottom: 8,
  },
  progressBar: {
    width: '100%',
    height: 4,
    borderRadius: 2,
  },
});

export default KPICard;
