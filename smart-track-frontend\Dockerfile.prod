# syntax=docker.io/docker/dockerfile:1

# =====================================================
# Production Dockerfile for Next.js Frontend
# Ultra-optimized for minimal image size
# =====================================================

# Base image with Node.js LTS (alpine variant) - smaller footprint
FROM node:22-alpine3.20 AS base

# Install only essential packages and clean up immediately
RUN apk update && apk upgrade && \
    apk add --no-cache \
    libc6-compat \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Install and configure pnpm globally
RUN corepack enable pnpm && \
    corepack prepare pnpm@latest --activate

# Set optimal Node.js flags for production builds
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# =====================================================
# Dependencies stage - install packages
# =====================================================
FROM base AS deps
WORKDIR /app

# Copy only package management files
COPY package.json pnpm-lock.yaml* ./

# Install dependencies with cache mount for faster builds
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm install --frozen-lockfile --prefer-offline

# =====================================================
# Builder stage - compile and build the application
# =====================================================
FROM base AS builder
WORKDIR /app

# Copy dependencies
COPY --from=deps /app/node_modules ./node_modules

# Copy source code (excluding unnecessary files via .dockerignore)
COPY . .

# Set production environment
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Build-time arguments (override with --build-arg)
ARG NEXT_PUBLIC_API_BASE_URL
ARG NEXT_PUBLIC_API_TIMEOUT
ARG NEXT_PUBLIC_APP_NAME
ARG NEXT_PUBLIC_APP_VERSION
ARG NEXT_PUBLIC_SESSION_TIMEOUT

# Expose them as env variables for Next.js build
ENV NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL
ENV NEXT_PUBLIC_API_TIMEOUT=$NEXT_PUBLIC_API_TIMEOUT
ENV NEXT_PUBLIC_APP_NAME=$NEXT_PUBLIC_APP_NAME
ENV NEXT_PUBLIC_APP_VERSION=$NEXT_PUBLIC_APP_VERSION
ENV NEXT_PUBLIC_SESSION_TIMEOUT=$NEXT_PUBLIC_SESSION_TIMEOUT
ENV NEXT_PUBLIC_ENVIRONMENT=production
ENV NEXT_PUBLIC_AUTO_REFRESH_TOKEN=true
ENV NEXT_PUBLIC_THEME=light
ENV NEXT_PUBLIC_DEBUG_MODE=false

# Build Next.js application
RUN pnpm run build

# Ensure public directory exists (Next.js might not create it if empty)
RUN mkdir -p /app/public

# =====================================================
# Production runtime stage - ultra minimal with distroless
# =====================================================
FROM gcr.io/distroless/nodejs22-debian12:nonroot AS runner

WORKDIR /app

# Environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Copy only the necessary files from builder
COPY --from=builder --chown=nonroot:nonroot /app/.next/standalone ./
COPY --from=builder --chown=nonroot:nonroot /app/.next/static ./.next/static

# Copy public folder (now guaranteed to exist)
COPY --from=builder --chown=nonroot:nonroot /app/public ./public

# Expose application port
EXPOSE 3000

# Start Next.js application
CMD ["server.js"]
