/* Global styles for Visit Tracking component */

/* Table Row Styles */
.ant-table-row-success {
  background-color: #f6ffed !important;
}

.ant-table-row-success:hover {
  background-color: #edfcdb !important;
}

.ant-table-row-processing {
  background-color: #e6f7ff !important;
}

.ant-table-row-processing:hover {
  background-color: #d4edff !important;
}

.ant-table-row-error {
  background-color: #fff2f0 !important;
}

.ant-table-row-error:hover {
  background-color: #ffe7e0 !important;
}

/* Success Message Styling */
.ant-message-success {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: white;
  border-radius: 8px;
}

/* Error Message Styling */
.ant-message-error {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: white;
  border-radius: 8px;
}

/* Info Message Styling */
.ant-message-info {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  border-radius: 8px;
}

/* Enhanced Table Styling */
.ant-table-thead > tr > th {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #e8e8e8;
}

.ant-table-tbody > tr:hover > td {
  background-color: rgba(24, 144, 255, 0.04) !important;
}

/* Enhanced Pagination */
.ant-pagination-item-active {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-color: #1890ff;
}

.ant-pagination-item-active a {
  color: white;
}

/* Enhanced Select Options */
.ant-select-dropdown {
  border-radius: 8px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* Enhanced Input Focus */
.ant-input:focus,
.ant-input-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Enhanced Button Hover */
.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* Enhanced Card Shadows */
.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}
