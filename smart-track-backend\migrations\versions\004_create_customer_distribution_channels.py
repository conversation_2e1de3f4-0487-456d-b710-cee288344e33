"""Create customer distribution channels table

Revision ID: 004
Revises: 003
Create Date: 2025-08-10 10:03:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create customer_distribution_channels table
    op.create_table('customer_distribution_channels',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('customer_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('distribution_channel_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('sales_rep_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('manager_code', sa.String(length=20), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.Column('assigned_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.id'], ),
    sa.ForeignKeyConstraint(['distribution_channel_id'], ['distribution_channels.id'], ),
    sa.ForeignKeyConstraint(['sales_rep_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customer_distribution_channels_id'), 'customer_distribution_channels', ['id'], unique=False)


def downgrade() -> None:
    op.drop_table('customer_distribution_channels')
