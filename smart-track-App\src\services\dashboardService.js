import { api } from './api';

class DashboardService {
  constructor() {
    this.api = api;
  }

  /**
   * Get dashboard data including KPIs, upcoming visits, and pending tasks
   */
  async getDashboardData() {
    try {
      console.log('📊 Fetching dashboard data from backend...');
      console.log('📊 API base URL:', this.api.baseURL);
      console.log('📊 Making request to: /dashboard/');
      
      // Verify we have authentication token
      const response = await this.api.get('/dashboard/');
      
      console.log('📊 Dashboard API response status:', response.status);
      console.log('📊 Dashboard API response data keys:', Object.keys(response.data || {}));
      
      if (response.status === 200 && response.data) {
        console.log('📊 Dashboard data fetched successfully');
        console.log('📊 Raw KPIs received:', JSON.stringify(response.data.kpis, null, 2));
        console.log('📊 Upcoming visits count:', response.data.upcoming_visits?.length || 0);
        console.log('📊 Pending tasks count:', response.data.pending_tasks?.length || 0);
        
        const transformedData = this.transformDashboardData(response.data);
        console.log('📊 Transformed KPIs:', JSON.stringify(transformedData.kpis, null, 2));
        
        return {
          success: true,
          data: transformedData
        };
      } else {
        console.warn('📊 Dashboard API returned unsuccessful response:', response);
        return {
          success: false,
          error: 'Failed to fetch dashboard data',
          data: null
        };
      }
    } catch (error) {
      console.error('📊 Dashboard service error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          baseURL: error.config?.baseURL,
          headers: error.config?.headers
        }
      });
      
      // More specific error handling
      if (error.response?.status === 500) {
        console.error('📊 Server error (500) - Backend issue detected');
      } else if (error.response?.status === 401) {
        console.error('📊 Authentication error (401) - Token might be expired');
      } else if (!error.response) {
        console.error('📊 Network error - Cannot reach backend');
      }
      
      return {
        success: false,
        error: error.response?.data?.detail || error.message || 'Network error fetching dashboard data',
        data: null
      };
    }
  }

  /**
   * Get visit statistics
   */
  async getVisitStatistics(dateFrom = null, dateTo = null) {
    try {
      const params = {};
      if (dateFrom) params.date_from = dateFrom;
      if (dateTo) params.date_to = dateTo;

      const response = await this.api.get('/visits/statistics', { params });
      
      if (response.success) {
        return {
          success: true,
          data: response.data
        };
      } else {
        return {
          success: false,
          error: response.error || 'Failed to fetch visit statistics'
        };
      }
    } catch (error) {
      console.error('Visit statistics error:', error);
      return {
        success: false,
        error: error.message || 'Network error fetching visit statistics'
      };
    }
  }



  /**
   * Transform backend data to match mobile app format
   */
  transformDashboardData(backendData) {
    if (!backendData) return null;

    // Helper function to safely convert to number and handle NaN
    const safeNumber = (value, defaultValue = 0) => {
      const num = Number(value);
      return isNaN(num) || !isFinite(num) ? defaultValue : num;
    };

    return {
      kpis: {
        completedVisits: safeNumber(backendData.kpis?.completed_visits, 0),
        targetVisits: safeNumber(backendData.kpis?.target_visits, 0),
        targetPercentage: safeNumber(backendData.kpis?.target_percentage, 0),
        monthlyTarget: safeNumber(backendData.kpis?.monthly_target, 0),
        revenue: safeNumber(backendData.kpis?.revenue, 0),
        revenueTarget: safeNumber(backendData.kpis?.revenue_target, 0),
        customersVisited: safeNumber(backendData.kpis?.clients_visited, 0),
        totalCustomers: safeNumber(backendData.kpis?.total_clients, 0),
      },
      upcomingVisits: (backendData.upcoming_visits || []).map(visit => ({
        id: visit.id,
        customer: visit.client || visit.customer || 'Unknown Customer',
        time: visit.time || '9:00 AM',
        location: visit.location || 'No location',
        type: visit.type || 'Scheduled',
        date: visit.date || 'TBD',
        priority: visit.priority || 'Medium',
        duration: visit.duration || '45 min',
        contact: visit.contact || 'No contact',
      })),
      pendingTasks: (backendData.pending_tasks || []).map(task => ({
        id: task.id || 'unknown',
        title: task.title || 'Untitled Task',
        priority: task.priority || 'Medium',
        dueDate: task.due_date || 'TBD',
        category: task.category || 'General',
        status: task.status || 'Pending',
      })),
    };
  }
}

export default new DashboardService();
