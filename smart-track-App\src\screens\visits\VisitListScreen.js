import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  Paragraph,
  Chip,
  FAB,
  Searchbar,
  Menu,
  Divider,
  IconButton,
} from 'react-native-paper';
import { format, parseISO, isToday, isTomorrow, isYesterday } from 'date-fns';
import { visitService } from '../../services/visitService.js';
import { useNavigation } from '@react-navigation/native';

const VisitListScreen = ({ navigation }) => {
  const [visits, setVisits] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);
  const [currentFilter, setCurrentFilter] = useState('all');

  const loadVisits = useCallback(async (filter) => {
    try {
      setLoading(true);
      const params = { limit: 50 };
      
      if (filter && filter !== 'all') {
        params.status = filter;
      }

      const data = await visitService.getVisits(params);
      setVisits(data);
    } catch (error) {
      Alert.alert('Error', 'Failed to load visits');
      console.error('Load visits error:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadVisits(currentFilter);
    setRefreshing(false);
  }, [loadVisits, currentFilter]);

  useEffect(() => {
    loadVisits(currentFilter);
  }, [loadVisits, currentFilter]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'planned':
        return '#2196F3';
      case 'in_progress':
        return '#FF9800';
      case 'completed':
        return '#4CAF50';
      case 'cancelled':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'planned':
        return 'Planned';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const formatDate = (dateString) => {
    const date = parseISO(dateString);
    
    if (isToday(date)) {
      return `Today, ${format(date, 'HH:mm')}`;
    } else if (isTomorrow(date)) {
      return `Tomorrow, ${format(date, 'HH:mm')}`;
    } else if (isYesterday(date)) {
      return `Yesterday, ${format(date, 'HH:mm')}`;
    } else {
      return format(date, 'MMM dd, HH:mm');
    }
  };

  const handleVisitPress = (visit) => {
    navigation.navigate('VisitDetail', { visitId: visit.id });
  };

  const handleCheckIn = async (visit) => {
    if (visit.status !== 'planned') {
      Alert.alert('Error', 'Can only check in to planned visits');
      return;
    }
    
    navigation.navigate('VisitCheckIn', { visitId: visit.id });
  };

  const filteredVisits = visits.filter(visit =>
    visit.customer?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    visit.purpose?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderVisitItem = ({ item }) => (
    <Card style={styles.visitCard} onPress={() => handleVisitPress(item)}>
      <Card.Content>
        <View style={styles.visitHeader}>
          <View style={styles.visitInfo}>
            <Title style={styles.customerName}>
              {item.customer?.name || 'Unknown Customer'}
            </Title>
            <Paragraph style={styles.visitTime}>
              {formatDate(item.planned_date)}
            </Paragraph>
          </View>
          <Chip
            mode="outlined"
            style={[styles.statusChip, { borderColor: getStatusColor(item.status) }]}
            textStyle={{ color: getStatusColor(item.status) }}
          >
            {getStatusLabel(item.status)}
          </Chip>
        </View>
        
        {item.purpose && (
          <Paragraph style={styles.purpose}>{item.purpose}</Paragraph>
        )}

        <View style={styles.visitActions}>
          {item.status === 'planned' && (
            <TouchableOpacity
              style={[styles.actionButton, styles.checkInButton]}
              onPress={() => handleCheckIn(item)}
            >
              <Text style={styles.checkInButtonText}>Check In</Text>
            </TouchableOpacity>
          )}
          
          {item.status === 'in_progress' && (
            <TouchableOpacity
              style={[styles.actionButton, styles.checkOutButton]}
              onPress={() => navigation.navigate('VisitCheckOut', { visitId: item.id })}
            >
              <Text style={styles.checkOutButtonText}>Check Out</Text>
            </TouchableOpacity>
          )}
          
          <IconButton
            icon="chevron-right"
            size={20}
            onPress={() => handleVisitPress(item)}
          />
        </View>
      </Card.Content>
    </Card>
  );

  const filterMenuItems = [
    { title: 'All Visits', value: 'all' },
    { title: 'Planned', value: 'planned' },
    { title: 'In Progress', value: 'in_progress' },
    { title: 'Completed', value: 'completed' },
    { title: 'Cancelled', value: 'cancelled' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search visits..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />
        <IconButton
          icon="clock-alert"
          size={24}
          mode="contained"
          iconColor="white"
          style={styles.pendingButton}
          onPress={() => navigation.navigate('PendingVisits')}
        />
        <Menu
          visible={filterMenuVisible}
          onDismiss={() => setFilterMenuVisible(false)}
          anchor={
            <IconButton
              icon="filter-variant"
              size={24}
              onPress={() => setFilterMenuVisible(true)}
            />
          }
        >
          {filterMenuItems.map((item, index) => (
            <React.Fragment key={item.value}>
              <Menu.Item
                onPress={() => {
                  setCurrentFilter(item.value);
                  setFilterMenuVisible(false);
                }}
                title={item.title}
                titleStyle={
                  currentFilter === item.value ? styles.activeFilterText : undefined
                }
              />
              {index < filterMenuItems.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </Menu>
      </View>

      <FlatList
        data={filteredVisits}
        renderItem={renderVisitItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => navigation.navigate('CreateVisit')}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'white',
    elevation: 2,
  },
  searchbar: {
    flex: 1,
    marginRight: 8,
  },
  pendingButton: {
    backgroundColor: '#FF9800',
    marginRight: 4,
  },
  listContainer: {
    padding: 16,
  },
  visitCard: {
    marginBottom: 12,
    elevation: 2,
  },
  visitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  visitInfo: {
    flex: 1,
    marginRight: 12,
  },
  customerName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  visitTime: {
    fontSize: 14,
    color: '#666',
  },
  statusChip: {
    height: 32,
  },
  purpose: {
    fontSize: 14,
    color: '#555',
    marginBottom: 12,
    fontStyle: 'italic',
  },
  visitActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 100,
    alignItems: 'center',
  },
  checkInButton: {
    backgroundColor: '#4CAF50',
  },
  checkInButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  checkOutButton: {
    backgroundColor: '#FF9800',
  },
  checkOutButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#2196F3',
  },
  activeFilterText: {
    fontWeight: 'bold',
    color: '#2196F3',
  },
});

export default VisitListScreen;
