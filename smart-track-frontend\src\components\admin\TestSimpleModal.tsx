'use client';

interface TestSimpleModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function TestSimpleModal({ isOpen, onClose }: TestSimpleModalProps) {
  console.log('🧪 TestSimpleModal CALLED - isOpen:', isOpen);
  
  if (!isOpen) {
    console.log('🚫 TestSimpleModal not open');
    return null;
  }
  
  console.log('✅ TestSimpleModal WILL RENDER');
  
  return (
    <div className="fixed inset-0 bg-red-500 bg-opacity-50 flex items-center justify-center z-[9999]">
      <div className="bg-white rounded-lg p-6 w-96">
        <h2 className="text-xl font-bold mb-4">🧪 TEST MODAL WORKS!</h2>
        <p className="mb-4">If you see this, the modal system is working.</p>
        <button 
          onClick={onClose}
          className="px-4 py-2 bg-red-500 text-white rounded"
        >
          Close Test Modal
        </button>
      </div>
    </div>
  );
}
