import { create } from 'zustand';
import { User, LoginRequest } from '../types';
import { apiService } from '../services/api';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isInitializing: boolean;
  error: string | null;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  getCurrentUser: () => Promise<void>;
  clearError: () => void;
  initializeAuth: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
}

// Helper functions for local storage with expiration
const setTokenWithExpiry = (key: string, value: string, expiryMinutes: number = 60) => {
  if (typeof window === 'undefined') return;
  
  const now = new Date();
  const expiryTime = now.getTime() + (expiryMinutes * 60 * 1000);
  
  const tokenData = {
    value,
    expiry: expiryTime
  };
  
  localStorage.setItem(key, JSON.stringify(tokenData));
};

const getTokenWithExpiry = (key: string): string | null => {
  if (typeof window === 'undefined') return null;
  
  const itemStr = localStorage.getItem(key);
  if (!itemStr) return null;
  
  try {
    const item = JSON.parse(itemStr);
    const now = new Date();
    
    if (now.getTime() > item.expiry) {
      localStorage.removeItem(key);
      return null;
    }
    
    return item.value;
  } catch (error) {
    localStorage.removeItem(key);
    return null;
  }
};

const clearAllAuthData = () => {
  if (typeof window === 'undefined') return;
  
  // Clear all auth-related data from localStorage
  const authKeys = ['access_token', 'refresh_token', 'user_data', 'login_timestamp'];
  authKeys.forEach(key => localStorage.removeItem(key));
  
  // Clear any other cached data
  const allKeys = Object.keys(localStorage);
  allKeys.forEach(key => {
    if (key.startsWith('auth_') || key.startsWith('smart_track_')) {
      localStorage.removeItem(key);
    }
  });
};

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,
  isInitializing: true,
  error: null,

  login: async (credentials: LoginRequest) => {
    set({ isLoading: true, error: null });
    try {
      const response = await apiService.login(credentials);
      const { access_token, refresh_token, user } = response.data;
      
      if (typeof window !== 'undefined') {
        // Store tokens with expiration (access token expires in 60 minutes, refresh token in 7 days)
        setTokenWithExpiry('access_token', access_token, 60);
        setTokenWithExpiry('refresh_token', refresh_token, 7 * 24 * 60);
        
        // Store user data and login timestamp
        localStorage.setItem('user_data', JSON.stringify(user));
        localStorage.setItem('login_timestamp', Date.now().toString());
      }
      
      set({ 
        user, 
        isAuthenticated: true, 
        isLoading: false,
        error: null 
      });
    } catch (error: any) {
      const message = error.response?.data?.detail || 'Login failed';
      set({ 
        error: message, 
        isLoading: false,
        isAuthenticated: false,
        user: null 
      });
      throw error;
    }
  },

  logout: () => {
    // Clear all authentication data and cache
    clearAllAuthData();
    
    set({ 
      user: null, 
      isAuthenticated: false, 
      error: null,
      isLoading: false,
      isInitializing: false
    });
  },

  getCurrentUser: async () => {
    if (typeof window === 'undefined') return;
    
    const token = getTokenWithExpiry('access_token');
    if (!token) {
      set({ isAuthenticated: false, user: null, isLoading: false, isInitializing: false });
      return;
    }

    // Only set loading if we're not initializing (to avoid interfering with login page)
    const currentState = get();
    if (!currentState.isInitializing) {
      set({ isLoading: true });
    }
    
    try {
      const response = await apiService.getCurrentUser();
      const user = response.data;
      
      // Update stored user data
      localStorage.setItem('user_data', JSON.stringify(user));
      
      set({ 
        user, 
        isAuthenticated: true, 
        isLoading: false,
        isInitializing: false,
        error: null 
      });
    } catch (error) {
      // Token might be invalid or expired, try to refresh
      const refreshSuccess = await get().refreshToken();
      if (!refreshSuccess) {
        clearAllAuthData();
        set({ 
          user: null, 
          isAuthenticated: false, 
          isLoading: false,
          isInitializing: false,
          error: 'Session expired' 
        });
      }
    }
  },

  initializeAuth: async () => {
    if (typeof window === 'undefined') {
      set({ isInitializing: false });
      return;
    }
    
    try {
      // Check if we have a valid access token
      const accessToken = getTokenWithExpiry('access_token');
      const refreshToken = getTokenWithExpiry('refresh_token');
      const storedUser = localStorage.getItem('user_data');
      
      if (accessToken && storedUser) {
        // We have valid tokens and user data, restore session
        const user = JSON.parse(storedUser);
        set({ 
          user, 
          isAuthenticated: true, 
          isInitializing: false,
          error: null 
        });
        
        // Verify the session is still valid
        get().getCurrentUser();
      } else if (refreshToken) {
        // Access token expired but refresh token is valid, try to refresh
        const refreshSuccess = await get().refreshToken();
        if (!refreshSuccess) {
          clearAllAuthData();
          set({ 
            user: null, 
            isAuthenticated: false, 
            isInitializing: false 
          });
        }
      } else {
        // No valid tokens, user needs to login
        clearAllAuthData();
        set({ 
          user: null, 
          isAuthenticated: false, 
          isInitializing: false 
        });
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      clearAllAuthData();
      set({ 
        user: null, 
        isAuthenticated: false, 
        isInitializing: false,
        error: 'Failed to initialize authentication' 
      });
    }
  },

  refreshToken: async (): Promise<boolean> => {
    if (typeof window === 'undefined') return false;
    
    const refreshToken = getTokenWithExpiry('refresh_token');
    if (!refreshToken) return false;
    
    try {
      const response = await apiService.refreshToken(refreshToken);
      const { access_token, refresh_token: newRefreshToken, user } = response.data;
      
      // Store new tokens
      setTokenWithExpiry('access_token', access_token, 60);
      if (newRefreshToken) {
        setTokenWithExpiry('refresh_token', newRefreshToken, 7 * 24 * 60);
      }
      
      // Update user data
      localStorage.setItem('user_data', JSON.stringify(user));
      
      set({ 
        user, 
        isAuthenticated: true, 
        isLoading: false,
        isInitializing: false,
        error: null 
      });
      
      return true;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  },

  clearError: () => set({ error: null }),
}));
