import {create} from 'zustand';
import {persist, createJSONStorage} from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const useAuthStore = create()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      user: null,
      accessToken: null,
      refreshToken: null,
      _hasHydrated: false,

      setHasHydrated: (hasHydrated) => {
        set({
          _hasHydrated: hasHydrated,
        });
      },

      login: async (loginData) => {
        console.log('🔐 Login called with data:', {
          hasUser: !!loginData.user,
          hasAccessToken: !!loginData.access_token,
          hasRefreshToken: !!loginData.refresh_token
        });
        
        // Store tokens in AsyncStorage
        await AsyncStorage.setItem('access_token', loginData.access_token);
        await AsyncStorage.setItem('refresh_token', loginData.refresh_token);

        set({
          isAuthenticated: true,
          user: loginData.user,
          accessToken: loginData.access_token,
          refreshToken: loginData.refresh_token,
        });
        
        console.log('✅ Auth state updated - isAuthenticated: true');
      },

      logout: async () => {
        // Clear tokens from AsyncStorage
        await AsyncStorage.removeItem('access_token');
        await AsyncStorage.removeItem('refresh_token');

        set({
          isAuthenticated: false,
          user: null,
          accessToken: null,
          refreshToken: null,
        });
      },

      updateTokens: async (accessToken, refreshToken) => {
        // Update tokens in AsyncStorage
        await AsyncStorage.setItem('access_token', accessToken);
        await AsyncStorage.setItem('refresh_token', refreshToken);

        set({
          accessToken,
          refreshToken,
        });
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
      }),
      onRehydrateStorage: () => (state, error) => {
        if (error) {
          console.log('❌ Hydration error:', error);
        } else {
          console.log('💧 Hydration complete:', state ? 'success' : 'no state');
        }
        // Always set hydrated to true, even if there's no persisted state
        useAuthStore.getState().setHasHydrated(true);
      },
    },
  ),
);
