/* Enhanced Supervisor Dashboard Styles */

.supervisorDashboard {
  min-height: 100vh;
}

.headerContainer {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 64px;
  line-height: 64px;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  line-height: normal;
}

.logoSection {
  display: flex;
  align-items: center;
  gap: 16px;
  height: 100%;
}

.logoIcon {
  font-size: 24px;
  color: white;
}

.titleSection h4 {
  margin: 0 !important;
  color: white !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
  display: flex !important;
  align-items: center !important;
}

.titleSection {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.subtitle {
  color: rgba(255,255,255,0.8);
  font-size: 12px;
  line-height: 1.2;
  margin-top: 2px;
}

.headerInfo {
  display: flex;
  align-items: center;
}

.welcomeText {
  color: white !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.mainTitle {
  margin: 0 !important;
  color: white !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
  height: auto !important;
}

.siderContainer {
  background: white;
  box-shadow: 2px 0 8px rgba(0,0,0,0.1);
  border-right: 1px solid #f0f0f0;
}

.userCard {
  margin: 16px;
  border-radius: 12px !important;
  background: linear-gradient(135deg, #f6f9ff 0%, #e6f7ff 100%) !important;
  border: 1px solid #d4edff !important;
}

.userCardBody {
  padding: 16px !important;
  text-align: center;
}

.userAvatar {
  background-color: #1890ff !important;
  margin-bottom: 12px;
  border: 3px solid white !important;
  box-shadow: 0 4px 12px rgba(24,144,255,0.3) !important;
}

.userCardName {
  margin: 0 !important;
  color: #1890ff !important;
}

.userCardEmail {
  font-size: 12px;
}

.menuContainer {
  border: none !important;
  background: transparent !important;
}

.menuItem {
  margin: 4px 16px !important;
  border-radius: 8px !important;
  height: 48px !important;
  line-height: 48px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.quickStats {
  margin: 16px;
  margin-top: auto;
}

.quickStatsCard {
  border-radius: 8px !important;
}

.quickStatsHeader {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%) !important;
  color: white !important;
  border-radius: 8px 8px 0 0 !important;
}

.statItem {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.statValue {
  font-weight: 600;
}

.statValueSuccess {
  color: #52c41a;
}

.contentArea {
  margin: 0;
  background: #f5f5f5;
  overflow: auto;
}

.contentPadding {
  padding: 24px;
  min-height: calc(100vh - 64px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .siderContainer {
    position: fixed;
    z-index: 999;
    height: 100vh;
  }
  
  .headerContent {
    padding: 0 12px;
  }
  
  .logoSection {
    gap: 8px;
  }
  
  .logoIcon {
    font-size: 20px;
  }
  
  .titleSection h4, .mainTitle {
    font-size: 16px !important;
  }
  
  .subtitle {
    font-size: 11px;
  }
  
  .welcomeText {
    font-size: 13px !important;
  }
  
  .contentPadding {
    padding: 12px;
  }
}

/* Additional Styling - Applied via className in component */
.antMenuSelected {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%) !important;
  color: #1890ff !important;
  border-left: 3px solid #1890ff !important;
}

.antMenuHover {
  background: rgba(24, 144, 255, 0.08) !important;
  color: #1890ff !important;
}

.antDropdown {
  border-radius: 8px !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12) !important;
}

.antCardHead {
  border-radius: 8px 8px 0 0 !important;
}

.antBadgeCount {
  background: #ff4d4f !important;
  box-shadow: 0 2px 6px rgba(255, 77, 79, 0.4) !important;
}
