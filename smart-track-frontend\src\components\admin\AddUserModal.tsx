'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { apiService } from '@/services/api';
import {
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Button,
  Row,
  Col,
  message,
  Divider,
  Typography,
  Space,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  LockOutlined,
  IdcardOutlined,
} from '@ant-design/icons';

const { Title } = Typography;
const { Option } = Select;

interface AddUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void; // Optional callback for successful user creation
}

interface UserFormData {
  sap_code: string;
  username: string;
  email: string;
  full_name: string;
  phone: string;
  password: string;
  role_id: number;
  is_active: boolean;
}

export default function AddUserModal({ isOpen, onClose, onSuccess }: AddUserModalProps) {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Fetch roles for dropdown
  const { data: rolesData, isLoading: rolesLoading } = useQuery({
    queryKey: ['roles'],
    queryFn: () => apiService.getRoles(),
    enabled: isOpen,
    retry: false,
  });

  const roles = rolesData?.data || [
    { id: 1, name: 'Admin', description: 'System Administrator' },
    { id: 2, name: 'Manager', description: 'Sales Manager' },
    { id: 3, name: 'Supervisor', description: 'Sales Supervisor' },
    { id: 4, name: 'Sales Rep', description: 'Sales Representative' }
  ];

  const createUserMutation = useMutation({
    mutationFn: (userData: UserFormData) => {
      console.log('🚀 CreateUser mutation called with data:', userData);
      return apiService.createUser(userData);
    },
    onSuccess: (response) => {
      console.log('✅ User created successfully:', response.data);
      queryClient.invalidateQueries({ 
        queryKey: ['users'],
        exact: false 
      });
      queryClient.refetchQueries({ 
        queryKey: ['users'],
        exact: false 
      });
      message.success('User created successfully!');
      if (onSuccess) {
        onSuccess();
      }
      handleClose();
    },
    onError: (error: any) => {
      console.error('❌ User creation failed:', error);
      if (error.response?.data?.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error('Failed to create user');
      }
      setLoading(false);
    },
  });

  const handleClose = () => {
    form.resetFields();
    setLoading(false);
    onClose();
  };

  const handleSubmit = async (values: UserFormData) => {
    setLoading(true);
    createUserMutation.mutate(values);
  };

  return (
    <Modal
      title={
        <Space>
          <UserOutlined style={{ color: '#1890ff' }} />
          <Title level={4} style={{ margin: 0 }}>Add New User</Title>
        </Space>
      }
      open={isOpen}
      onCancel={handleClose}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Divider style={{ margin: '16px 0' }} />
      
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          is_active: true,
          role_id: 1,
        }}
        requiredMark={false}
      >
        <Row gutter={[16, 0]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="sap_code"
              label="SAP Code"
              rules={[{ required: true, message: 'SAP Code is required' }]}
            >
              <Input
                prefix={<IdcardOutlined />}
                placeholder="Enter SAP code"
                size="large"
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="username"
              label="Username"
              rules={[
                { required: true, message: 'Username is required' },
                { min: 3, message: 'Username must be at least 3 characters' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Enter username"
                size="large"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="email"
              label="Email Address"
              rules={[
                { required: true, message: 'Email is required' },
                { type: 'email', message: 'Please enter a valid email' }
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="Enter email address"
                size="large"
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="full_name"
              label="Full Name"
              rules={[{ required: true, message: 'Full name is required' }]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Enter full name"
                size="large"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="phone"
              label="Phone Number"
            >
              <Input
                prefix={<PhoneOutlined />}
                placeholder="Enter phone number"
                size="large"
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="password"
              label="Password"
              rules={[
                { required: true, message: 'Password is required' },
                { min: 6, message: 'Password must be at least 6 characters' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Enter password"
                size="large"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="role_id"
              label="Role"
              rules={[{ required: true, message: 'Please select a role' }]}
            >
              <Select
                placeholder="Select user role"
                size="large"
                loading={rolesLoading}
              >
                {roles.map((role: any) => (
                  <Option key={role.id} value={role.id}>
                    <Space>
                      <span>{role.name}</span>
                      {role.description && (
                        <span style={{ color: '#666', fontSize: '12px' }}>
                          ({role.description})
                        </span>
                      )}
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="is_active"
              label="Status"
              valuePropName="checked"
            >
              <div style={{ paddingTop: '8px' }}>
                <Switch
                  checkedChildren="Active"
                  unCheckedChildren="Inactive"
                  defaultChecked
                />
                <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                  User will be able to log in and access the system
                </div>
              </div>
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        <Row justify="end">
          <Col>
            <Space>
              <Button onClick={handleClose} size="large">
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                icon={<UserOutlined />}
              >
                {loading ? 'Creating User...' : 'Create User'}
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
}
