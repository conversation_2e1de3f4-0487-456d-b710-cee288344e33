import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import envConfig from '../config/environment';

class ApiService {
  constructor() {
    // Use environment configuration
    this.baseURL = envConfig.getAutoDetectedBaseUrl();
    
    if (envConfig.isDebugMode()) {
      console.log('🚀 API Service Initialized');
      console.log('API Base URL:', this.baseURL);
      console.log('Environment:', envConfig.get('ENVIRONMENT'));
      console.log('Debug Mode:', envConfig.isDebugMode());
      console.log('Available URLs:', envConfig.getAlternativeUrls());
    }
    
    this.instance = axios.create({
      baseURL: this.baseURL,
      timeout: envConfig.getApiTimeout(),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      // Add additional config for Expo
      validateStatus: function (status) {
        return status >= 200 && status < 300; // Only accept success status codes
      }
    });

    this.setupInterceptors();
  }

  async getStoredTokens() {
    try {
      const accessToken = await AsyncStorage.getItem('access_token');
      const refreshToken = await AsyncStorage.getItem('refresh_token');
      return { accessToken, refreshToken };
    } catch (error) {
      console.log('Failed to get tokens from AsyncStorage:', error);
      return { accessToken: null, refreshToken: null };
    }
  }

  async storeTokens(accessToken, refreshToken) {
    try {
      await AsyncStorage.setItem('access_token', accessToken);
      await AsyncStorage.setItem('refresh_token', refreshToken);
    } catch (error) {
      console.log('Failed to store tokens in AsyncStorage:', error);
    }
  }

  async clearTokens() {
    try {
      await AsyncStorage.removeItem('access_token');
      await AsyncStorage.removeItem('refresh_token');
    } catch (error) {
      console.log('Failed to clear tokens from AsyncStorage:', error);
    }
  }

  setupInterceptors() {
    // Request interceptor to add auth token
    this.instance.interceptors.request.use(
      async (config) => {
        try {
          const { accessToken } = await this.getStoredTokens();
          if (accessToken) {
            config.headers.Authorization = `Bearer ${accessToken}`;
          } else {
            // For development/testing - remove this in production
            console.log('No access token found - proceeding without auth for testing');
          }
        } catch (error) {
          console.log('Failed to get access token:', error);
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle token refresh
    this.instance.interceptors.response.use(
      (response) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const { refreshToken } = await this.getStoredTokens();
            if (refreshToken) {
              const response = await axios.post(`${this.baseURL}/auth/refresh`, {
                refresh_token: refreshToken,
              });

              const { access_token, refresh_token } = response.data;
              
              // Update tokens in AsyncStorage
              await this.storeTokens(access_token, refresh_token);

              // Retry original request
              originalRequest.headers.Authorization = `Bearer ${access_token}`;
              return this.instance(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, clear tokens
            await this.clearTokens();
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // HTTP methods
  async get(url, config) {
    return this.instance.get(url, config);
  }

  async post(url, data, config) {
    return this.instance.post(url, data, config);
  }

  async put(url, data, config) {
    return this.instance.put(url, data, config);
  }

  async patch(url, data, config) {
    return this.instance.patch(url, data, config);
  }

  async delete(url, config) {
    return this.instance.delete(url, config);
  }

  // Auth methods
  async login(username, password) {
    try {
      console.log('Attempting login to:', `${this.baseURL}/auth/login`);
      console.log('Username:', username);
      console.log('User-Agent check:', navigator?.userAgent || 'No user agent');
      
      const formData = new FormData();
      formData.append('username', username);
      formData.append('password', password);

      const response = await this.instance.post('/auth/login', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 15000, // Longer timeout for Expo
      });
      
      console.log('Login successful:', response.status);
      console.log('Response data keys:', Object.keys(response.data));
      return response.data;
    } catch (error) {
      console.error('Login error details:', {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        baseURL: this.baseURL,
        url: error.config?.url,
        headers: error.config?.headers
      });
      
      // Add more specific error handling for Expo
      if (!error.response) {
        // Network error - backend not reachable
        let networkMessage = 'Cannot connect to server. ';
        
        if (error.code === 'ECONNREFUSED') {
          networkMessage += 'Connection refused. Make sure the backend is running on port 8000.';
        } else if (error.code === 'ENOTFOUND' || error.code === 'EAI_AGAIN') {
          networkMessage += 'Cannot resolve server address. Check your network connection.';
        } else {
          networkMessage += 'Please check if the backend is running and accessible.';
        }
        
        const networkError = new Error(networkMessage);
        networkError.code = 'NETWORK_ERROR';
        throw networkError;
      }
      
      throw error;
    }
  }

  async register(userData) {
    const response = await this.instance.post('/auth/register', userData);
    return response.data;
  }

  async refreshToken(refreshToken) {
    const response = await this.instance.post('/auth/refresh', {
      refresh_token: refreshToken,
    });
    return response.data;
  }

  async getCurrentUser() {
    const response = await this.instance.get('/auth/me');
    return response.data;
  }

  // Test backend connectivity
  async testConnection() {
    try {
      console.log('Testing connection to backend...');
      console.log('Full URL being tested:', this.baseURL);
      
      const rootURL = this.baseURL.replace('/api/v1', '');
      console.log('Root URL:', rootURL);
      
      // Use axios instead of fetch for better Expo compatibility
      const response = await axios.get(rootURL, {
        timeout: 10000,
        validateStatus: function (status) {
          return status < 500; // Accept any status code less than 500
        }
      });
      
      if (response.status === 200) {
        console.log('Backend is reachable:', response.data);
        return { success: true, data: response.data };
      } else {
        console.log('Backend responded with status:', response.status);
        return { success: false, error: `HTTP ${response.status}: ${response.statusText}` };
      }
    } catch (error) {
      console.error('Backend connection test failed:', {
        message: error.message,
        code: error.code,
        config: error.config?.url,
        response: error.response?.status
      });
      
      let errorMessage = error.message;
      if (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR') {
        errorMessage = `Cannot reach backend at ${this.baseURL}. Make sure the backend server is running on port 8000.`;
      }
      
      return { success: false, error: errorMessage };
    }
  }

  // Test dashboard API specifically
  async testDashboardAPI() {
    try {
      console.log('🧪 Testing dashboard API endpoint...');
      
      // First, test if we're authenticated
      const { accessToken } = await this.getStoredTokens();
      if (!accessToken) {
        return { success: false, error: 'No access token found. Please login first.' };
      }
      
      console.log('🧪 Access token found, testing dashboard endpoint...');
      
      const response = await this.get('/dashboard/');
      
      console.log('🧪 Dashboard test response:', {
        status: response.status,
        dataKeys: Object.keys(response.data || {}),
        hasKPIs: !!response.data?.kpis,
        hasVisits: !!response.data?.upcoming_visits,
        hasTasks: !!response.data?.pending_tasks
      });
      
      return { 
        success: response.status === 200, 
        data: response.data,
        status: response.status 
      };
      
    } catch (error) {
      console.error('🧪 Dashboard API test failed:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data
      });
      
      return { 
        success: false, 
        error: error.message,
        status: error.response?.status,
        details: error.response?.data
      };
    }
  }

  // Update base URL if needed (for different environments)
  updateBaseURL(newBaseURL) {
    this.baseURL = newBaseURL;
    this.instance.defaults.baseURL = newBaseURL;
    if (envConfig.isDebugMode()) {
      console.log('🔄 Updated API Base URL to:', newBaseURL);
    }
  }

  // Method to test different URLs for connectivity
  async findWorkingBaseURL() {
    const urlsToTry = envConfig.getAlternativeUrls();

    if (envConfig.isDebugMode()) {
      console.log('🔍 Testing multiple URLs for connectivity...');
    }
    
    for (const url of urlsToTry) {
      if (envConfig.isDebugMode()) {
        console.log(`Testing URL: ${url}`);
      }
      
      try {
        // Create a temporary axios instance for testing
        const testResponse = await axios.get(url.replace('/api/v1', ''), {
          timeout: 5000,
          validateStatus: function (status) {
            return status < 500;
          }
        });
        
        if (testResponse.status === 200) {
          if (envConfig.isDebugMode()) {
            console.log(`✅ Found working URL: ${url}`);
          }
          this.updateBaseURL(url);
          return { success: true, workingURL: url };
        }
      } catch (error) {
        if (envConfig.isDebugMode()) {
          console.log(`❌ URL ${url} failed:`, error.message);
        }
      }
    }

    return { success: false, error: 'No working URL found' };
  }
}

export const api = new ApiService();
export default api;
