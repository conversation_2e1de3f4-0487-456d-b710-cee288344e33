// Quick test file to verify imports work correctly
import React from 'react';
import { View, Text } from 'react-native';
import { useAuthStore } from '../stores/authStore';
import dashboardService from '../services/dashboardService';
import locationService from '../services/locationService';
import { api } from '../services/api';

const TestImports = () => {
  const user = useAuthStore((state) => state.user);
  
  React.useEffect(() => {
    console.log('Testing service imports...');
    console.log('Dashboard service:', typeof dashboardService);
    console.log('Location service:', typeof locationService);
    console.log('API service:', typeof api);
    console.log('Auth store user:', user);
  }, []);

  return (
    <View>
      <Text>Import test component</Text>
    </View>
  );
};

export default TestImports;
