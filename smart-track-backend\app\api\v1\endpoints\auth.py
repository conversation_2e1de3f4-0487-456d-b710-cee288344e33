from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session, joinedload
from datetime import timedelta
import logging

from app.core.database import get_db
from app.core.security import (
    verify_password, 
    create_access_token, 
    create_refresh_token,
    verify_token,
    get_current_user
)
from app.models.user import User
from app.schemas.auth import LoginResponse, RefreshTokenRequest
from app.schemas.user import UserResponse
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/login", response_model=LoginResponse)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """Authenticate user and return access token"""
    # Find user by username or email with role and profile relationships
    user = db.query(User).options(
        joinedload(User.role),
        joinedload(User.profile)
    ).filter(
        (User.username == form_data.username) | (User.email == form_data.username)
    ).first()
    
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is deactivated",
        )
    
    # Explicitly access relationships to ensure they're loaded
    user_role = user.role
    user_profile = user.profile
    
    # Create tokens
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )
    refresh_token = create_refresh_token(data={"sub": str(user.id)})
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "user": {
            "id": user.id,
            "sap_code": user.sap_code,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "phone": user.phone,
            "is_active": user.is_active,
            "is_verified": user.is_verified,
            "role_id": user.role_id,
            "created_at": user.created_at,
            "updated_at": user.updated_at,
            "role": {
                "id": user_role.id,
                "name": user_role.name,
                "description": user_role.description,
                "is_active": user_role.is_active,
                "created_at": user_role.created_at
            } if user_role else None,
            "profile": {
                "id": user_profile.id,
                "user_id": user_profile.user_id,
                "distribution_channel_id": user_profile.distribution_channel_id,
                "role_type": user_profile.role_type,
                "supervisor_id": user_profile.supervisor_id,
                "profile_image": user_profile.profile_image,
                "address": user_profile.address,
                "emergency_contact": user_profile.emergency_contact,
                "created_at": user_profile.created_at,
                "updated_at": user_profile.updated_at
            } if user_profile else None
        }
    }

@router.post("/refresh", response_model=LoginResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """Refresh access token using refresh token"""
    try:
        payload = verify_token(refresh_data.refresh_token)
        user_id = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        user = db.query(User).filter(User.id == user_id).first()
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or deactivated"
            )
        
        # Create new tokens
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(user.id)}, expires_delta=access_token_expires
        )
        new_refresh_token = create_refresh_token(data={"sub": str(user.id)})
        
        return {
            "access_token": access_token,
            "refresh_token": new_refresh_token,
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "sap_code": user.sap_code,
                "username": user.username,
                "email": user.email,
                "full_name": user.full_name,
                "phone": user.phone,
                "is_active": user.is_active,
                "is_verified": user.is_verified,
                "role_id": user.role_id,
                "created_at": user.created_at,
                "updated_at": user.updated_at,
                "role": {
                    "id": user.role.id,
                    "name": user.role.name,
                    "description": user.role.description,
                    "is_active": user.role.is_active,
                    "created_at": user.role.created_at
                } if user.role else None,
                "profile": {
                    "id": user.profile.id,
                    "user_id": user.profile.user_id,
                    "distribution_channel_id": user.profile.distribution_channel_id,
                    "role_type": user.profile.role_type,
                    "supervisor_id": user.profile.supervisor_id,
                    "profile_image": user.profile.profile_image,
                    "address": user.profile.address,
                    "emergency_contact": user.profile.emergency_contact,
                    "created_at": user.profile.created_at,
                    "updated_at": user.profile.updated_at
                } if user.profile else None
            }
        }
        
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )

@router.get("/me")
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information with role and profile"""
    return {
        "id": current_user.id,
        "sap_code": current_user.sap_code,
        "username": current_user.username,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "phone": current_user.phone,
        "is_active": current_user.is_active,
        "is_verified": current_user.is_verified,
        "role_id": current_user.role_id,
        "created_at": current_user.created_at,
        "updated_at": current_user.updated_at,
        "role": {
            "id": current_user.role.id,
            "name": current_user.role.name,
            "description": current_user.role.description,
            "is_active": current_user.role.is_active,
            "created_at": current_user.role.created_at
        } if current_user.role else None,
        "profile": {
            "id": current_user.profile.id,
            "user_id": current_user.profile.user_id,
            "distribution_channel_id": current_user.profile.distribution_channel_id,
            "role_type": current_user.profile.role_type,
            "supervisor_id": current_user.profile.supervisor_id,
            "profile_image": current_user.profile.profile_image,
            "address": current_user.profile.address,
            "emergency_contact": current_user.profile.emergency_contact,
            "created_at": current_user.profile.created_at,
            "updated_at": current_user.profile.updated_at
        } if current_user.profile else None
    }
