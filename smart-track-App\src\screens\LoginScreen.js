import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import {
  Text,
  TextInput,
  ActivityIndicator,
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuthStore } from '../stores/authStore';
import { api } from '../services/api';

const { width, height } = Dimensions.get('window');

const LoginScreen = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(true);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const login = useAuthStore((state) => state.login);

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Handle login with username/password
  const handleLogin = async () => {
    if (!username.trim() || !password.trim()) {
      Alert.alert('Missing Information', 'Please enter both username and password', [
        { text: 'OK', style: 'default' }
      ]);
      return;
    }

    setIsLoading(true);
    
    try {
      const response = await api.login(username.trim(), password);
      
      console.log('🔐 Login response received:', response);
      console.log('🔐 Response keys:', Object.keys(response || {}));
      
      // Store the authentication data using Zustand store
      await login(response);
      
      console.log('✅ Login process completed');
      
      // Navigation will happen automatically when isAuthenticated becomes true
      // No need for success alert as the user will see the main app
    } catch (error) {
      console.error('Login error:', error);
      let errorMessage = 'Login failed. Please try again.';
      
      if (error.code === 'NETWORK_ERROR' || !error.response) {
        errorMessage = `Cannot connect to server at ${api.baseURL}.\n\nPlease check:\n• Backend server is running\n• Network connection\n• Correct server URL`;
      } else if (error.response?.status === 401) {
        errorMessage = 'Invalid username or password.';
      } else if (error.response?.status === 403) {
        errorMessage = 'Your account is deactivated. Please contact support.';
      }
      
      Alert.alert('Login Error', errorMessage, [
        { text: 'OK', style: 'default' }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      setIsLoading(true);
      
      // First try the current URL
      console.log('Testing current API URL...');
      const result = await api.testConnection();
      
      if (result.success) {
        Alert.alert('Connection Test', `✅ Backend is reachable!\n\nServer: ${result.data.message}\nVersion: ${result.data.version}\nURL: ${api.baseURL}`, [
          { text: 'OK', style: 'default' }
        ]);
      } else {
        // If current URL fails, try to find a working one
        console.log('Current URL failed, trying alternatives...');
        const findResult = await api.findWorkingBaseURL();
        
        if (findResult.success) {
          Alert.alert('Connection Test', `✅ Found working connection!\n\nWorking URL: ${findResult.workingURL}\n\nThe app will now use this URL.`, [
            { text: 'OK', style: 'default' }
          ]);
        } else {
          Alert.alert('Connection Test', `❌ Backend not reachable\n\nTried URLs:\n• ${api.baseURL}\n• http://********:8000/api/v1\n• http://localhost:8000/api/v1\n\nMake sure:\n• Backend server is running\n• Port 8000 is accessible\n• No firewall blocking connection`, [
            { text: 'OK', style: 'default' }
          ]);
        }
      }
    } catch (error) {
      Alert.alert('Connection Test', `❌ Test failed: ${error.message}`, [
        { text: 'OK', style: 'default' }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />
      <View style={styles.content}>
        {/* Background with gradient effect */}
        <View style={styles.backgroundGradient}>
          <View style={styles.gradientLayer1} />
          <View style={styles.gradientLayer2} />
          <View style={styles.gradientLayer3} />
        </View>
        
        {/* Content */}
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardContainer}
        >
          <ScrollView 
            contentContainerStyle={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            <Animated.View 
              style={[
                styles.content,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }]
                }
              ]}
            >
              {/* Logo and Header */}
              <View style={styles.header}>
                <View style={styles.logoContainer}>
                  <View style={styles.logoCircle}>
                    <Ionicons name="business" size={32} color="#007AFF" />
                  </View>
                </View>
                
                <Text style={styles.appTitle}>Smart Sales</Text>
                <Text style={styles.subtitle}>
                  Sign in to your account
                </Text>
              </View>

              {/* Form Container */}
              <View style={styles.formContainer}>
                <View style={styles.formContent}>
                  <View style={styles.inputContainer}>
                    <Text style={styles.inputLabel}>Username or Email</Text>
                    <TextInput
                      value={username}
                      onChangeText={setUsername}
                      style={styles.textInput}
                      placeholder="Enter your username or email"
                      placeholderTextColor="#8E8E93"
                      underlineColor="transparent"
                      activeUnderlineColor="transparent"
                      mode="flat"
                      autoCapitalize="none"
                      autoCorrect={false}
                      theme={{
                        colors: {
                          primary: 'transparent',
                          background: 'transparent',
                        }
                      }}
                    />
                  </View>
                  
                  <View style={styles.inputContainer}>
                    <Text style={styles.inputLabel}>Password</Text>
                    <View style={styles.passwordInputWrapper}>
                      <TextInput
                        value={password}
                        onChangeText={setPassword}
                        secureTextEntry={!showPassword}
                        style={[styles.textInput, styles.passwordInput]}
                        placeholder="Enter your password"
                        placeholderTextColor="#8E8E93"
                        underlineColor="transparent"
                        activeUnderlineColor="transparent"
                        mode="flat"
                        autoCapitalize="none"
                        autoCorrect={false}
                        theme={{
                          colors: {
                            primary: 'transparent',
                            background: 'transparent',
                          }
                        }}
                      />
                      <TouchableOpacity
                        style={styles.passwordToggle}
                        onPress={() => setShowPassword(!showPassword)}
                        activeOpacity={0.7}
                      >
                        <Ionicons 
                          name={showPassword ? "eye-off-outline" : "eye-outline"} 
                          size={20} 
                          color="#8E8E93" 
                        />
                      </TouchableOpacity>
                    </View>
                  </View>
                  
                  {/* Remember Me Checkbox */}
                  <TouchableOpacity 
                    style={styles.rememberMeContainer}
                    onPress={() => setRememberMe(!rememberMe)}
                    activeOpacity={0.7}
                  >
                    <Ionicons 
                      name={rememberMe ? "checkbox" : "checkbox-outline"} 
                      size={20} 
                      color="#007AFF" 
                    />
                    <Text style={styles.rememberMeText}>Remember me</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[
                      styles.primaryButton,
                      (!username.trim() || !password.trim() || isLoading) && styles.buttonDisabled
                    ]}
                    onPress={handleLogin}
                    disabled={!username.trim() || !password.trim() || isLoading}
                    activeOpacity={0.8}
                  >
                    {isLoading ? (
                      <ActivityIndicator size="small" color="#FFFFFF" />
                    ) : (
                      <Text style={styles.primaryButtonText}>Sign In</Text>
                    )}
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.testButton}
                    onPress={handleTestConnection}
                    disabled={isLoading}
                    activeOpacity={0.8}
                  >
                    <Text style={styles.testButtonText}>Test Connection</Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Demo Info */}
              <View style={styles.demoContainer}>
                <Text style={styles.demoText}>
                  Demo Credentials:{'\n'}
                  Admin: admin / password123{'\n'}
                  Sales Rep: salesrep1 / password123{'\n'}
                  {'\n'}
                  📱 Expo Troubleshooting:{'\n'}
                  1. Use "Test Connection" button{'\n'}
                  2. Ensure backend is running on port 8000{'\n'}
                  3. For physical device: Update IP in api.js
                </Text>
              </View>
            </Animated.View>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  content: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  gradientLayer1: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#1a1a1a',
  },
  gradientLayer2: {
    position: 'absolute',
    top: '20%',
    left: '-20%',
    width: '60%',
    height: '40%',
    backgroundColor: '#007AFF',
    opacity: 0.1,
    borderRadius: width,
    transform: [{ scale: 1.5 }],
  },
  gradientLayer3: {
    position: 'absolute',
    bottom: '10%',
    right: '-30%',
    width: '80%',
    height: '50%',
    backgroundColor: '#5856D6',
    opacity: 0.08,
    borderRadius: width,
    transform: [{ scale: 1.2 }],
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 60,
  },
  header: {
    alignItems: 'center',
    marginBottom: 50,
  },
  logoContainer: {
    marginBottom: 32,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0, 122, 255, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0, 122, 255, 0.3)',
    shadowColor: '#007AFF',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  appTitle: {
    fontSize: 34,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 8,
    letterSpacing: -0.5,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 17,
    color: 'rgba(255, 255, 255, 0.7)',
    fontWeight: '400',
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 20,
    padding: 28,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
  },
  formContent: {
    width: '100%',
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 15,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
    letterSpacing: -0.2,
  },
  textInput: {
    flex: 1,
    fontSize: 17,
    color: '#FFFFFF',
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    borderRadius: 14,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.15)',
    paddingHorizontal: 16,
    paddingVertical: 0,
    height: 52,
  },
  passwordInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    borderRadius: 14,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.15)',
    paddingHorizontal: 16,
    minHeight: 52,
  },
  passwordInput: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    borderRadius: 0,
    paddingHorizontal: 0,
  },
  passwordToggle: {
    padding: 8,
    marginLeft: 8,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    borderRadius: 14,
    height: 52,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#007AFF',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.4,
    shadowRadius: 14,
    elevation: 8,
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '600',
    letterSpacing: -0.2,
  },
  buttonDisabled: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    shadowOpacity: 0,
    elevation: 0,
  },
  testButton: {
    backgroundColor: 'transparent',
    borderRadius: 14,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  testButtonText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontWeight: '500',
  },
  rememberMeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  rememberMeText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 15,
    fontWeight: '500',
    marginLeft: 8,
  },
  demoContainer: {
    marginTop: 40,
    padding: 20,
    backgroundColor: 'rgba(0, 122, 255, 0.08)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(0, 122, 255, 0.2)',
  },
  demoText: {
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 13,
    fontWeight: '500',
    letterSpacing: -0.1,
  },
});

export default LoginScreen;
