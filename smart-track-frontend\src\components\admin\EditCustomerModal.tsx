'use client';

import React, { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Modal, 
  Form, 
  Input, 
  Button, 
  message, 
  Row, 
  Col, 
  InputNumber, 
  Select, 
  Switch 
} from 'antd';
import { 
  UserOutlined, 
  PhoneOutlined, 
  MailOutlined, 
  HomeOutlined,
  BankOutlined,
  DollarOutlined
} from '@ant-design/icons';

import { apiService } from '../../services/api';
import { Customer } from '../../types';

interface EditCustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer: Customer | null;
}

interface CustomerFormData {
  code: string;
  name: string;
  customer_group?: string;
  address?: string;
  city?: string;
  province?: string;
  region?: string;
  phone?: string;
  email?: string;
  customer_type?: string;
  credit_limit?: number;
  current_balance?: number;
  customer_statistics_group?: string;
  sdst?: string;
  latitude?: number;
  longitude?: number;
  is_active: boolean;
}

const customerTypes = [
  { value: 'dealer', label: 'Dealer' },
  { value: 'distributor', label: 'Distributor' },
  { value: 'retailer', label: 'Retailer' },
  { value: 'wholesale', label: 'Wholesale' },
  { value: 'end_user', label: 'End User' },
];

export default function EditCustomerModal({ isOpen, onClose, customer }: EditCustomerModalProps) {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isActive, setIsActive] = useState(true); // Add state to track switch value

  // Update form when customer changes
  useEffect(() => {
    if (customer && isOpen) {
      console.log('🔧 Setting form values for customer:', customer);
      
      const activeStatus = customer.is_active;
      setIsActive(activeStatus);
      
      const formValues = {
        code: customer.code || '',
        name: customer.name || '',
        customer_group: customer.customer_group || '',
        address: customer.address || '',
        city: customer.city || '',
        province: customer.province || '',
        region: customer.region || '',
        phone: customer.phone || '',
        email: customer.email || '',
        customer_type: customer.customer_type || '',
        credit_limit: customer.credit_limit || 0,
        current_balance: customer.current_balance || 0,
        customer_statistics_group: customer.customer_statistics_group || '',
        sdst: customer.sdst || '',
        latitude: customer.latitude || undefined,
        longitude: customer.longitude || undefined,
        is_active: activeStatus
      };
      
      console.log('🔧 Form values to set:', formValues);
      console.log('🔧 State isActive will be set to:', activeStatus);
      
      form.setFieldsValue(formValues);
    }
  }, [customer, isOpen, form]);

  const updateCustomerMutation = useMutation({
    mutationFn: (customerData: CustomerFormData) => {
      console.log('🚀 UpdateCustomer mutation called with data:', customerData);
      console.log('🚀 customerData.is_active type:', typeof customerData.is_active);
      console.log('🚀 customerData.is_active value:', customerData.is_active);
      
      // Let's also log the entire payload being sent
      const payload = {
        code: customerData.code,
        name: customerData.name,
        customer_group: customerData.customer_group,
        address: customerData.address,
        city: customerData.city,
        province: customerData.province,
        region: customerData.region,
        phone: customerData.phone,
        email: customerData.email,
        customer_type: customerData.customer_type,
        credit_limit: customerData.credit_limit || 0,
        current_balance: customerData.current_balance || 0,
        customer_statistics_group: customerData.customer_statistics_group,
        sdst: customerData.sdst,
        latitude: customerData.latitude,
        longitude: customerData.longitude,
        is_active: customerData.is_active
      };
      console.log('🚀 Complete payload being sent to API:', payload);
      
      return apiService.updateCustomer(customer!.id, payload);
    },
    onSuccess: (response) => {
      console.log('✅ Customer updated successfully:', response.data);
      console.log('✅ Updated customer is_active:', response.data.is_active);
      
      // Invalidate the cache to mark it as stale - this will trigger refetch when components next mount
      queryClient.invalidateQueries({ 
        queryKey: ['customers'],
        exact: false 
      }).then(() => {
        console.log('✅ Customer queries invalidated successfully');
      });
      
      message.success('Customer updated successfully!');
      handleClose();
    },
    onError: (error: any) => {
      console.error('❌ Customer update failed:', error);
      if (error.response?.data?.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error('Failed to update customer');
      }
      setLoading(false);
    },
  });

  const handleClose = () => {
    form.resetFields();
    setLoading(false);
    setIsActive(true); // Reset state variable
    onClose();
  };

  const onFinish = async (values: CustomerFormData) => {
    try {
      setLoading(true);
      console.log('📝 Form submitted with values:', values);
      console.log('📝 Form isActive state:', isActive);
      console.log('📝 Values is_active:', values.is_active);
      updateCustomerMutation.mutate(values);
    } catch (error) {
      console.error('❌ Form submission error:', error);
      setLoading(false);
    }
  };

  if (!customer) return null;

  return (
    <Modal
      title="Edit Customer"
      open={isOpen}
      onCancel={handleClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Customer Code"
              name="code"
              rules={[
                { required: true, message: 'Please enter customer code!' },
                { min: 2, message: 'Code must be at least 2 characters' }
              ]}
            >
              <Input 
                prefix={<BankOutlined />} 
                placeholder="Enter customer code"
                autoFocus
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Customer Name"
              name="name"
              rules={[
                { required: true, message: 'Please enter customer name!' },
                { min: 2, message: 'Name must be at least 2 characters' }
              ]}
            >
              <Input 
                prefix={<UserOutlined />} 
                placeholder="Enter customer name"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Email"
              name="email"
              rules={[
                { type: 'email', message: 'Please enter a valid email!' }
              ]}
            >
              <Input 
                prefix={<MailOutlined />} 
                placeholder="Enter email address"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Phone"
              name="phone"
            >
              <Input 
                prefix={<PhoneOutlined />} 
                placeholder="Enter phone number"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label="Address"
              name="address"
            >
              <Input 
                prefix={<HomeOutlined />} 
                placeholder="Enter full address"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label="City"
              name="city"
            >
              <Input placeholder="Enter city" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Province"
              name="province"
            >
              <Input placeholder="Enter province" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Region"
              name="region"
            >
              <Input placeholder="Enter region" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Customer Type"
              name="customer_type"
            >
              <Select 
                placeholder="Select customer type"
                options={customerTypes}
                allowClear
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Customer Group"
              name="customer_group"
            >
              <Input placeholder="Enter customer group" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Credit Limit"
              name="credit_limit"
            >
              <InputNumber 
                prefix={<DollarOutlined />}
                placeholder="0.00"
                min={0}
                precision={2}
                style={{ width: '100%' }}
                formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => (parseFloat(value!.replace(/\$\s?|(,*)/g, '')) || 0) as any}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Current Balance"
              name="current_balance"
            >
              <InputNumber 
                prefix={<DollarOutlined />}
                placeholder="0.00"
                min={0}
                precision={2}
                style={{ width: '100%' }}
                formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => (parseFloat(value!.replace(/\$\s?|(,*)/g, '')) || 0) as any}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Statistics Group"
              name="customer_statistics_group"
            >
              <Input placeholder="Enter statistics group" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="SDST"
              name="sdst"
            >
              <Input placeholder="Enter SDST" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Latitude"
              name="latitude"
            >
              <InputNumber 
                placeholder="0.000000"
                precision={6}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Longitude"
              name="longitude"
            >
              <InputNumber 
                placeholder="0.000000"
                precision={6}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label="Active Customer"
          name="is_active"
          valuePropName="checked"
        >
          <Switch 
            checkedChildren="Active" 
            unCheckedChildren="Inactive"
            onChange={(checked) => {
              console.log('🔧 Switch changed to:', checked);
              setIsActive(checked);
            }}
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, marginTop: 24 }}>
          <Row gutter={8} justify="end">
            <Col>
              <Button onClick={handleClose}>
                Cancel
              </Button>
            </Col>
            <Col>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                disabled={loading}
              >
                Update Customer
              </Button>
            </Col>
          </Row>
        </Form.Item>
      </Form>
    </Modal>
  );
}
