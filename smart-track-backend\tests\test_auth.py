"""
Test authentication endpoints
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from app.main import app

def test_login_endpoint_exists():
    """Test that login endpoint exists"""
    client = TestClient(app)
    response = client.post("/api/v1/auth/login", json={
        "username": "testuser",
        "password": "wrongpassword"
    })
    # Should return 401 or 422, not 404 (endpoint exists)
    assert response.status_code != 404

def test_register_endpoint_exists():
    """Test that register endpoint exists"""
    client = TestClient(app)
    response = client.post("/api/v1/auth/register", json={
        "username": "newuser",
        "email": "<EMAIL>",
        "password": "newpassword123",
        "full_name": "New User"
    })
    # Should not return 404 (endpoint exists)
    assert response.status_code != 404

def test_protected_endpoint_without_token():
    """Test that protected endpoints require authentication"""
    client = TestClient(app)
    response = client.get("/api/v1/users/me")
    # Should return 401 Unauthorized
    assert response.status_code in [401, 403]

def test_invalid_login_credentials():
    """Test login with invalid credentials"""
    client = TestClient(app)
    response = client.post("/api/v1/auth/login", json={
        "username": "nonexistent",
        "password": "wrongpassword"
    })
    assert response.status_code in [401, 422]

def test_register_validation():
    """Test user registration validation"""
    client = TestClient(app)
    
    # Test with invalid email
    response = client.post("/api/v1/auth/register", json={
        "username": "testuser",
        "email": "invalid-email",
        "password": "password123",
        "full_name": "Test User"
    })
    assert response.status_code == 422
    
    # Test with short password
    response = client.post("/api/v1/auth/register", json={
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "123",
        "full_name": "Test User"
    })
    assert response.status_code == 422
