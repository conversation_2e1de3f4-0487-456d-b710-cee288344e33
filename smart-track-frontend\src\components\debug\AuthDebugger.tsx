import { useEffect } from 'react';
import { useAuthStore } from '@/store/auth';

export default function AuthDebugger() {
  const { user, isAuthenticated, token } = useAuthStore();

  useEffect(() => {
    console.log('🔍 AuthDebugger - Current Auth State:');
    console.log('  - User:', user);
    console.log('  - Is Authenticated:', isAuthenticated);
    console.log('  - Token exists:', !!token);
    console.log('  - User ID:', user?.id);
    console.log('  - Full Name:', user?.full_name);
    console.log('  - Email:', user?.email);
    
    // Check localStorage
    try {
      const storedAuth = localStorage.getItem('auth-storage');
      console.log('  - LocalStorage auth-storage:', storedAuth);
      if (storedAuth) {
        const parsed = JSON.parse(storedAuth);
        console.log('  - Parsed auth data:', parsed);
      }
    } catch (error) {
      console.error('  - Error reading localStorage:', error);
    }
  }, [user, isAuthenticated, token]);

  return null; // This is just a debugging component
}
