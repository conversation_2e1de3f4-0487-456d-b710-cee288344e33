'use client';

import { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiService } from '@/services/api';
import AddUserModal from './AddUserModal';
import EditUserModal from './EditUserModal';
import DeleteUserModal from './DeleteUserModal';
import { User } from '@/types';
import {
  Table,
  Button,
  Input,
  Space,
  Card,
  Avatar,
  Tag,
  Typography,
  Tooltip,
  Pagination,
  Empty,
  Spin,
  Row,
  Col,
  Divider,
  Badge,
  Popconfirm,
  message,
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons';

const { Title } = Typography;
const { Search } = Input;

export default function UserManagement() {
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const handleOpenModal = useCallback(() => {
    console.log('🎯 Add New User button clicked');
    setIsAddModalOpen(true);
  }, []);

  // Move useQuery before the callbacks that use refetch
  const { data: usersData, isLoading, refetch } = useQuery({
    queryKey: ['users', page],
    queryFn: () => apiService.getUsers(page, 10),
    staleTime: 0,
    gcTime: 0,
  });

  const users = usersData?.data || [];
  const pageSize = 10;
  const hasNextPage = users.length === pageSize;
  const hasPrevPage = page > 1;

  const handleUserCreated = useCallback(() => {
    console.log('🎉 User creation success callback triggered');
    setPage(1);
    refetch();
    message.success('User created successfully!');
  }, [refetch]);

  const handleCloseModal = useCallback(() => {
    console.log('🔒 Closing Add User modal');
    setIsAddModalOpen(false);
    setPage(1);
    refetch();
  }, [refetch]);

  const handleEditUser = useCallback((user: User) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  }, []);

  const handleCloseEditModal = useCallback(() => {
    setIsEditModalOpen(false);
    setSelectedUser(null);
    refetch();
    message.success('User updated successfully!');
  }, [refetch]);

  const handleDeleteUser = useCallback((user: User) => {
    setSelectedUser(user);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(false);
    setSelectedUser(null);
    refetch();
    message.success('User deleted successfully!');
  }, [refetch]);

  const handleRefresh = useCallback(() => {
    refetch();
    message.success('Data refreshed!');
  }, [refetch]);

  // Filter users based on search term
  const filteredUsers = users.filter(user => 
    user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Define table columns
  const columns = [
    {
      title: 'User',
      key: 'user',
      render: (_: any, user: User) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            size={40} 
            style={{ backgroundColor: '#1890ff', marginRight: 12 }}
            icon={<UserOutlined />}
          >
            {user.full_name.charAt(0).toUpperCase()}
          </Avatar>
          <div>
            <div style={{ fontWeight: 500, marginBottom: 2 }}>{user.full_name}</div>
            <div style={{ color: '#666', fontSize: '12px' }}>{user.email}</div>
            <div style={{ color: '#999', fontSize: '11px' }}>@{user.username}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role: any, user: User) => (
        <Tag color="blue">
          {role?.name || user.profile?.role_type || 'No Role'}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'status',
      render: (isActive: boolean) => (
        <Badge 
          status={isActive ? 'success' : 'error'} 
          text={isActive ? 'Active' : 'Inactive'}
        />
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, user: User) => (
        <Space size="small">
          <Tooltip title="Edit user">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEditUser(user)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="Delete user">
            <Popconfirm
              title="Are you sure you want to delete this user?"
              description="This action cannot be undone."
              onConfirm={() => handleDeleteUser(user)}
              okText="Yes, Delete"
              cancelText="Cancel"
              okButtonProps={{ danger: true }}
            >
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  if (isLoading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '60px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16, color: '#666' }}>Loading users...</div>
        </div>
      </Card>
    );
  }

  return (
    <div>
      <Card 
        title={
          <Title level={3} style={{ margin: 0 }}>
            User Management
          </Title>
        }
        extra={
          <Space>
            <Tooltip title="Refresh data">
              <Button 
                icon={<ReloadOutlined />} 
                onClick={handleRefresh}
                loading={isLoading}
              >
                Refresh
              </Button>
            </Tooltip>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleOpenModal}
            >
              Add New User
            </Button>
          </Space>
        }
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* Search Section */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="Search users by name, email, or username..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onSearch={(value) => setSearchTerm(value)}
                allowClear
                enterButton={<SearchOutlined />}
              />
            </Col>
            <Col xs={24} sm={12} md={16}>
              <div style={{ color: '#666', fontSize: '14px', lineHeight: '32px' }}>
                {searchTerm ? (
                  <>
                    Found {filteredUsers.length} user(s) matching "{searchTerm}"
                    {filteredUsers.length !== users.length && ` (filtered from ${users.length} total)`}
                  </>
                ) : (
                  `Showing ${users.length} user(s) on page ${page}`
                )}
              </div>
            </Col>
          </Row>

          <Divider style={{ margin: '12px 0' }} />

          {/* Table Section */}
          {filteredUsers.length === 0 ? (
            <Empty
              description={
                searchTerm 
                  ? `No users found matching "${searchTerm}"` 
                  : "No users found"
              }
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              {!searchTerm && (
                <Button type="primary" icon={<PlusOutlined />} onClick={handleOpenModal}>
                  Add First User
                </Button>
              )}
            </Empty>
          ) : (
            <>
              <Table
                columns={columns}
                dataSource={filteredUsers}
                rowKey="id"
                pagination={false}
                scroll={{ x: 800 }}
                size="middle"
              />

              {/* Custom Pagination */}
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center', 
                marginTop: 16 
              }}>
                <div style={{ color: '#666', fontSize: '14px' }}>
                  Showing {((page - 1) * pageSize) + 1} to{' '}
                  {Math.min(page * pageSize, ((page - 1) * pageSize) + filteredUsers.length)} of{' '}
                  {searchTerm ? `${filteredUsers.length} filtered` : 'many'} users
                </div>
                <Space>
                  <Button 
                    disabled={!hasPrevPage} 
                    onClick={() => setPage(Math.max(1, page - 1))}
                  >
                    Previous
                  </Button>
                  <span style={{ 
                    padding: '0 16px', 
                    color: '#1890ff', 
                    fontWeight: 500 
                  }}>
                    Page {page}
                  </span>
                  <Button 
                    disabled={!hasNextPage} 
                    onClick={() => setPage(page + 1)}
                  >
                    Next
                  </Button>
                </Space>
              </div>
            </>
          )}
        </Space>
      </Card>

      {/* Modals */}
      <AddUserModal 
        isOpen={isAddModalOpen} 
        onClose={handleCloseModal}
        onSuccess={handleUserCreated}
      />
      
      <EditUserModal 
        isOpen={isEditModalOpen} 
        onClose={handleCloseEditModal}
        user={selectedUser}
      />
      
      <DeleteUserModal 
        isOpen={isDeleteModalOpen} 
        onClose={handleCloseDeleteModal}
        user={selectedUser}
      />
    </div>
  );
}
