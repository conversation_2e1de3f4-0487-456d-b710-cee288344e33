'use client'

import React from 'react'
import { ConfigProvider } from 'antd'
import enUS from 'antd/locale/en_US'
import theme from '@/config/theme'

interface AntdProviderProps {
  children: React.ReactNode
}

const AntdProvider: React.FC<AntdProviderProps> = ({ children }) => {
  return (
    <ConfigProvider
      theme={theme}
      locale={enUS}
      componentSize="middle"
    >
      {children}
    </ConfigProvider>
  )
}

export default AntdProvider
