

# Production overrides for docker-compose.yml
# Use: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up

services:
  postgres:
    environment:
      POSTGRES_USER: smart_track
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your_secure_password_here}
      POSTGRES_DB: smart_track_production
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
    restart: unless-stopped

  # Uncomment the following lines to enable Redis service
  # Note: We are currently not using Redis in the application, but it can be enabled if needed.
  # If you decide to use Redis, make sure to update the REDIS_URL in your configuration file (app/core/config.py) and in .env accordingly.
  # redis:
  #   restart: unless-stopped
  #   volumes:
  #     - redis_prod_data:/data

  backend:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: smart_track_backend
    env_file:
      - .env
    restart: unless-stopped
    volumes:
      - ./uploads:/home/<USER>/src/uploads
      # Remove the source code mount for production
      # - .:/app

volumes:
  postgres_prod_data:
  redis_prod_data:
