// Environment configuration for Next.js dashboard
class EnvironmentConfig {
  constructor() {
    this.config = this.loadConfig();
  }

  loadConfig() {
    const env = {
      // API Configuration
      API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api/v1',
      API_TIMEOUT: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '15000'),
      
      // App Configuration
      APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || 'Smart Track Dashboard',
      APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
      ENVIRONMENT: process.env.NEXT_PUBLIC_ENVIRONMENT || 'development',
      
      // Authentication Configuration
      SESSION_TIMEOUT: parseInt(process.env.NEXT_PUBLIC_SESSION_TIMEOUT || '3600000'),
      AUTO_REFRESH_TOKEN: (process.env.NEXT_PUBLIC_AUTO_REFRESH_TOKEN || 'true') === 'true',
      
      // UI Configuration
      THEME: process.env.NEXT_PUBLIC_THEME || 'light',
      DEBUG_MODE: (process.env.NEXT_PUBLIC_DEBUG_MODE || 'true') === 'true'
    };

    if (env.DEBUG_MODE) {
      console.log('🔧 Dashboard Environment Configuration Loaded:');
      console.log('API_BASE_URL:', env.API_BASE_URL);
      console.log('ENVIRONMENT:', env.ENVIRONMENT);
      console.log('DEBUG_MODE:', env.DEBUG_MODE);
      console.log('API_TIMEOUT:', env.API_TIMEOUT);
    }

    return env;
  }

  get(key) {
    return this.config[key];
  }

  getApiBaseUrl() {
    return this.config.API_BASE_URL;
  }

  getApiTimeout() {
    return this.config.API_TIMEOUT;
  }

  getAppName() {
    return this.config.APP_NAME;
  }

  getAppVersion() {
    return this.config.APP_VERSION;
  }

  isDebugMode() {
    return this.config.DEBUG_MODE;
  }

  isProduction() {
    return this.config.ENVIRONMENT === 'production';
  }

  isDevelopment() {
    return this.config.ENVIRONMENT === 'development';
  }

  getSessionTimeout() {
    return this.config.SESSION_TIMEOUT;
  }

  shouldAutoRefreshToken() {
    return this.config.AUTO_REFRESH_TOKEN;
  }

  getTheme() {
    return this.config.THEME;
  }
}

const envConfig = new EnvironmentConfig();

export default envConfig;
