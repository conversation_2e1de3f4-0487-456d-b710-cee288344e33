import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createStackNavigator} from '@react-navigation/stack';
import { MaterialIcons } from '@expo/vector-icons';

// Import screens
import HomeScreen from '../screens/HomeScreen';
import CustomersScreen from '../screens/CustomersScreen';
import WeeklyPlanScreen from '../screens/WeeklyPlanScreen';

// Import visit screens
import VisitListScreen from '../screens/visits/VisitListScreen.js';
import VisitDetailScreen from '../screens/visits/VisitDetailScreen.js';
import VisitCheckInScreen from '../screens/visits/VisitCheckInScreen.js';
import VisitCheckOutScreen from '../screens/visits/VisitCheckOutScreen.js';
import CreateVisitScreen from '../screens/visits/CreateVisitScreen.js';
import VisitStatisticsScreen from '../screens/visits/VisitStatisticsScreen.js';
import PendingVisitsScreen from '../screens/visits/PendingVisitsScreen.js';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Visit Stack Navigator
const VisitStackNavigator = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="VisitList" 
        component={VisitListScreen}
        options={{ title: 'My Visits' }}
      />
      <Stack.Screen 
        name="VisitDetail" 
        component={VisitDetailScreen}
        options={{ title: 'Visit Details' }}
      />
      <Stack.Screen 
        name="VisitCheckIn" 
        component={VisitCheckInScreen}
        options={{ title: 'Check In' }}
      />
      <Stack.Screen 
        name="VisitCheckOut" 
        component={VisitCheckOutScreen}
        options={{ title: 'Check Out' }}
      />
      <Stack.Screen 
        name="CreateVisit" 
        component={CreateVisitScreen}
        options={{ title: 'Plan Visit' }}
      />
      <Stack.Screen 
        name="VisitStatistics" 
        component={VisitStatisticsScreen}
        options={{ title: 'Visit Statistics' }}
      />
      <Stack.Screen 
        name="PendingVisits" 
        component={PendingVisitsScreen}
        options={{ title: 'Pending Visits' }}
      />
    </Stack.Navigator>
  );
};

// Main Tab Navigator
const MainNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({focused, color, size}) => {
          let iconName;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Visits':
              iconName = 'assignment';
              break;
            case 'Customers':
              iconName = 'people';
              break;
            case 'Plans':
              iconName = 'calendar-today';
              break;
            default:
              iconName = 'circle';
              break;
          }

          return <MaterialIcons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#2196F3',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}>
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen 
        name="Visits" 
        component={VisitStackNavigator}
        options={{ title: 'Visits' }}
      />
      <Tab.Screen name="Customers" component={CustomersScreen} />
      <Tab.Screen 
        name="Plans" 
        component={WeeklyPlanScreen}
        options={{ title: 'Weekly Plan' }}
      />
    </Tab.Navigator>
  );
};

export default MainNavigator;
