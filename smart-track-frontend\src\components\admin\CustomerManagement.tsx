'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  Card, 
  Table, 
  Button, 
  Input, 
  Space, 
  Typography, 
  Avatar, 
  Tag, 
  Badge, 
  Tooltip, 
  Popconfirm, 
  message, 
  Spin, 
  Empty, 
  Row, 
  Col, 
  Divider 
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ReloadOutlined, 
  UserOutlined, 
  SearchOutlined 
} from '@ant-design/icons';

import { apiService } from '../../services/api';
import { Customer } from '../../types';
import AddCustomerModal from './AddCustomerModal';
import EditCustomerModal from './EditCustomerModal';
import DeleteCustomerModal from './DeleteCustomerModal';

const { Title } = Typography;
const { Search } = Input;

export default function CustomerManagement() {
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  const handleOpenModal = useCallback(() => {
    console.log('🎯 Add New Customer button clicked');
    setIsAddModalOpen(true);
  }, []);

  // Move useQuery before the callbacks that use refetch
  const { data: customersData, isLoading, refetch } = useQuery({
    queryKey: ['customers', page],
    queryFn: () => apiService.getCustomers(page, 10),
    staleTime: 0, // Always consider data stale
    gcTime: 0, // Don't cache data
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: false, // Don't refetch on window focus
  });

  // Backend returns array directly, not wrapped in data property
  const customers = customersData?.data || [];
  console.log('📊 Raw customers data received:', customersData);
  console.log('📊 Extracted customers array:', customers);
  console.log('📊 Number of customers:', customers.length);
  console.log('📊 Current page:', page);
  
  // Track when customer data changes
  useEffect(() => {
    if (customers.length > 0) {
      console.log('📊 Customer data updated:', customers.map(c => ({ id: c.id, name: c.name, is_active: c.is_active })));
    }
  }, [customers]);
  
  const pageSize = 10;
  const hasNextPage = customers.length === pageSize;
  const hasPrevPage = page > 1;

  const handleCustomerCreated = useCallback(() => {
    console.log('🎉 Customer creation success callback triggered');
    console.log('🔄 Forcing page reset to 1 and refetch');
    setPage(1);
    setTimeout(() => {
      refetch();
    }, 100); // Small delay to ensure the backend has processed the creation
    message.success('Customer created successfully!');
  }, [refetch]);

  const handleCloseModal = useCallback(() => {
    console.log('🔒 Closing Add Customer modal');
    setIsAddModalOpen(false);
    setPage(1);
    refetch();
  }, [refetch]);

  const handleEditCustomer = useCallback((customer: Customer) => {
    setSelectedCustomer(customer);
    setIsEditModalOpen(true);
  }, []);

  const handleCloseEditModal = useCallback(async () => {
    console.log('🔄 Closing edit modal and refetching data...');
    setIsEditModalOpen(false);
    setSelectedCustomer(null);
    
    // Force a fresh refetch after closing edit modal
    const result = await refetch();
    console.log('🔄 Refetch completed after edit modal close:', result.data?.data?.length, 'customers loaded');
  }, [refetch]);

  const handleDeleteCustomer = useCallback((customer: Customer) => {
    setSelectedCustomer(customer);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(false);
    setSelectedCustomer(null);
    refetch();
    message.success('Customer deleted successfully!');
  }, [refetch]);

  const handleRefresh = useCallback(async () => {
    console.log('🔄 Manual refresh triggered');
    console.log('🕐 Current time:', new Date().toISOString());
    
    try {
      // First, clear the cache completely
      queryClient.removeQueries({ 
        queryKey: ['customers'],
        exact: false 
      });
      
      // Then trigger a fresh refetch
      const result = await refetch();
      console.log('🔄 Refetch result:', result);
      
      message.success('Data refreshed!');
    } catch (error) {
      console.error('❌ Refresh error:', error);
      message.error('Failed to refresh data');
    }
  }, [refetch, queryClient]);

  const handleTestAPI = useCallback(async () => {
    console.log('🧪 Testing API directly...');
    try {
      const result = await apiService.getCustomers(1, 10);
      console.log('🧪 Direct API result:', result);
      console.log('🧪 Number of customers from API:', result.data.length);
      message.info(`API returned ${result.data.length} customers`);
    } catch (error) {
      console.error('❌ Direct API test failed:', error);
      message.error('Direct API test failed');
    }
  }, []);

  // Filter customers based on search term
  const filteredCustomers = customers.filter(customer => 
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (customer.city && customer.city.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Define table columns
  const columns = [
    {
      title: 'Customer',
      key: 'customer',
      render: (_: any, customer: Customer) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            size={40} 
            style={{ backgroundColor: '#1890ff', marginRight: 12 }}
            icon={<UserOutlined />}
          >
            {customer.name.charAt(0).toUpperCase()}
          </Avatar>
          <div>
            <div style={{ fontWeight: 500, marginBottom: 2 }}>{customer.name}</div>
            <div style={{ color: '#666', fontSize: '12px' }}>{customer.code}</div>
            <div style={{ color: '#999', fontSize: '11px' }}>{customer.city || 'No city'}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Contact',
      key: 'contact',
      render: (_: any, customer: Customer) => (
        <div>
          <div style={{ marginBottom: 2 }}>{customer.phone || 'No phone'}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>{customer.email || 'No email'}</div>
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'customer_type',
      key: 'customer_type',
      render: (type: string) => (
        <Tag color="blue">
          {type || 'No Type'}
        </Tag>
      ),
    },
    {
      title: 'Location',
      key: 'location',
      render: (_: any, customer: Customer) => (
        <div>
          <div>{customer.city || 'No city'}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>{customer.province || 'No province'}</div>
        </div>
      ),
    },
    {
      title: 'Credit',
      key: 'credit',
      render: (_: any, customer: Customer) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            ${(customer.credit_limit || 0).toLocaleString()}
          </div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            Balance: ${(customer.current_balance || 0).toLocaleString()}
          </div>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'status',
      render: (isActive: boolean) => (
        <Badge 
          status={isActive ? 'success' : 'error'} 
          text={isActive ? 'Active' : 'Inactive'}
        />
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, customer: Customer) => (
        <Space size="small">
          <Tooltip title="Edit customer">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEditCustomer(customer)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="Delete customer">
            <Popconfirm
              title="Are you sure you want to delete this customer?"
              description="This action cannot be undone."
              onConfirm={() => handleDeleteCustomer(customer)}
              okText="Yes, Delete"
              cancelText="Cancel"
              okButtonProps={{ danger: true }}
            >
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  if (isLoading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '60px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16, color: '#666' }}>Loading customers...</div>
        </div>
      </Card>
    );
  }

  return (
    <div>
      <Card 
        title={
          <Title level={3} style={{ margin: 0 }}>
            Customer Management
          </Title>
        }
        extra={
          <Space>
            <Tooltip title="Test API directly">
              <Button 
                onClick={handleTestAPI}
                type="default"
              >
                Test API
              </Button>
            </Tooltip>
            <Tooltip title="Refresh data">
              <Button 
                icon={<ReloadOutlined />} 
                onClick={handleRefresh}
                loading={isLoading}
              >
                Refresh
              </Button>
            </Tooltip>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleOpenModal}
            >
              Add New Customer
            </Button>
          </Space>
        }
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* Search and Info Section */}
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="Search customers..."
                allowClear
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                prefix={<SearchOutlined />}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={12} md={16}>
              <div style={{ textAlign: 'right', color: '#666', fontSize: '14px' }}>
                {isLoading ? (
                  'Loading customers...'
                ) : (
                  `Showing ${customers.length} customer(s) on page ${page}`
                )}
              </div>
            </Col>
          </Row>

          <Divider style={{ margin: '12px 0' }} />

          {/* Table Section */}
          {filteredCustomers.length === 0 ? (
            <Empty
              description={
                searchTerm 
                  ? `No customers found matching "${searchTerm}"` 
                  : "No customers found"
              }
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              {!searchTerm && (
                <Button type="primary" icon={<PlusOutlined />} onClick={handleOpenModal}>
                  Add First Customer
                </Button>
              )}
            </Empty>
          ) : (
            <>
              <Table
                columns={columns}
                dataSource={filteredCustomers}
                rowKey="id"
                pagination={false}
                scroll={{ x: 800 }}
                size="middle"
              />

              {/* Custom Pagination */}
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center', 
                marginTop: 16 
              }}>
                <div style={{ color: '#666', fontSize: '14px' }}>
                  Showing {((page - 1) * pageSize) + 1} to{' '}
                  {Math.min(page * pageSize, ((page - 1) * pageSize) + filteredCustomers.length)} of{' '}
                  {searchTerm ? `${filteredCustomers.length} filtered` : 'many'} customers
                </div>
                <Space>
                  <Button 
                    disabled={!hasPrevPage} 
                    onClick={() => setPage(Math.max(1, page - 1))}
                  >
                    Previous
                  </Button>
                  <span style={{ 
                    padding: '0 16px', 
                    color: '#1890ff', 
                    fontWeight: 500 
                  }}>
                    Page {page}
                  </span>
                  <Button 
                    disabled={!hasNextPage} 
                    onClick={() => setPage(page + 1)}
                  >
                    Next
                  </Button>
                </Space>
              </div>
            </>
          )}
        </Space>
      </Card>

      {/* Modals */}
      <AddCustomerModal 
        isOpen={isAddModalOpen} 
        onClose={handleCloseModal}
        onSuccess={handleCustomerCreated}
      />
      
      <EditCustomerModal 
        isOpen={isEditModalOpen} 
        onClose={handleCloseEditModal}
        customer={selectedCustomer}
      />
      
      <DeleteCustomerModal 
        isOpen={isDeleteModalOpen} 
        onClose={handleCloseDeleteModal}
        customer={selectedCustomer}
      />
    </div>
  );
}
