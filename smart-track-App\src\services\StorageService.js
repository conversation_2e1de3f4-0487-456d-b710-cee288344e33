import AsyncStorage from '@react-native-async-storage/async-storage';

// Keys for AsyncStorage
const STORAGE_KEYS = {
  USER_TOKEN: 'userToken',
  USER_PHONE: 'userPhone',
  VISIT_DATA: 'visitData',
  WEEKLY_PLAN: 'weeklyPlan',
  APP_SETTINGS: 'appSettings',
};

class StorageService {
  // Generic methods
  static async setItem(key, value) {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, jsonValue);
      return true;
    } catch (error) {
      console.error('Error storing data:', error);
      return false;
    }
  }

  static async getItem(key) {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      return jsonValue != null ? JSON.parse(jsonValue) : null;
    } catch (error) {
      console.error('Error retrieving data:', error);
      return null;
    }
  }

  static async removeItem(key) {
    try {
      await AsyncStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Error removing data:', error);
      return false;
    }
  }

  // User authentication
  static async storeUserData(token, phone) {
    const results = await Promise.all([
      this.setItem(STORAGE_KEYS.USER_TOKEN, token),
      this.setItem(STORAGE_KEYS.USER_PHONE, phone),
    ]);
    return results.every(result => result === true);
  }

  static async getUserToken() {
    return await this.getItem(STORAGE_KEYS.USER_TOKEN);
  }

  static async getUserPhone() {
    return await this.getItem(STORAGE_KEYS.USER_PHONE);
  }

  static async logout() {
    const results = await Promise.all([
      this.removeItem(STORAGE_KEYS.USER_TOKEN),
      this.removeItem(STORAGE_KEYS.USER_PHONE),
    ]);
    return results.every(result => result === true);
  }

  // Visit data management
  static async storeVisitData(visitData) {
    const existingData = await this.getItem(STORAGE_KEYS.VISIT_DATA) || [];
    const newData = [...existingData, { ...visitData, id: Date.now() }];
    return await this.setItem(STORAGE_KEYS.VISIT_DATA, newData);
  }

  static async getVisitData() {
    return await this.getItem(STORAGE_KEYS.VISIT_DATA) || [];
  }

  static async updateVisitData(visitId, updatedData) {
    const existingData = await this.getItem(STORAGE_KEYS.VISIT_DATA) || [];
    const updatedList = existingData.map(visit =>
      visit.id === visitId ? { ...visit, ...updatedData } : visit
    );
    return await this.setItem(STORAGE_KEYS.VISIT_DATA, updatedList);
  }

  // Weekly plan management
  static async storeWeeklyPlan(planData) {
    return await this.setItem(STORAGE_KEYS.WEEKLY_PLAN, planData);
  }

  static async getWeeklyPlan() {
    return await this.getItem(STORAGE_KEYS.WEEKLY_PLAN);
  }

  // App settings
  static async storeAppSettings(settings) {
    return await this.setItem(STORAGE_KEYS.APP_SETTINGS, settings);
  }

  static async getAppSettings() {
    const defaultSettings = {
      notifications: true,
      theme: 'light',
      autoSync: true,
      language: 'en',
    };
    const storedSettings = await this.getItem(STORAGE_KEYS.APP_SETTINGS);
    return { ...defaultSettings, ...storedSettings };
  }

  // Clear all data (for debugging or reset)
  static async clearAllData() {
    try {
      await AsyncStorage.clear();
      return true;
    } catch (error) {
      console.error('Error clearing all data:', error);
      return false;
    }
  }
}

export default StorageService;
export { STORAGE_KEYS };
