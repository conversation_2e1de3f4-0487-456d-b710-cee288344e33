import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Text,
  Card,
  Title,
  Searchbar,
  Chip,
  Avatar,
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { customerService } from '../services/customerService';

const CustomersScreen = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [customers, setCustomers] = useState([]);

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await customerService.getCustomers();
      
      if (response && Array.isArray(response)) {
        // Transform API response to match the expected format
        const transformedCustomers = response.map(customer => ({
          id: customer.id,
          name: customer.name,
          type: customer.customer_type || '',
          location: customer.city || customer.address || 'Unknown',
          phone: customer.phone || 'N/A',
          lastVisit: formatLastVisit(customer.last_visit_date),
          status: customer.is_active ? 'Active' : 'Inactive',
          revenue: customer.revenue_ytd || 0,
          email: customer.email,
          creditLimit: customer.credit_limit || 0,
          currentBalance: customer.current_balance || 0,
          address: customer.address,
          city: customer.city,
          region: customer.region,
        }));
        
        setCustomers(transformedCustomers);
      } else {
        console.warn('No customers data received from API');
        // Don't use fallback data - set empty array
        setCustomers([]);
      }
    } catch (error) {
      console.error('Failed to load customers:', error);
      setError('Failed to load customers. No offline data available.');
      
      // Don't use fallback data - set empty array
      setCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  const formatLastVisit = (dateString) => {
    if (!dateString) return 'Never';
    
    const visitDate = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - visitDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  };

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await loadCustomers();
    setRefreshing(false);
  }, []);

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         customer.location.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSearch;
  });

  const getStatusColor = (status) => {
    return status === 'Active' ? '#4CAF50' : '#FF9800';
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Title style={styles.headerTitle}>Customers</Title>
        <Text style={styles.customerCount}>
          {loading ? 'Loading...' : `${filteredCustomers.length} customer${filteredCustomers.length !== 1 ? 's' : ''}`}
        </Text>
        {error && (
          <Text style={styles.errorText}>{error}</Text>
        )}
      </View>

      <View style={styles.filters}>
        <Searchbar
          placeholder="Search customers..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {filteredCustomers.map((customer) => (
          <Card key={customer.id} style={styles.customerCard}>
            <Card.Content>
              <View style={styles.customerHeader}>
                <View style={styles.customerInfo}>
                  <Avatar.Text
                    size={48}
                    label={customer.name.substring(0, 2).toUpperCase()}
                    style={styles.avatar}
                  />
                  <View style={styles.customerDetails}>
                    <Text style={styles.customerName}>{customer.name}</Text>
                    <Text style={styles.customerType}>{customer.type}</Text>
                    <View style={styles.locationRow}>
                      <Ionicons name="location" size={16} color="#666" />
                      <Text style={styles.location}>{customer.location}</Text>
                    </View>
                  </View>
                </View>
                <View style={styles.statusContainer}>
                  <Chip
                    style={[styles.enhancedStatusChip, { backgroundColor: getStatusColor(customer.status) }]}
                    textStyle={styles.enhancedStatusText}
                    icon={() => (
                      <Ionicons 
                        name={customer.status === 'Active' ? "checkmark-circle" : "close-circle"} 
                        size={16} 
                        color="#FFFFFF" 
                      />
                    )}
                  >
                    {customer.status}
                  </Chip>
                </View>
              </View>

              <View style={styles.customerMeta}>
                <View style={styles.metaItem}>
                  <Text style={styles.metaLabel}>Last Visit</Text>
                  <Text style={styles.metaValue}>{customer.lastVisit}</Text>
                </View>
                <View style={styles.metaItem}>
                  <Text style={styles.metaLabel}>Phone</Text>
                  <Text style={styles.metaValue}>{customer.phone}</Text>
                </View>
              </View>
            </Card.Content>
          </Card>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    paddingTop: 10,
    elevation: 2,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  customerCount: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  errorText: {
    fontSize: 12,
    color: '#ff6b6b',
    marginTop: 3,
    fontStyle: 'italic',
  },
  filters: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingBottom: 15,
  },
  searchbar: {
    marginBottom: 15,
    elevation: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 15,
  },
  customerCard: {
    marginTop: 15,
    elevation: 3,
  },
  customerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  customerInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  avatar: {
    backgroundColor: '#2196F3',
  },
  customerDetails: {
    marginLeft: 15,
    flex: 1,
  },
  customerName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 3,
  },
  customerType: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  location: {
    marginLeft: 5,
    color: '#666',
    fontSize: 14,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusChip: {
    height: 28,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
  },
  enhancedStatusChip: {
    height: 32,
    paddingHorizontal: 12,
  },
  enhancedStatusText: {
    color: '#fff',
    fontSize: 13,
    fontWeight: 'bold',
  },
  customerMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 15,
  },
  metaItem: {
    alignItems: 'center',
    flex: 1,
  },
  metaLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 3,
  },
  metaValue: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default CustomersScreen;
