'use client';

import { User } from '@/types';
import { useAuthStore } from '@/stores/auth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { 
  LogOut, 
  User as UserIcon, 
  Moon, 
  Sun,
  ChevronDown,
  Shield,
  Clock
} from 'lucide-react';
import { 
  Dropdown, 
  Avatar, 
  Switch, 
  Tooltip,
  Button as AntButton,
  Space,
  Divider
} from 'antd';
import type { MenuProps } from 'antd';

interface DashboardLayoutProps {
  children: React.ReactNode;
  user: User;
  title: string;
}

export default function DashboardLayout({ children, user, title }: DashboardLayoutProps) {
  const { logout } = useAuthStore();
  const router = useRouter();
  const { isDark, toggleTheme } = useTheme();

  const handleLogout = () => {
    // Enhanced logout confirmation
    const confirmed = window.confirm('Are you sure you want to logout? Any unsaved changes will be lost.');
    if (confirmed) {
      // Clear all authentication data and cache
      logout();
      // Navigate to login page
      router.push('/login');
    }
  };

  const toggleDarkMode = () => {
    toggleTheme();
  };

  const handleProfileClick = () => {
    router.push('/profile');
  };

  // User dropdown menu items
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserIcon className="w-4 h-4" />,
      label: 'Profile',
      onClick: handleProfileClick,
    },
    {
      type: 'divider',
    },
    {
      key: 'theme',
      icon: isDark ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />,
      label: (
        <div className="flex items-center justify-between w-full">
          <span>Dark Mode</span>
          <Switch 
            size="small" 
            checked={isDark} 
            onChange={(checked, e) => {
              e?.stopPropagation();
              toggleDarkMode();
            }}
          />
        </div>
      ),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogOut className="w-4 h-4" />,
      label: 'Logout',
      onClick: handleLogout,
      className: 'text-red-600 hover:text-red-700 hover:bg-red-50',
    },
  ];

  // Add keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + L for logout
      if ((event.ctrlKey || event.metaKey) && event.key === 'l') {
        event.preventDefault();
        handleLogout();
      }
      // Ctrl/Cmd + D for dark mode toggle
      if ((event.ctrlKey || event.metaKey) && event.key === 'd') {
        event.preventDefault();
        toggleDarkMode();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isDark]);

  const getRoleIcon = (user: User) => {
    const roleName = user.role?.name?.toLowerCase();
    const roleType = user.profile?.role_type;
    
    if (roleName === 'admin') {
      return <Shield className="w-4 h-4 text-red-500" />;
    }
    return <UserIcon className="w-4 h-4 text-blue-500" />;
  };

  const getRoleBadgeColor = (user: User) => {
    const roleName = user.role?.name?.toLowerCase();
    const roleType = user.profile?.role_type;
    
    if (roleName === 'admin') return 'bg-red-100 text-red-800';
    if (roleType === 'manager') return 'bg-purple-100 text-purple-800';
    if (roleType === 'supervisor') return 'bg-blue-100 text-blue-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className={`h-screen flex flex-col ${isDark ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      {/* Enhanced Header/Navigation Bar */}
      <header className={`${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} shadow-lg border-b transition-all duration-200`}>
        <div className="px-4 lg:px-6 py-3">
          <div className="flex items-center justify-between">
            
            {/* Left Section - Logo and Title */}
            <div className="flex items-center space-x-4">
              {/* Logo/Brand */}
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm font-bold">ST</span>
                </div>
                <div>
                  <h1 className={`text-xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                    {title}
                  </h1>
                  <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'} hidden lg:block`}>
                    Smart Track Dashboard
                  </p>
                </div>
              </div>
            </div>

            {/* Right Section - User Actions */}
            <div className="flex items-center space-x-2 lg:space-x-4">
              
              {/* User Profile Section */}
              <div className="flex items-center space-x-3">
                {/* User Info - Hidden on small screens */}
                <div className="hidden md:block text-right">
                  <div className="flex items-center space-x-2">
                    <div>
                      <p className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                        {user.full_name}
                      </p>
                      <div className="flex items-center space-x-1">
                        {getRoleIcon(user)}
                        <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${getRoleBadgeColor(user)}`}>
                          {user.role?.name || user.profile?.role_type}
                        </span>
                      </div>
                    </div>
                    <Clock className={`w-3 h-3 ${isDark ? 'text-gray-400' : 'text-gray-500'}`} />
                  </div>
                </div>

                {/* User Avatar with Dropdown */}
                <Dropdown
                  menu={{ items: userMenuItems }}
                  trigger={['click']}
                  placement="bottomRight"
                  arrow={{ pointAtCenter: true }}
                >
                  <div className="flex items-center space-x-2 cursor-pointer p-2 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <Avatar
                      size={40}
                      className="bg-gradient-to-r from-blue-500 to-purple-600 border-2 border-white shadow-sm"
                    >
                      {user.full_name.charAt(0).toUpperCase()}
                    </Avatar>
                    <ChevronDown className={`w-4 h-4 ${isDark ? 'text-gray-400' : 'text-gray-500'} hidden sm:block`} />
                  </div>
                </Dropdown>

                {/* Quick Logout Button - Desktop Only */}
                <Tooltip title="Quick Logout (Ctrl+L)">
                  <AntButton
                    type="text"
                    danger
                    icon={<LogOut className="w-4 h-4" />}
                    onClick={handleLogout}
                    className="hidden lg:flex items-center space-x-1 border-none"
                  >
                    <span className="hidden xl:inline">Logout</span>
                  </AntButton>
                </Tooltip>
              </div>
            </div>
          </div>

          {/* Welcome Message - Mobile */}
          <div className={`mt-2 lg:hidden ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
            <p className="text-sm">Welcome back, <span className="font-medium">{user.full_name}</span></p>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className={`flex-1 overflow-auto ${isDark ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="h-full">
          {children}
        </div>
      </main>
    </div>
  );
}
