import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as Location from 'expo-location';
import { visitService } from '../../services/visitService';

const VisitCheckOutScreen = ({ route, navigation }) => {
  const { visitId } = route.params;
  const [visit, setVisit] = useState(null);
  const [loading, setLoading] = useState(true);
  const [checkingOut, setCheckingOut] = useState(false);
  const [outcome, setOutcome] = useState('');
  const [nextAction, setNextAction] = useState('');
  const [notes, setNotes] = useState('');

  useEffect(() => {
    loadVisit();
  }, []);

  const loadVisit = async () => {
    try {
      const visitData = await visitService.getVisit(visitId);
      setVisit(visitData);
    } catch (error) {
      console.error('Error loading visit:', error);
      Alert.alert('Error', 'Failed to load visit details');
    } finally {
      setLoading(false);
    }
  };

  const handleCheckOut = async () => {
    try {
      setCheckingOut(true);

      // Get current location
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required for check-out');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      
      const checkOutData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        outcome: outcome,
        next_action: nextAction,
      };

      const result = await visitService.checkOutVisit(visitId, checkOutData);
      
      Alert.alert(
        'Success',
        'Successfully checked out from visit',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Check out error:', error);
      Alert.alert('Error', `Failed to check out: ${error.message}`);
    } finally {
      setCheckingOut(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading visit details...</Text>
      </View>
    );
  }

  if (!visit) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Visit not found</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
        <View style={styles.visitInfo}>
          <Text style={styles.sectionTitle}>Visit Information</Text>
          <Text style={styles.visitDetail}>Customer: {visit.customer?.name || 'N/A'}</Text>
          <Text style={styles.visitDetail}>Purpose: {visit.purpose}</Text>
          <Text style={styles.visitDetail}>Status: {visit.status}</Text>
          {visit.checkin_latitude && (
            <Text style={styles.visitDetail}>
              Check-in Location: {visit.checkin_latitude.toFixed(6)}, {visit.checkin_longitude.toFixed(6)}
            </Text>
          )}
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Check-Out Details</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Visit Outcome</Text>
            <TextInput
              style={styles.textInput}
              value={outcome}
              onChangeText={setOutcome}
              placeholder="e.g., Order placed, Follow-up required, etc."
              multiline
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Next Action</Text>
            <TextInput
              style={styles.textInput}
              value={nextAction}
              onChangeText={setNextAction}
              placeholder="e.g., Schedule follow-up call, Send quotation, etc."
              multiline
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Additional Notes</Text>
            <TextInput
              style={styles.textArea}
              value={notes}
              onChangeText={setNotes}
              placeholder="Any additional observations or notes..."
              multiline
              numberOfLines={4}
            />
          </View>
        </View>

        <TouchableOpacity
          style={[styles.checkOutButton, checkingOut && styles.disabledButton]}
          onPress={handleCheckOut}
          disabled={checkingOut}
        >
          {checkingOut ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.checkOutButtonText}>Complete Check-Out</Text>
          )}
        </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    color: '#ff3b30',
  },
  visitInfo: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  visitDetail: {
    fontSize: 14,
    marginBottom: 8,
    color: '#666',
  },
  formSection: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    minHeight: 60,
    textAlignVertical: 'top',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    minHeight: 100,
    textAlignVertical: 'top',
  },
  checkOutButton: {
    backgroundColor: '#ff3b30',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  checkOutButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default VisitCheckOutScreen;
