'use client';

import { useState } from 'react';
import { User } from '@/types';
import DashboardLayout from '@/components/layout/DashboardLayout';
import StatsCards from '@/components/dashboard/StatsCards';
import ManagerOverview from '@/components/manager/ManagerOverview';
import SupervisorManagement from '@/components/manager/SupervisorManagement';
import PerformanceAnalytics from '@/components/manager/PerformanceAnalytics';

interface ManagerDashboardProps {
  user: User;
}

type ManagerTab = 'overview' | 'supervisors' | 'analytics' | 'reports';

export default function ManagerDashboard({ user }: ManagerDashboardProps) {
  const [activeTab, setActiveTab] = useState<ManagerTab>('overview');

  const managerTabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'supervisors', label: 'Supervisors', icon: '👨‍💼' },
    { id: 'analytics', label: 'Analytics', icon: '📈' },
    { id: 'reports', label: 'Reports', icon: '📋' },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <StatsCards />
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold mb-4">Region Performance</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Cairo Region</span>
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '88%' }}></div>
                      </div>
                      <span className="text-sm text-gray-600">88%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Giza Region</span>
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '82%' }}></div>
                      </div>
                      <span className="text-sm text-gray-600">82%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Alexandria Region</span>
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '75%' }}></div>
                      </div>
                      <span className="text-sm text-gray-600">75%</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold mb-4">Supervisor Status</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Active Supervisors</span>
                    <span className="text-lg font-bold text-green-600">12</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Total Sales Reps</span>
                    <span className="text-lg font-bold text-blue-600">45</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">On Field</span>
                    <span className="text-lg font-bold text-orange-600">32</span>
                  </div>
                </div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold mb-4">Today's Highlights</h3>
                <div className="space-y-3">
                  <div className="p-3 bg-green-50 rounded border-l-4 border-green-400">
                    <p className="text-sm text-green-800">15 visits completed</p>
                  </div>
                  <div className="p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                    <p className="text-sm text-blue-800">8 new customers added</p>
                  </div>
                  <div className="p-3 bg-yellow-50 rounded border-l-4 border-yellow-400">
                    <p className="text-sm text-yellow-800">3 pending approvals</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'supervisors':
        return <SupervisorManagement managerId={user.id} />;
      case 'analytics':
        return <PerformanceAnalytics managerId={user.id} />;
      case 'reports':
        return (
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">Reports</h3>
            <p className="text-gray-600">Reports functionality coming soon...</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <DashboardLayout user={user} title="Manager Dashboard">
      <div className="flex h-full">
        {/* Sidebar Navigation */}
        <div className="w-64 bg-white shadow-sm border-r">
          <nav className="mt-8">
            {managerTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as ManagerTab)}
                className={`w-full flex items-center px-6 py-3 text-left transition-colors ${
                  activeTab === tab.id
                    ? 'bg-indigo-50 text-indigo-700 border-r-2 border-indigo-700'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <span className="mr-3">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8 overflow-auto">
          {renderContent()}
        </div>
      </div>
    </DashboardLayout>
  );
}
