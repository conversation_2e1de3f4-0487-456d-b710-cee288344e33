import React from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import {PaperProvider} from 'react-native-paper';
import {QueryClient, QueryClientProvider} from '@tanstack/react-query';
import {View, ActivityIndicator} from 'react-native';

import AuthNavigator from './navigation/AuthNavigator.js';
import MainNavigator from './navigation/MainNavigator.js';
import {useAuthStore} from './stores/authStore.js';
import theme from './theme/theme.js';

const Stack = createStackNavigator();
const queryClient = new QueryClient();

const App = () => {
  const {isAuthenticated, _hasHydrated, setHasHydrated} = useAuthStore();

  // Fallback hydration check
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (!_hasHydrated) {
        console.log('⚠️ Forcing hydration after timeout');
        setHasHydrated(true);
      }
    }, 3000); // 3 second timeout

    return () => clearTimeout(timer);
  }, [_hasHydrated, setHasHydrated]);

  // Debug logging
  React.useEffect(() => {
    console.log('🚀 App state:', { isAuthenticated, _hasHydrated });
  }, [isAuthenticated, _hasHydrated]);

  // Show loading screen while hydrating
  if (!_hasHydrated) {
    console.log('⏳ Waiting for hydration...');
    return (
      <PaperProvider theme={theme}>
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      </PaperProvider>
    );
  }

  console.log(`🧭 Navigation: ${isAuthenticated ? 'MainNavigator' : 'AuthNavigator'}`);

  return (
    <QueryClientProvider client={queryClient}>
      <PaperProvider theme={theme}>
        <NavigationContainer>
          {isAuthenticated ? <MainNavigator /> : <AuthNavigator />}
        </NavigationContainer>
      </PaperProvider>
    </QueryClientProvider>
  );
};

export default App;
