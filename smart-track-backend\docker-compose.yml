

services:
  postgres:
    image: postgres:15
    container_name: smart_track_postgres
    environment:
      POSTGRES_USER: smart_track
      POSTGRES_PASSWORD: password
      POSTGRES_DB: smart_track_db
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - smart_track_network


  # Uncomment the following lines to enable Redis service
  # Note: We are currently not using Redis in the application, but it can be enabled if needed.
  # If you decide to use Redis, make sure to update the REDIS_URL in your configuration file (app/core/config.py) and in .env accordingly.
  # redis:
  #   image: redis:7-alpine
  #   container_name: smart_track_redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - smart_track_network

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: smart_track_backend
    ports:
      - "8004:8000"
    env_file:
      - .env
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - DATABASE_HOSTNAME=postgres
      - DATABASE_PORT=5432
    depends_on:
      - postgres
      # - redis
    volumes:
      - ./uploads:/home/<USER>/src/uploads
      - .:/home/<USER>/src
    networks:
      - smart_track_network

volumes:
  postgres_data:
  redis_data:

networks:
  smart_track_network:
    driver: bridge
