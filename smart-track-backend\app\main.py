from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.database import engine, Base
from app.api.v1.router import api_router
from app.core.security import get_current_user

# Create database tables
# Base.metadata.create_all(bind=engine)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("Starting Smart Track API...")
    yield
    # Shutdown
    print("Shutting down Smart Track API...")

# Initialize FastAPI app
app = FastAPI(
    title="Smart Track API",
    description="Sales Force Automation API for Smart Track Mobile Application",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.parsed_allowed_origins,  # Use origins from environment
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix="/api/v1")

# Debug: Print all registered routes
print("DEBUG: Registered routes:")
for route in app.routes:
    print(f"  {route.path} [{getattr(route, 'methods', 'N/A')}]")

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Smart Track API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
 
