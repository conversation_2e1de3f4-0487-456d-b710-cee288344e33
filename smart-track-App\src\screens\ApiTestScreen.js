import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { <PERSON><PERSON>, Card, Title, Paragraph } from 'react-native-paper';
import { api } from '../services/api';

const ApiTestScreen = () => {
  const [testResults, setTestResults] = useState(null);
  const [testing, setTesting] = useState(false);

  const runConnectivityTest = async () => {
    setTesting(true);
    setTestResults(null);

    try {
      console.log('Starting API connectivity test...');
      
      // Test 1: Basic connection
      const connectionTest = await api.testConnection();
      
      // Test 2: Login test
      let loginTest = { success: false, error: 'Not tested' };
      try {
        await api.login('admin', 'admin123');
        loginTest = { success: true };
      } catch (error) {
        loginTest = { 
          success: false, 
          error: error.message || 'Login failed'
        };
      }

      const results = {
        connection: connectionTest,
        login: loginTest,
        baseURL: api.baseURL,
        timestamp: new Date().toLocaleString()
      };

      setTestResults(results);
      
      if (connectionTest.success && loginTest.success) {
        Alert.alert('Success!', 'Backend connection and login both working correctly.');
      } else {
        Alert.alert('Test Results', 'Some tests failed. Check the details below.');
      }
      
    } catch (error) {
      console.error('Test error:', error);
      Alert.alert('Error', `Test failed: ${error.message}`);
    } finally {
      setTesting(false);
    }
  };

  const getStatusColor = (success) => success ? '#4CAF50' : '#F44336';
  const getStatusText = (success) => success ? 'PASS' : 'FAIL';

  return (
    <SafeAreaView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>API Connectivity Test</Title>
          <Paragraph>Test connection to the backend server</Paragraph>
          
          {testResults && (
            <View style={styles.results}>
              <Text style={styles.resultTitle}>Test Results:</Text>
              <Text style={styles.baseUrl}>Base URL: {testResults.baseURL}</Text>
              <Text style={styles.timestamp}>Time: {testResults.timestamp}</Text>
              
              <View style={styles.testResult}>
                <Text style={styles.testName}>Backend Connection:</Text>
                <Text style={[styles.testStatus, { color: getStatusColor(testResults.connection.success) }]}>
                  {getStatusText(testResults.connection.success)}
                </Text>
              </View>
              
              {!testResults.connection.success && (
                <Text style={styles.errorText}>Error: {testResults.connection.error}</Text>
              )}
              
              <View style={styles.testResult}>
                <Text style={styles.testName}>Login Test:</Text>
                <Text style={[styles.testStatus, { color: getStatusColor(testResults.login.success) }]}>
                  {getStatusText(testResults.login.success)}
                </Text>
              </View>
              
              {!testResults.login.success && (
                <Text style={styles.errorText}>Error: {testResults.login.error}</Text>
              )}
            </View>
          )}
          
          <Button
            mode="contained"
            onPress={runConnectivityTest}
            loading={testing}
            disabled={testing}
            style={styles.button}
          >
            {testing ? 'Testing...' : 'Run Test'}
          </Button>
        </Card.Content>
      </Card>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  card: {
    marginBottom: 16,
  },
  button: {
    marginTop: 16,
  },
  results: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  baseUrl: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  timestamp: {
    fontSize: 12,
    color: '#666',
    marginBottom: 12,
  },
  testResult: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  testName: {
    fontSize: 14,
    fontWeight: '500',
  },
  testStatus: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  errorText: {
    fontSize: 12,
    color: '#F44336',
    marginLeft: 8,
    marginBottom: 8,
  },
});

export default ApiTestScreen;
