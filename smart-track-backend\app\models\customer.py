from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text, Float, DECIMAL
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class Customer(Base):
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(20), unique=True, index=True, nullable=False)
    name = Column(String(100), nullable=False)
    customer_group = Column(String(50))  # From CSV: Customer group
    address = Column(Text)
    city = Column(String(50))
    province = Column(String(50))  # From CSV: Province
    region = Column(String(50))    # From CSV: Region
    phone = Column(String(15))
    email = Column(String(100))
    customer_type = Column(String(20))  # 'dealer', 'distributor', 'retailer'
    credit_limit = Column(DECIMAL(12, 2), default=0.0)
    current_balance = Column(DECIMAL(12, 2), default=0.0)
    customer_statistics_group = Column(String(10))  # From CSV: Customer Statistics Group
    sdst = Column(String(10))  # From CSV: SDst
    
    # GPS Coordinates for location-based services
    latitude = Column(Float)
    longitude = Column(Float)
    
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    contacts = relationship("CustomerContact", back_populates="customer")
    visits = relationship("Visit", back_populates="customer")
    distribution_channels = relationship("CustomerDistributionChannel", back_populates="customer")
    branches = relationship("Branch", back_populates="customer")

class CustomerContact(Base):
    __tablename__ = "customer_contacts"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    name = Column(String(100), nullable=False)
    designation = Column(String(50))
    phone = Column(String(15))
    email = Column(String(100))
    is_primary = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    customer = relationship("Customer", back_populates="contacts")

class DistributionChannel(Base):
    __tablename__ = "distribution_channels"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)  # e.g., "FRESH Dist Chl.", "Cooling", "Colman & Filter"
    code = Column(String(20), unique=True, index=True, nullable=False)
    description = Column(Text)
    manager_id = Column(Integer, ForeignKey("users.id"))  # Each distribution channel has one manager
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    manager = relationship("User", foreign_keys=[manager_id])
    users = relationship("UserProfile", back_populates="distribution_channel")
    customer_distribution_channels = relationship("CustomerDistributionChannel", back_populates="distribution_channel")

class CustomerDistributionChannel(Base):
    __tablename__ = "customer_distribution_channels"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    distribution_channel_id = Column(Integer, ForeignKey("distribution_channels.id"), nullable=False)
    sales_rep_id = Column(Integer, ForeignKey("users.id"), nullable=False)  # One sales rep per customer per distribution channel
    manager_code = Column(String(20))  # From CSV: Manager Code
    is_active = Column(Boolean, default=True)
    assigned_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    customer = relationship("Customer", back_populates="distribution_channels")
    distribution_channel = relationship("DistributionChannel", back_populates="customer_distribution_channels")
    sales_rep = relationship("User")


class Branch(Base):
    __tablename__ = "branches"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    name = Column(String(100), nullable=False)
    address = Column(Text)
    city = Column(String(50))
    state = Column(String(50))
    region = Column(String(50))  # New region field
    phone = Column(String(15))
    latitude = Column(Float)  # GPS coordinates for branch location
    longitude = Column(Float)  # GPS coordinates for branch location
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    customer = relationship("Customer", back_populates="branches")
