import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StyleSheet, StatusBar, Platform } from 'react-native';

/**
 * SafeAreaWrapper - A reusable component that provides consistent safe area handling
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Child components to render
 * @param {Object} props.style - Additional styles to apply to the SafeAreaView
 * @param {Array<'top'|'bottom'|'left'|'right'>} props.edges - Which edges to apply safe area to (default: all)
 * @param {string} props.backgroundColor - Background color (default: '#f5f5f5')
 * @param {boolean} props.forceInsets - Force manual insets for problematic areas (default: false)
 */
const SafeAreaWrapper = ({ 
  children, 
  style, 
  edges = ['top', 'bottom', 'left', 'right'],
  backgroundColor = '#f5f5f5',
  forceInsets = false
}) => {
  // For screens that need special handling (like those with custom headers)
  const getContainerStyle = () => {
    const baseStyle = [
      styles.container,
      { backgroundColor },
      style
    ];

    // Add manual padding for status bar on Android if needed
    if (forceInsets && Platform.OS === 'android') {
      baseStyle.push({
        paddingTop: StatusBar.currentHeight || 24
      });
    }

    return baseStyle;
  };

  return (
    <SafeAreaView 
      style={getContainerStyle()}
      edges={edges}
    >
      {children}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default SafeAreaWrapper;
