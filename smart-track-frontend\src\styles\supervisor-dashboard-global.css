/* Global Ant Design Overrides for Supervisor Dashboard */

/* <PERSON>u <PERSON>ver Effects */
.ant-menu-item-selected {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%) !important;
  color: #1890ff !important;
  border-left: 3px solid #1890ff !important;
}

.ant-menu-item:hover {
  background: rgba(24, 144, 255, 0.08) !important;
  color: #1890ff !important;
}

/* Dropdown Enhancements */
.ant-dropdown {
  border-radius: 8px !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12) !important;
}

/* Card Enhancements */
.ant-card-head {
  border-radius: 8px 8px 0 0 !important;
}

/* Badge Enhancements */
.ant-badge-count {
  background: #ff4d4f !important;
  box-shadow: 0 2px 6px rgba(255, 77, 79, 0.4) !important;
}

/* Table Row Styling for Visit Tracking */
.ant-table-row-success {
  background-color: #f6ffed !important;
}

.ant-table-row-processing {
  background-color: #fffbe6 !important;
}

.ant-table-row-error {
  background-color: #fff2f0 !important;
}

/* Enhanced <PERSON><PERSON> Styling */
.ant-btn-primary {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
  transition: all 0.3s ease;
}

/* Enhanced Card Styling */
.ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* Enhanced Table Styling */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  border-bottom: 2px solid #f0f0f0;
}

/* Enhanced Input and Select Styling */
.ant-input, .ant-select-selector {
  border-radius: 8px;
}

.ant-input:focus, .ant-select-focused .ant-select-selector {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Loading Spinner Enhancement */
.ant-spin-dot-item {
  background-color: #1890ff;
}

/* Typography Enhancements */
.ant-typography h1, .ant-typography h2, .ant-typography h3, .ant-typography h4, .ant-typography h5 {
  color: #262626;
  font-weight: 600;
}

/* Avatar Enhancements */
.ant-avatar {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Tooltip Enhancements */
.ant-tooltip-inner {
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.85);
}
