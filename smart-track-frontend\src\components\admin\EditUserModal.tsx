'use client';

import { useState, useEffect } from 'react';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { apiService } from '@/services/api';
import { User } from '@/types';
import {
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Button,
  Row,
  Col,
  message,
  Divider,
  Typography,
  Space,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  IdcardOutlined,
  EditOutlined,
} from '@ant-design/icons';

const { Title } = Typography;
const { Option } = Select;

interface EditUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
}

interface UserFormData {
  sap_code: string;
  username: string;
  email: string;
  full_name: string;
  phone: string;
  role_id: number;
  is_active: boolean;
}

export default function EditUserModal({ isOpen, onClose, user }: EditUserModalProps) {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isActive, setIsActive] = useState(true); // Add state to track switch value

  // Fetch roles for dropdown
  const { data: rolesData, isLoading: rolesLoading } = useQuery({
    queryKey: ['roles'],
    queryFn: () => apiService.getRoles(),
    enabled: isOpen,
  });

  const roles = rolesData?.data || [
    { id: 1, name: 'Admin', description: 'System Administrator' },
    { id: 2, name: 'Manager', description: 'Sales Manager' },
    { id: 3, name: 'Supervisor', description: 'Sales Supervisor' },
    { id: 4, name: 'Sales Rep', description: 'Sales Representative' }
  ];

  // Update form when user changes
  useEffect(() => {
    if (user && isOpen) {
      console.log('🔧 Setting form values for user:', user);
      console.log('🔧 User is_active value:', user.is_active);
      console.log('🔧 User is_active type:', typeof user.is_active);
      
      const activeStatus = Boolean(user.is_active);
      setIsActive(activeStatus); // Update state variable
      
      const formValues = {
        sap_code: user.sap_code || '',
        username: user.username || '',
        email: user.email || '',
        full_name: user.full_name || '',
        phone: user.phone || '',
        role_id: user.role_id || 1,
        is_active: activeStatus, // Use the same value
      };
      
      console.log('🔧 Form values being set:', formValues);
      console.log('🔧 is_active will be set to:', formValues.is_active);
      console.log('🔧 State isActive will be set to:', activeStatus);
      
      form.setFieldsValue(formValues);
    }
  }, [user, isOpen, form]);

  const updateUserMutation = useMutation({
    mutationFn: (userData: UserFormData) => {
      console.log('🚀 UpdateUser mutation called with data:', userData);
      console.log('🚀 userData.is_active type:', typeof userData.is_active);
      console.log('🚀 userData.is_active value:', userData.is_active);
      
      // Let's also log the entire payload being sent
      const payload = {
        sap_code: userData.sap_code,
        username: userData.username,
        email: userData.email,
        full_name: userData.full_name,
        phone: userData.phone,
        role_id: userData.role_id,
        is_active: userData.is_active
      };
      console.log('🚀 Complete payload being sent to API:', payload);
      
      return apiService.updateUser(user!.id, payload);
    },
    onSuccess: (response) => {
      console.log('✅ User updated successfully:', response.data);
      queryClient.invalidateQueries({ 
        queryKey: ['users'],
        exact: false 
      });
      queryClient.refetchQueries({ 
        queryKey: ['users'],
        exact: false 
      });
      message.success('User updated successfully!');
      handleClose();
    },
    onError: (error: any) => {
      console.error('❌ User update failed:', error);
      if (error.response?.data?.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error('Failed to update user');
      }
      setLoading(false);
    },
  });

  const handleClose = () => {
    form.resetFields();
    setLoading(false);
    setIsActive(true); // Reset state variable
    onClose();
  };

  const handleSubmit = async (values: UserFormData) => {
    console.log('🔧 Form values before submission:', values);
    console.log('🔧 is_active value type:', typeof values.is_active);
    console.log('🔧 is_active value:', values.is_active);
    
    // Get the current form values to double-check
    const currentFormValues = form.getFieldsValue();
    console.log('🔧 Current form values:', currentFormValues);
    
    setLoading(true);
    
    // Ensure is_active is properly converted to boolean
    const cleanedValues = {
      ...values,
      is_active: Boolean(values.is_active)
    };
    
    console.log('🔧 Cleaned values for API:', cleanedValues);
    
    // Also log what will be sent to the API
    console.log('🔧 User ID being updated:', user?.id);
    
    updateUserMutation.mutate(cleanedValues);
  };

  return (
    <Modal
      title={
        <Space>
          <EditOutlined style={{ color: '#1890ff' }} />
          <Title level={4} style={{ margin: 0 }}>Edit User</Title>
        </Space>
      }
      open={isOpen}
      onCancel={handleClose}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Divider style={{ margin: '16px 0' }} />
      
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        requiredMark={false}
      >
        <Row gutter={[16, 0]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="sap_code"
              label="SAP Code"
              rules={[{ required: true, message: 'SAP Code is required' }]}
            >
              <Input
                prefix={<IdcardOutlined />}
                placeholder="Enter SAP code"
                size="large"
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="username"
              label="Username"
              rules={[
                { required: true, message: 'Username is required' },
                { min: 3, message: 'Username must be at least 3 characters' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Enter username"
                size="large"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="email"
              label="Email Address"
              rules={[
                { required: true, message: 'Email is required' },
                { type: 'email', message: 'Please enter a valid email' }
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="Enter email address"
                size="large"
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="full_name"
              label="Full Name"
              rules={[{ required: true, message: 'Full name is required' }]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Enter full name"
                size="large"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="phone"
              label="Phone Number"
            >
              <Input
                prefix={<PhoneOutlined />}
                placeholder="Enter phone number"
                size="large"
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="role_id"
              label="Role"
              rules={[{ required: true, message: 'Please select a role' }]}
            >
              <Select
                placeholder="Select user role"
                size="large"
                loading={rolesLoading}
              >
                {roles.map((role: any) => (
                  <Option key={role.id} value={role.id}>
                    <Space>
                      <span>{role.name}</span>
                      {role.description && (
                        <span style={{ color: '#666', fontSize: '12px' }}>
                          ({role.description})
                        </span>
                      )}
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col xs={24}>
            <Form.Item
              name="is_active"
              label="Status"
              valuePropName="checked"
            >
              <div style={{ paddingTop: '8px' }}>
                <Switch
                  checkedChildren="Active"
                  unCheckedChildren="Inactive"
                  checked={isActive} // Use state variable for controlled component
                  onChange={(checked) => {
                    console.log('🔧 Switch changed to:', checked);
                    console.log('🔧 Switch checked type:', typeof checked);
                    setIsActive(checked); // Update state
                    form.setFieldValue('is_active', checked); // Update form
                  }}
                />
                <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                  User will be able to log in and access the system
                </div>
                <div style={{ fontSize: '10px', color: '#999', marginTop: '4px' }}>
                  Debug: State = {JSON.stringify(isActive)}, Form = {JSON.stringify(form.getFieldValue('is_active'))}
                </div>
              </div>
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        <Row justify="end">
          <Col>
            <Space>
              <Button onClick={handleClose} size="large">
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                icon={<EditOutlined />}
              >
                {loading ? 'Updating User...' : 'Update User'}
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
}
