// API service for handling all API calls
// This is a mock implementation - replace with actual API endpoints

const API_BASE_URL = 'https://your-api-endpoint.com/api';

class ApiService {
  static async makeRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
      },
    };

    try {
      const response = await fetch(url, { ...defaultOptions, ...options });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Authentication APIs
  static async sendOTP(phoneNumber) {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: 'OTP sent successfully',
          otpId: 'mock_otp_id_' + Date.now(),
        });
      }, 2000);
    });
  }

  static async verifyOTP(phoneNumber, otp, otpId) {
    // Mock implementation - replace with actual API call
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (otp === '123456') {
          resolve({
            success: true,
            token: 'mock_token_' + Date.now(),
            user: {
              phone: phoneNumber,
              name: 'Sales Rep',
              territory: 'North Zone',
            },
          });
        } else {
          reject(new Error('Invalid OTP'));
        }
      }, 1500);
    });
  }

  // Visit APIs
  static async submitVisitData(visitData) {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          visitId: 'visit_' + Date.now(),
          message: 'Visit data submitted successfully',
        });
      }, 2000);
    });
  }

  static async getVisitHistory(dateRange) {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          visits: [
            {
              id: 1,
              customerName: 'ABC Store',
              date: '2025-07-30',
              status: 'completed',
              orderValue: 15000,
            },
            {
              id: 2,
              customerName: 'XYZ Market',
              date: '2025-07-29',
              status: 'completed',
              orderValue: 22000,
            },
          ],
        });
      }, 1500);
    });
  }

  // Weekly plan APIs
  static async getWeeklyPlan(weekStartDate) {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          plan: {
            week: weekStartDate,
            visits: {
              Monday: [
                { customer: 'ABC Store', time: '10:00 AM', region: 'Downtown' },
              ],
              Tuesday: [
                { customer: 'XYZ Market', time: '11:00 AM', region: 'Mall Road' },
              ],
              // ... other days
            },
          },
        });
      }, 1500);
    });
  }

  static async updateWeeklyPlan(planData) {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: 'Weekly plan updated successfully',
        });
      }, 1500);
    });
  }

  // Dashboard APIs
  static async getDashboardData() {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            kpis: {
              visitsCompleted: 15,
              visitsTarget: 20,
              salesAchieved: 37500,
              salesTarget: 50000,
              ordersToday: 8,
              newCustomers: 3,
            },
            upcomingVisits: [
              { customer: 'ABC Store', time: '10:00 AM', location: 'Downtown' },
              { customer: 'XYZ Market', time: '2:00 PM', location: 'Mall Road' },
            ],
            pendingTasks: [
              { task: 'Follow up with ABC Store', priority: 'high' },
              { task: 'Update inventory for XYZ', priority: 'medium' },
            ],
          },
        });
      }, 1500);
    });
  }

  // Inventory APIs
  static async getProductCatalog() {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          products: [
            { id: 1, name: 'Product A', category: 'Beverages', price: 50 },
            { id: 2, name: 'Product B', category: 'Snacks', price: 30 },
            // ... more products
          ],
        });
      }, 1500);
    });
  }

  static async syncInventoryData(inventoryData) {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: 'Inventory data synced successfully',
        });
      }, 2000);
    });
  }

  // Report APIs
  static async generateReport(reportType, dateRange) {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          reportUrl: 'https://example.com/report.pdf',
          reportData: {
            totalVisits: 45,
            totalSales: 150000,
            conversionRate: 0.75,
          },
        });
      }, 3000);
    });
  }
}

export default ApiService;
