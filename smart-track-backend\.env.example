# Smart Track Backend Environment Configuration
# Copy this file to .env and update the values for your environment
# This file shows all available configuration options

# Database Configuration
# Individual database connection parameters (recommended approach)
DATABASE_HOSTNAME=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=smart_track
DATABASE_PASSWORD=password
DATABASE_NAME=smart_track_db



# For production, use secure values:
# DATABASE_HOSTNAME=your-db-host
# DATABASE_PASSWORD=your-secure-password
# DATABASE_NAME=smart_track_production

# Redis
# REDIS_URL=redis://localhost:6379/0

# JWT Authentication
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads

# CORS - JSON array format for allowed origins
# For development: include "*" to allow all origins
# For production: specify exact domains for security
ALLOWED_ORIGINS=["*"]

# Environment
ENVIRONMENT=development
DEBUG=True

# SAP Integration (placeholder)
SAP_BASE_URL=https://your-sap-instance.com
SAP_USERNAME=
SAP_PASSWORD=
