# Smart Track Dashboard Environment Configuration - Example
# Copy this file to .env.local and update the values for your environment

# Backend API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_API_TIMEOUT=15000

# Dashboard Configuration
NEXT_PUBLIC_APP_NAME=Smart Track Dashboard
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=development

# Authentication Configuration
NEXT_PUBLIC_SESSION_TIMEOUT=3600000
NEXT_PUBLIC_AUTO_REFRESH_TOKEN=true

# UI Configuration
NEXT_PUBLIC_THEME=light
NEXT_PUBLIC_DEBUG_MODE=true

# Instructions:
# 1. Copy this file to .env.local for local development
# 2. Update NEXT_PUBLIC_API_BASE_URL if your backend runs on a different host/port
# 3. Set NEXT_PUBLIC_DEBUG_MODE=false for production
# 4. Update other values as needed for your environment
