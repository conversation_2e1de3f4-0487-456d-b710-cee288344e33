{"name": "smart-sales-rep-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.2.0", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "@tanstack/react-query": "^5.61.5", "axios": "^1.7.9", "date-fns": "^4.1.0", "expo": "~53.0.20", "expo-camera": "^16.1.11", "expo-constants": "^17.1.7", "expo-font": "^13.3.2", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-location": "^18.1.6", "expo-status-bar": "~2.2.3", "expo-system-ui": "^5.0.10", "formik": "^2.4.6", "react": "19.0.0", "react-native": "0.79.5", "react-native-date-picker": "^5.0.13", "react-native-elements": "^3.4.3", "react-native-keychain": "^8.2.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "yup": "^1.6.1", "zustand": "^4.5.5"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}