'use client';

import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Form, 
  Input, 
  Select, 
  Button, 
  message, 
  Space,
  DatePicker,
  Row,
  Col
} from 'antd';
import { UserOutlined, CalendarOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';

import { apiService } from '../../services/api';
import { Visit, Customer, User } from '../../types';

const { Option } = Select;
const { TextArea } = Input;

interface AddVisitModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function AddVisitModal({ isOpen, onClose, onSuccess }: AddVisitModalProps) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Get customers for selection
  const { data: customersData } = useQuery({
    queryKey: ['customers-for-visit'],
    queryFn: () => apiService.getCustomers(1, 100),
    enabled: isOpen,
  });

  // Get users for selection
  const { data: usersData } = useQuery({
    queryKey: ['users-for-visit'],
    queryFn: () => apiService.getUsers(1, 100),
    enabled: isOpen,
  });

  const customers = customersData?.data || [];
  const users = usersData?.data || [];

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      console.log('📝 Creating visit with values:', values);

      const visitData: Partial<Visit> = {
        customer_id: values.customer_id,
        user_id: values.user_id,
        planned_date: values.planned_date.toISOString(),
        purpose: values.purpose,
        notes: values.notes,
        status: 'planned' as const
      };

      console.log('📝 Visit data to send:', visitData);

      const response = await apiService.createVisit(visitData);
      console.log('✅ Visit created successfully:', response.data);

      message.success('Visit created successfully!');
      form.resetFields();
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('❌ Failed to create visit:', error);
      const errorMessage = error.response?.data?.detail || error.message || 'Failed to create visit';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  useEffect(() => {
    if (!isOpen) {
      form.resetFields();
    }
  }, [isOpen, form]);

  return (
    <Modal
      title="Add New Visit"
      open={isOpen}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        requiredMark={false}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="customer_id"
              label="Customer"
              rules={[{ required: true, message: 'Please select a customer' }]}
            >
              <Select
                placeholder="Select customer"
                showSearch
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {customers.map((customer: Customer) => (
                  <Option key={customer.id} value={customer.id}>
                    {customer.name} - {customer.code}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              name="user_id"
              label="Assigned User"
              rules={[{ required: true, message: 'Please select a user' }]}
            >
              <Select
                placeholder="Select user"
                showSearch
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {users.map((user: User) => (
                  <Option key={user.id} value={user.id}>
                    {user.full_name} - {user.email}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="planned_date"
          label="Planned Date & Time"
          rules={[{ required: true, message: 'Please select planned date and time' }]}
        >
          <DatePicker
            showTime
            style={{ width: '100%' }}
            placeholder="Select date and time"
                            disabledDate={(current) => current && current < dayjs().startOf('day')}
          />
        </Form.Item>

        <Form.Item
          name="purpose"
          label="Purpose"
          rules={[{ required: true, message: 'Please enter visit purpose' }]}
        >
          <Input placeholder="Enter visit purpose" />
        </Form.Item>

        <Form.Item
          name="notes"
          label="Notes"
        >
          <TextArea 
            rows={3} 
            placeholder="Additional notes (optional)"
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Space>
            <Button onClick={handleCancel}>
              Cancel
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              Create Visit
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
}
