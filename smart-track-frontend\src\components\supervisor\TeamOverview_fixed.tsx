'use client';

import { useState, useEffect } from 'react';
import { User, Visit } from '@/types';
import { api } from '@/services/api';

interface TeamMemberStats {
  user: User;
  totalVisits: number;
  completedVisits: number;
  pendingVisits: number;
  completionRate: number;
  lastVisitDate: string | null;
  recentActivity: Visit[];
}

interface TeamOverviewProps {
  user?: User;
}

export default function TeamOverview({ user: propUser }: TeamOverviewProps) {
  const [team, setTeam] = useState<User[]>([]);
  const [teamStats, setTeamStats] = useState<TeamMemberStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMember, setSelectedMember] = useState<number | null>(null);

  // Use prop user if available
  const user = propUser;

  useEffect(() => {
    // Only fetch data if user is available
    if (user?.id) {
      fetchTeamData();
    } else {
      setLoading(false);
    }
  }, [user?.id]);

  const fetchTeamData = async () => {
    if (!user?.id) {
      console.error('No user ID available');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      // Use the actual logged-in supervisor's ID
      const supervisorId = user.id;
      console.log(`🔍 Fetching team data for supervisor ID: ${supervisorId} (username: ${user.username || 'unknown'})`);
      
      // Fetch team members using the new endpoint
      const teamResponse = await api.getSupervisorTeam(supervisorId);
      console.log('✅ Team response received:', teamResponse.data);
      setTeam(teamResponse.data);

      // Fetch stats for each team member
      const statsPromises = teamResponse.data.map(async (member: User) => {
        try {
          // Get visits for this member
          const visitsResponse = await api.getVisits(1, 100, undefined, undefined);
          const memberVisits = visitsResponse.data.filter(visit => visit.user_id === member.id);
          
          const totalVisits = memberVisits.length;
          const completedVisits = memberVisits.filter(visit => visit.status === 'completed').length;
          const pendingVisits = memberVisits.filter(visit => visit.status === 'planned').length;
          const completionRate = totalVisits > 0 ? (completedVisits / totalVisits) * 100 : 0;
          
          // Get last visit date
          const sortedVisits = memberVisits.sort((a, b) => 
            new Date(b.planned_date).getTime() - new Date(a.planned_date).getTime()
          );
          const lastVisitDate = sortedVisits.length > 0 ? sortedVisits[0].planned_date : null;
          
          // Get recent activity (last 5 visits)
          const recentActivity = sortedVisits.slice(0, 5);

          return {
            user: member,
            totalVisits,
            completedVisits,
            pendingVisits,
            completionRate,
            lastVisitDate,
            recentActivity
          };
        } catch (error) {
          console.error(`Error fetching stats for member ${member.id}:`, error);
          return {
            user: member,
            totalVisits: 0,
            completedVisits: 0,
            pendingVisits: 0,
            completionRate: 0,
            lastVisitDate: null,
            recentActivity: []
          };
        }
      });

      const stats = await Promise.all(statsPromises);
      setTeamStats(stats);
    } catch (error: any) {
      console.error('❌ Error fetching team data:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
        
        // Handle specific error cases
        if (error.response.status === 401) {
          console.error('Authentication failed - user may need to login again');
        } else if (error.response.status === 403) {
          console.error('Access forbidden - user may not have supervisor permissions');
        } else if (error.response.status === 404) {
          console.error('Endpoint not found - check API endpoint configuration');
        }
      }
      setTeam([]);
      setTeamStats([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'planned': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPerformanceColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600';
    if (rate >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  if (loading || !user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="mb-8">
              <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="h-24 bg-white rounded-xl shadow-sm"></div>
              ))}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-80 bg-white rounded-xl shadow-sm"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="mb-4 md:mb-0">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                My Team Dashboard
              </h1>
              <p className="text-gray-600 text-lg">
                Monitor your team's performance and activity
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={fetchTeamData}
                className="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Refresh
              </button>
              <button className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg shadow-sm text-sm font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-200">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Member
              </button>
            </div>
          </div>
        </div>

        {teamStats.length === 0 ? (
          <div className="text-center py-16">
            <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
              <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Team Members</h3>
              <p className="text-gray-600 mb-6">No team members are currently assigned to this supervisor.</p>
              <button className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-200">
                Assign Team Members
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-2xl shadow-lg p-6 border-l-4 border-blue-500">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600 uppercase tracking-wide">Team Members</p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">{teamStats.length}</p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-full">
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-2xl shadow-lg p-6 border-l-4 border-green-500">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600 uppercase tracking-wide">Total Visits</p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">
                      {teamStats.reduce((sum, stats) => sum + stats.totalVisits, 0)}
                    </p>
                  </div>
                  <div className="p-3 bg-green-100 rounded-full">
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-2xl shadow-lg p-6 border-l-4 border-purple-500">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600 uppercase tracking-wide">Completed</p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">
                      {teamStats.reduce((sum, stats) => sum + stats.completedVisits, 0)}
                    </p>
                  </div>
                  <div className="p-3 bg-purple-100 rounded-full">
                    <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-2xl shadow-lg p-6 border-l-4 border-orange-500">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-600 uppercase tracking-wide">Avg. Completion</p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">
                      {teamStats.length > 0 
                        ? Math.round(teamStats.reduce((sum, stats) => sum + stats.completionRate, 0) / teamStats.length)
                        : 0}%
                    </p>
                  </div>
                  <div className="p-3 bg-orange-100 rounded-full">
                    <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {teamStats.map((memberStats) => (
                <div
                  key={memberStats.user.id}
                  className="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 cursor-pointer overflow-hidden"
                  onClick={() => setSelectedMember(
                    selectedMember === memberStats.user.id ? null : memberStats.user.id
                  )}
                >
                  <div className="relative p-6 pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="relative">
                          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                            {memberStats.user.full_name?.split(' ').map(n => n[0]).join('') || 'UN'}
                          </div>
                          <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-2 border-white ${
                            memberStats.completionRate >= 80 ? 'bg-green-500' : 
                            memberStats.completionRate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}></div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-bold text-lg text-gray-900 truncate">
                            {memberStats.user.full_name}
                          </h3>
                          <p className="text-sm text-gray-600 truncate">
                            {memberStats.user.email}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            ID: {memberStats.user.sap_code || memberStats.user.id}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-3xl font-bold ${getPerformanceColor(memberStats.completionRate)}`}>
                          {Math.round(memberStats.completionRate)}%
                        </div>
                        <p className="text-xs text-gray-500">Completion</p>
                      </div>
                    </div>
                  </div>

                  <div className="px-6 pb-4">
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-gray-900">{memberStats.totalVisits}</p>
                        <p className="text-xs text-gray-500">Total</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">{memberStats.completedVisits}</p>
                        <p className="text-xs text-gray-500">Done</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-orange-600">{memberStats.pendingVisits}</p>
                        <p className="text-xs text-gray-500">Pending</p>
                      </div>
                    </div>

                    <div className="relative">
                      <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full transition-all duration-500 ease-out"
                          style={{ width: `${memberStats.completionRate}%` }}
                        ></div>
                      </div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-xs font-medium text-white drop-shadow-sm">
                          {Math.round(memberStats.completionRate)}%
                        </span>
                      </div>
                    </div>

                    {memberStats.lastVisitDate && (
                      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Last Activity:</span>
                          <span className="text-sm font-medium text-gray-900">
                            {formatDate(memberStats.lastVisitDate)}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="px-6 pb-4">
                    <div className="flex items-center justify-center text-gray-400 group-hover:text-gray-600 transition-colors">
                      <svg 
                        className={`w-5 h-5 transition-transform duration-200 ${
                          selectedMember === memberStats.user.id ? 'rotate-180' : ''
                        }`} 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {selectedMember && (
              <div className="bg-white rounded-2xl shadow-lg p-6 border-t-4 border-indigo-500">
                {(() => {
                  const memberStats = teamStats.find(stats => stats.user.id === selectedMember);
                  if (!memberStats) return null;

                  return (
                    <div>
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                            {memberStats.user.full_name?.split(' ').map(n => n[0]).join('') || 'UN'}
                          </div>
                          <div>
                            <h3 className="text-2xl font-bold text-gray-900">
                              {memberStats.user.full_name} - Recent Activity
                            </h3>
                            <p className="text-gray-600">{memberStats.user.email}</p>
                          </div>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedMember(null);
                          }}
                          className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
                        >
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                      
                      {memberStats.recentActivity.length === 0 ? (
                        <div className="text-center py-12">
                          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                          </div>
                          <h4 className="text-lg font-medium text-gray-900 mb-2">No Recent Activity</h4>
                          <p className="text-gray-600">This team member hasn't had any recent visits.</p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Recent Visits ({memberStats.recentActivity.length})
                          </h4>
                          
                          <div className="grid gap-4">
                            {memberStats.recentActivity.map((visit, index) => (
                              <div 
                                key={visit.id} 
                                className="group relative bg-gradient-to-r from-gray-50 to-gray-100 hover:from-indigo-50 hover:to-purple-50 rounded-xl p-5 border border-gray-200 hover:border-indigo-200 transition-all duration-200"
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center space-x-3 mb-3">
                                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(visit.status)}`}>
                                        <div className={`w-2 h-2 rounded-full mr-2 ${
                                          visit.status === 'completed' ? 'bg-green-500' :
                                          visit.status === 'in_progress' ? 'bg-blue-500 animate-pulse' :
                                          visit.status === 'planned' ? 'bg-yellow-500' : 'bg-red-500'
                                        }`}></div>
                                        {visit.status.replace('_', ' ').toUpperCase()}
                                      </span>
                                      <span className="text-sm text-gray-500">
                                        #{index + 1}
                                      </span>
                                    </div>
                                    
                                    <h5 className="font-bold text-lg text-gray-900 mb-2 group-hover:text-indigo-900 transition-colors">
                                      {visit.customer?.name || 'Unknown Customer'}
                                    </h5>
                                    
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                      <div className="flex items-center text-gray-600">
                                        <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                        {formatDate(visit.planned_date)}
                                      </div>
                                      
                                      <div className="flex items-center text-gray-600">
                                        <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                        {visit.customer?.city || 'Unknown Location'}
                                      </div>
                                      
                                      {visit.purpose && (
                                        <div className="flex items-center text-gray-600 md:col-span-2">
                                          <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                          </svg>
                                          <span className="font-medium">Purpose:</span>
                                          <span className="ml-1">{visit.purpose}</span>
                                        </div>
                                      )}
                                    </div>
                                    
                                    {visit.notes && (
                                      <div className="mt-3 p-3 bg-white rounded-lg border border-gray-200">
                                        <p className="text-sm text-gray-700 italic">
                                          <svg className="w-4 h-4 inline mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                                          </svg>
                                          "{visit.notes}"
                                        </p>
                                      </div>
                                    )}
                                  </div>
                                  
                                  <div className="ml-4">
                                    <button className="opacity-0 group-hover:opacity-100 p-2 text-gray-400 hover:text-indigo-600 hover:bg-white rounded-full transition-all duration-200">
                                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                      </svg>
                                    </button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })()}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
