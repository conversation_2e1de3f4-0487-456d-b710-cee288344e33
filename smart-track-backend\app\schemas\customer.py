from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, ConfigDict
from decimal import Decimal

# Distribution Channel Schemas
class DistributionChannelBase(BaseModel):
    name: str
    code: str
    description: Optional[str] = None
    manager_id: Optional[int] = None
    is_active: bool = True

class DistributionChannelResponse(DistributionChannelBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime

class DistributionChannelCreate(DistributionChannelBase):
    pass

class DistributionChannelUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    manager_id: Optional[int] = None
    is_active: Optional[bool] = None

# Customer Schemas
class CustomerBase(BaseModel):
    code: str
    name: str
    customer_group: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    province: Optional[str] = None
    region: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    customer_type: Optional[str] = None
    credit_limit: Optional[Decimal] = 0.0
    current_balance: Optional[Decimal] = 0.0
    customer_statistics_group: Optional[str] = None
    sdst: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    is_active: bool = True

class CustomerCreate(CustomerBase):
    pass

class CustomerUpdate(BaseModel):
    name: Optional[str] = None
    customer_group: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    province: Optional[str] = None
    region: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    customer_type: Optional[str] = None
    credit_limit: Optional[Decimal] = None
    current_balance: Optional[Decimal] = None
    customer_statistics_group: Optional[str] = None
    sdst: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    is_active: Optional[bool] = None

class CustomerResponse(CustomerBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

# Customer Distribution Channel Schemas
class CustomerDistributionChannelBase(BaseModel):
    customer_id: int
    distribution_channel_id: int
    sales_rep_id: int
    manager_code: Optional[str] = None
    is_active: bool = True

class CustomerDistributionChannelCreate(BaseModel):
    distribution_channel_id: int
    sales_rep_id: int
    manager_code: Optional[str] = None

class CustomerDistributionChannelUpdate(BaseModel):
    sales_rep_id: Optional[int] = None
    manager_code: Optional[str] = None
    is_active: Optional[bool] = None

class CustomerDistributionChannelResponse(CustomerDistributionChannelBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    assigned_at: datetime
    distribution_channel: DistributionChannelResponse
    # Note: sales_rep relationship would need User schema import

# Customer Contact Schemas
class CustomerContactBase(BaseModel):
    name: str
    designation: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    is_primary: bool = False

class CustomerContactCreate(CustomerContactBase):
    pass

class CustomerContactUpdate(BaseModel):
    name: Optional[str] = None
    designation: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    is_primary: Optional[bool] = None

class CustomerContactResponse(CustomerContactBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    customer_id: int
    created_at: datetime

# Branch Schemas (Customer Branches)
class BranchBase(BaseModel):
    name: str
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    region: Optional[str] = None
    phone: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    is_active: bool = True

class BranchCreate(BranchBase):
    customer_id: int

class BranchUpdate(BaseModel):
    name: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    region: Optional[str] = None
    phone: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    is_active: Optional[bool] = None

class BranchResponse(BranchBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    customer_id: int
    created_at: datetime
