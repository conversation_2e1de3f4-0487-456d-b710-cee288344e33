'use client';

import React, { useState } from 'react';
import { Modal, Button, message, Space, Typography, Alert } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

import { apiService } from '../../services/api';
import { Visit } from '../../types';

const { Text, Title } = Typography;

interface DeleteVisitModalProps {
  isOpen: boolean;
  onClose: () => void;
  visit: Visit | null;
}

export default function DeleteVisitModal({ isOpen, onClose, visit }: DeleteVisitModalProps) {
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!visit) return;

    try {
      setLoading(true);
      console.log('🗑️ Deleting visit:', visit.id);

      await apiService.deleteVisit(visit.id);
      console.log('✅ Visit deleted successfully');

      message.success('Visit deleted successfully!');
      onClose();
    } catch (error: any) {
      console.error('❌ Failed to delete visit:', error);
      const errorMessage = error.response?.data?.detail || error.message || 'Failed to delete visit';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return dayjs(dateString).format('MMM DD, YYYY HH:mm');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned':
        return '#1890ff';
      case 'in_progress':
        return '#fa8c16';
      case 'completed':
        return '#52c41a';
      case 'cancelled':
        return '#ff4d4f';
      default:
        return '#666';
    }
  };

  if (!visit) return null;

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
          Delete Visit
        </div>
      }
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={500}
    >
      <div style={{ marginBottom: 24 }}>
        <Alert
          message="Are you sure you want to delete this visit?"
          description="This action cannot be undone. All visit data will be permanently removed."
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <div style={{ 
          padding: 16, 
          border: '1px solid #f0f0f0', 
          borderRadius: 8, 
          backgroundColor: '#fafafa' 
        }}>
          <Title level={5} style={{ margin: 0, marginBottom: 8 }}>
            Visit Details
          </Title>
          
          <div style={{ marginBottom: 8 }}>
            <Text strong>Customer: </Text>
            <Text>{visit.customer?.name || 'Unknown Customer'}</Text>
          </div>
          
          <div style={{ marginBottom: 8 }}>
            <Text strong>Scheduled: </Text>
            <Text>{formatDate(visit.planned_date)}</Text>
          </div>
          
          <div style={{ marginBottom: 8 }}>
            <Text strong>Purpose: </Text>
            <Text>{visit.purpose || 'No purpose specified'}</Text>
          </div>
          
          <div style={{ marginBottom: 8 }}>
            <Text strong>Status: </Text>
            <Text style={{ color: getStatusColor(visit.status) }}>
              {visit.status?.charAt(0).toUpperCase() + visit.status?.slice(1)}
            </Text>
          </div>

          {visit.notes && (
            <div>
              <Text strong>Notes: </Text>
              <Text>{visit.notes}</Text>
            </div>
          )}
        </div>
      </div>

      <div style={{ textAlign: 'right' }}>
        <Space>
          <Button onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button 
            type="primary" 
            danger 
            loading={loading}
            onClick={handleDelete}
          >
            Delete Visit
          </Button>
        </Space>
      </div>
    </Modal>
  );
}
