from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime

# Role Schemas
class RoleBase(BaseModel):
    name: str
    description: Optional[str] = None
    is_active: bool = True

class RoleCreate(RoleBase):
    pass

class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

class RoleResponse(RoleBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True

# Permission Schemas
class PermissionBase(BaseModel):
    name: str
    description: Optional[str] = None
    resource: str
    action: str

class PermissionResponse(PermissionBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True

# User Profile Schemas
class UserProfileBase(BaseModel):
    distribution_channel_id: Optional[int] = None
    role_type: Optional[str] = None
    supervisor_id: Optional[int] = None
    profile_image: Optional[str] = None
    address: Optional[str] = None
    emergency_contact: Optional[str] = None

class UserProfileCreate(UserProfileBase):
    user_id: int

class UserProfileUpdate(BaseModel):
    distribution_channel_id: Optional[int] = None
    role_type: Optional[str] = None
    supervisor_id: Optional[int] = None
    profile_image: Optional[str] = None
    address: Optional[str] = None
    emergency_contact: Optional[str] = None

class UserProfileResponse(UserProfileBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    # Note: branch relationship removed since branches are now customer-specific

    class Config:
        from_attributes = True

# User Schemas
class UserBase(BaseModel):
    sap_code: str
    username: str
    email: EmailStr
    full_name: str
    phone: Optional[str] = None
    is_active: bool = True
    is_verified: bool = False
    role_id: int

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    sap_code: Optional[str] = None
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    role_id: Optional[int] = None

class UserResponse(UserBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    role: Optional[RoleResponse] = None
    profile: Optional[UserProfileResponse] = None

    class Config:
        from_attributes = True

class UserDetailResponse(UserResponse):
    """Extended user response with relationships"""
    role: RoleResponse
    profile: Optional[UserProfileResponse] = None

    class Config:
        from_attributes = True
