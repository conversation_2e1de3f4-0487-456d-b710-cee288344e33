# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Next.js
.next
out
build
dist

# Git
.git
.gitignore
README.md
.github

# Environment files (keep .env.example but ignore actual env files)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Development tools
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Coverage directory used by tools like istanbul
coverage
.nyc_output

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Stylelint cache
.stylelintcache

# TypeScript cache
*.tsbuildinfo

# Testing
/coverage
/tests/e2e/reports/
/tests/e2e/screenshots/

# Storybook build outputs
storybook-static

# Temporary folders
tmp
temp

# Documentation
*.md
docs

# CI/CD
.github
.gitlab-ci.yml
.travis.yml
.circleci

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Vercel
.vercel

# Turbo
.turbo

# SWC cache
.swc

# Editor and IDE files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Package manager lock files (keep pnpm-lock.yaml for reproducible builds)
package-lock.json
yarn.lock

# Local development files
.env.development
.env.production

# Additional optimizations
*.test.js
*.test.ts
*.test.tsx
*.spec.js
*.spec.ts
*.spec.tsx
**/__tests__/**
**/test/**
**/tests/**
**/*.test.*
**/*.spec.*

# Storybook
.storybook
storybook-static

# Build artifacts that shouldn't be copied
.next/cache
.next/trace
.next/server/chunks/**/*.js.map
.next/static/chunks/**/*.js.map
.next/static/css/**/*.css.map

# Development dependencies
node_modules/.cache
.cache
