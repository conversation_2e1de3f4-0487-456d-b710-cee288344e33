# Smart Sales Rep Mobile App

A comprehensive mobile application designed for salespeople to manage their daily activities, track visits, manage inventory, and monitor performance.

## Features

### 🔐 Authentication
- **OTP-based Login**: Secure authentication using phone number and OTP verification
- **Session Management**: Automatic login persistence with secure token storage

### 🏠 Dashboard (Home Screen)
- **Performance KPIs**: Real-time display of daily/weekly performance metrics
  - Visits completed vs target
  - Sales achieved vs target
  - Orders processed today
  - New clients acquired
- **Upcoming Visits**: Quick overview of scheduled client visits
- **Pending Tasks**: Task management with priority indicators
- **Quick Actions**: Fast access to start new visits or update data

### 📅 Weekly Planning
- **Regional View**: Organize visits by geographical regions
- **Progress Tracking**: Visual progress indicators for each day
- **Visit Management**: Add, edit, and mark visits as complete
- **Search & Filter**: Find visits by client name or filter by region
- **Status Updates**: Real-time status tracking (pending, completed, cancelled)

### 📝 Visit Data Entry
- **Client Information**: Comprehensive visit logging with client details
- **Photo Documentation**: 
  - Take photos directly from camera
  - Upload from device gallery
  - Multiple photos per visit
- **Inventory Tracking**:
  - Client product inventory with stock levels
  - Competitor product analysis
  - Product categorization and pricing
- **Order Management**: Record and track product orders
- **Form Validation**: Ensure data quality with built-in validation

## Technology Stack

- **Framework**: React Native with Expo
- **Navigation**: React Navigation (Stack & Tab navigators)
- **UI Components**: React Native Paper (Material Design)
- **State Management**: React Hooks & Context API
- **Storage**: AsyncStorage for local data persistence
- **Form Handling**: Formik with Yup validation
- **Image Handling**: Expo Image Picker & Camera
- **Icons**: Expo Vector Icons

## Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- Expo CLI: `npm install -g @expo/cli`
- Expo Go app on your mobile device (for testing)

### Setup Instructions

1. **Clone/Download the project**
   ```bash
   cd "Mobile apps/smart sales rep app"
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npx expo start
   ```

4. **Run on device**
   - Install Expo Go app on your mobile device
   - Scan the QR code displayed in terminal/browser
   - The app will load on your device

### For Production Build

#### Android APK
```bash
npx expo build:android
```

#### iOS App Store
```bash
npx expo build:ios
```

## App Structure

```
src/
├── components/          # Reusable UI components
│   └── KPICard.js      # Performance indicator cards
├── screens/            # Main app screens
│   ├── LoginScreen.js  # OTP authentication
│   ├── HomeScreen.js   # Dashboard with KPIs
│   ├── WeeklyPlanScreen.js   # Weekly visit planning
│   └── VisitDataEntryScreen.js # Visit data entry form
├── services/           # Business logic & API calls
│   ├── ApiService.js   # API integration (mock implementation)
│   └── StorageService.js # Local storage management
└── utils/              # Helper functions
    └── helpers.js      # Utility functions
```

## Key Features Explained

### Login System
- **Phone-based Authentication**: Enter 10-digit phone number
- **OTP Verification**: Receive and verify 6-digit OTP
- **Demo Mode**: Use OTP "123456" for any phone number during development
- **Auto Login**: Remember user session for subsequent app launches

### Dashboard Analytics
- **Real-time KPIs**: Track daily performance metrics
- **Progress Bars**: Visual representation of targets vs achievements
- **Quick Navigation**: Direct access to key functions
- **Refresh to Update**: Pull-to-refresh for latest data

### Weekly Planning
- **Day-wise View**: Organize visits by weekdays
- **Regional Filtering**: Filter visits by geographical regions
- **Progress Tracking**: Monitor completion status for each day
- **Dynamic Updates**: Real-time status updates and modifications

### Visit Documentation
- **Comprehensive Forms**: Capture all visit details
- **Photo Evidence**: Document visits with multiple photos
- **Inventory Analysis**: Track both client and competitor products
- **Order Processing**: Record customer orders and values
- **Data Validation**: Ensure data quality with form validation

## Customization

### Adding New Regions
Edit the `regions` array in `WeeklyPlanScreen.js`:
```javascript
const regions = ['All', 'Downtown', 'Mall Road', 'City Center', 'Suburbs', 'New Region'];
```

### Adding Product Categories
Update the `productCategories` array in `VisitDataEntryScreen.js`:
```javascript
const productCategories = [
  'Beverages', 'Snacks', 'Dairy', 'Frozen Foods', 
  'Personal Care', 'Household Items', 'New Category'
];
```

### API Integration
Replace mock implementations in `ApiService.js` with actual API endpoints:
```javascript
const API_BASE_URL = 'https://your-actual-api.com/api';
```

## Data Storage

The app uses AsyncStorage for local data persistence:
- **User Authentication**: Tokens and user information
- **Visit Data**: All visit records and associated data
- **Weekly Plans**: Scheduled visits and plans
- **App Settings**: User preferences and configuration

## Development Notes

### Demo Features
- **Mock OTP**: Use "123456" as OTP for any phone number
- **Sample Data**: Pre-populated with demo visit data and KPIs
- **Offline Mode**: Fully functional without backend connectivity

### Performance Considerations
- **Image Optimization**: Automatic image compression for photos
- **Efficient Rendering**: Optimized list rendering for large datasets
- **Memory Management**: Proper cleanup of resources and listeners

## Future Enhancements

### Planned Features
- **GPS Tracking**: Location-based visit verification
- **Offline Sync**: Robust offline functionality with sync
- **Push Notifications**: Real-time updates and reminders
- **Advanced Analytics**: Detailed reporting and insights
- **Team Management**: Multi-user capabilities for team leads
- **Integration APIs**: CRM and ERP system integration

### Technical Improvements
- **State Management**: Redux/Zustand for complex state
- **Testing**: Unit and integration test coverage
- **Performance**: Code splitting and lazy loading
- **Security**: Enhanced authentication and data encryption

## Support & Maintenance

### Common Issues
1. **App won't start**: Check Node.js version and dependencies
2. **Camera not working**: Verify device permissions
3. **Data not saving**: Check AsyncStorage availability
4. **Navigation errors**: Ensure all screen imports are correct

### Debug Mode
Enable debug mode in development:
```javascript
// In App.js, add:
console.log('Debug mode enabled');
```

## Contributing

When contributing to this project:
1. Follow the existing code structure and naming conventions
2. Add proper error handling and validation
3. Test on both iOS and Android devices
4. Update documentation for new features
5. Ensure backward compatibility with existing data

## License

This project is proprietary software developed for internal use. All rights reserved.

---

**Version**: 1.0.0  
**Last Updated**: July 31, 2025  
**Compatibility**: iOS 11+, Android 6.0+
