from fastapi import APIRouter
from app.api.v1.endpoints import auth, users, visits, locations, customers, distribution_channels, dashboard, weekly_plans, supervisor

api_router = APIRouter()

# Include endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
api_router.include_router(supervisor.router, prefix="/supervisor", tags=["supervisor"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(visits.router, prefix="/visits", tags=["visits"])
api_router.include_router(customers.router, prefix="/customers", tags=["customers"])
api_router.include_router(locations.router, prefix="/locations", tags=["locations"])
api_router.include_router(distribution_channels.router, prefix="/distribution-channels", tags=["distribution-channels"])
api_router.include_router(weekly_plans.router, prefix="/weekly-plans", tags=["weekly-plans"])
