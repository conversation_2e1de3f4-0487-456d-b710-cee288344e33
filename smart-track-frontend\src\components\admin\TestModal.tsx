import React from 'react';

const TestModal = ({ isOpen }: { isOpen: boolean }) => {
  console.log('🧪 TestModal render:', { isOpen });
  
  if (!isOpen) return null;
  
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      backgroundColor: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 9999
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        maxWidth: '400px',
        width: '90%'
      }}>
        <h2>🧪 Test Modal</h2>
        <p>If you see this, the modal system is working!</p>
        <button onClick={() => console.log('✅ Button clicked!')}>
          Test Button
        </button>
      </div>
    </div>
  );
};

export default TestModal;
