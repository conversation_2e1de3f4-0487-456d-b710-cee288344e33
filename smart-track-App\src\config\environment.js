import Constants from 'expo-constants';

// Environment configuration for the mobile app
class EnvironmentConfig {
  constructor() {
    this.config = this.loadConfig();
  }

  loadConfig() {
    // Get environment variables from Expo
    const expoConfig = Constants.expoConfig || {};
    const manifest = Constants.manifest || {};
    
    // Use process.env for web, Constants.expoConfig.extra for native
    const env = {
      // API Configuration
      API_BASE_URL: process.env.EXPO_PUBLIC_API_BASE_URL || 
                    expoConfig.extra?.EXPO_PUBLIC_API_BASE_URL || 
                    'http://localhost:8000/api/v1',
      
      API_TIMEOUT: parseInt(process.env.EXPO_PUBLIC_API_TIMEOUT || 
                   expoConfig.extra?.EXPO_PUBLIC_API_TIMEOUT || 
                   '20000'),
      
      // Alternative URLs
      API_LOCALHOST: process.env.EXPO_PUBLIC_API_LOCALHOST || 
                     expoConfig.extra?.EXPO_PUBLIC_API_LOCALHOST || 
                     'http://localhost:8000/api/v1',
      
      API_ANDROID_EMULATOR: process.env.EXPO_PUBLIC_API_ANDROID_EMULATOR || 
                            expoConfig.extra?.EXPO_PUBLIC_API_ANDROID_EMULATOR || 
                            'http://********:8000/api/v1',
      
      API_IOS_LOCALHOST: process.env.EXPO_PUBLIC_API_IOS_LOCALHOST || 
                         expoConfig.extra?.EXPO_PUBLIC_API_IOS_LOCALHOST || 
                         'http://127.0.0.1:8000/api/v1',
      
      // Environment Configuration
      ENVIRONMENT: process.env.EXPO_PUBLIC_ENVIRONMENT || 
                   expoConfig.extra?.EXPO_PUBLIC_ENVIRONMENT || 
                   'development',
      
      DEBUG_MODE: (process.env.EXPO_PUBLIC_DEBUG_MODE || 
                   expoConfig.extra?.EXPO_PUBLIC_DEBUG_MODE || 
                   'true') === 'true',
      
      // Authentication Configuration
      TOKEN_REFRESH_ENABLED: (process.env.EXPO_PUBLIC_TOKEN_REFRESH_ENABLED || 
                              expoConfig.extra?.EXPO_PUBLIC_TOKEN_REFRESH_ENABLED || 
                              'true') === 'true',
      
      AUTO_LOGIN_ENABLED: (process.env.EXPO_PUBLIC_AUTO_LOGIN_ENABLED || 
                           expoConfig.extra?.EXPO_PUBLIC_AUTO_LOGIN_ENABLED || 
                           'false') === 'true'
    };

    if (env.DEBUG_MODE) {
      console.log('🔧 Environment Configuration Loaded:');
      console.log('API_BASE_URL:', env.API_BASE_URL);
      console.log('ENVIRONMENT:', env.ENVIRONMENT);
      console.log('DEBUG_MODE:', env.DEBUG_MODE);
      console.log('API_TIMEOUT:', env.API_TIMEOUT);
    }

    return env;
  }

  get(key) {
    return this.config[key];
  }

  getApiBaseUrl() {
    return this.config.API_BASE_URL;
  }

  getApiTimeout() {
    return this.config.API_TIMEOUT;
  }

  getAlternativeUrls() {
    return [
      this.config.API_BASE_URL,
      this.config.API_LOCALHOST,
      this.config.API_ANDROID_EMULATOR,
      this.config.API_IOS_LOCALHOST
    ].filter(Boolean);
  }

  isDebugMode() {
    return this.config.DEBUG_MODE;
  }

  isProduction() {
    return this.config.ENVIRONMENT === 'production';
  }

  isDevelopment() {
    return this.config.ENVIRONMENT === 'development';
  }

  // Auto-detect the best base URL for the current platform
  getAutoDetectedBaseUrl() {
    try {
      // Check if we're in Expo environment
      const isExpo = typeof Constants.expoConfig !== 'undefined' || 
                     typeof __DEV__ !== 'undefined';
      
      if (isExpo) {
        // For Expo, use the configured IP address
        return this.config.API_BASE_URL;
      }
      
      // For native React Native apps
      const Platform = require('react-native').Platform;
      
      if (Platform.OS === 'android') {
        return this.config.API_ANDROID_EMULATOR;
      } else if (Platform.OS === 'ios') {
        return this.config.API_IOS_LOCALHOST;
      }
      
      // Fallback
      return this.config.API_LOCALHOST;
    } catch (error) {
      console.log('Platform detection failed, using default URL:', error);
      return this.config.API_BASE_URL;
    }
  }
}

export const envConfig = new EnvironmentConfig();
export default envConfig;
