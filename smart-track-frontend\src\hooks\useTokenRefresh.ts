'use client';

import { useEffect, useRef } from 'react';
import { useAuthStore } from '@/stores/auth';

// Hook to handle automatic token refresh
export const useTokenRefresh = () => {
  const { refreshToken, isAuthenticated, logout } = useAuthStore();
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!isAuthenticated) return;

    // Function to schedule token refresh
    const scheduleTokenRefresh = () => {
      // Clear any existing timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }

      // Schedule refresh 5 minutes before token expires (55 minutes)
      const refreshInterval = 55 * 60 * 1000; // 55 minutes in milliseconds

      refreshTimeoutRef.current = setTimeout(async () => {
        try {
          const success = await refreshToken();
          if (success) {
            // Schedule next refresh
            scheduleTokenRefresh();
          } else {
            // Refresh failed, logout user
            logout();
          }
        } catch (error) {
          console.error('Token refresh failed:', error);
          logout();
        }
      }, refreshInterval);
    };

    // Start the refresh schedule
    scheduleTokenRefresh();

    // Cleanup on unmount or when authentication status changes
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [isAuthenticated, refreshToken, logout]);

  // Handle page visibility change to refresh token when user returns
  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible' && isAuthenticated) {
        // Check if token needs refresh when user returns to tab
        const token = localStorage.getItem('access_token');
        if (token) {
          try {
            const tokenData = JSON.parse(token);
            const now = new Date().getTime();
            const timeUntilExpiry = tokenData.expiry - now;
            
            // If token expires in less than 10 minutes, refresh it
            if (timeUntilExpiry < 10 * 60 * 1000) {
              await refreshToken();
            }
          } catch (error) {
            // If parsing fails, try to refresh anyway
            await refreshToken();
          }
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isAuthenticated, refreshToken]);

  // Handle focus event to refresh token when window gains focus
  useEffect(() => {
    const handleFocus = async () => {
      if (isAuthenticated) {
        // Similar logic to visibility change
        const token = localStorage.getItem('access_token');
        if (token) {
          try {
            const tokenData = JSON.parse(token);
            const now = new Date().getTime();
            const timeUntilExpiry = tokenData.expiry - now;
            
            if (timeUntilExpiry < 10 * 60 * 1000) {
              await refreshToken();
            }
          } catch (error) {
            await refreshToken();
          }
        }
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [isAuthenticated, refreshToken]);
};
