import { api } from './api';

class CustomerService {
  async getCustomers(params = {}) {
    try {
      const response = await api.get('/customers/', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching customers:', error);
      throw error;
    }
  }

  async getCustomer(customerId) {
    try {
      const response = await api.get(`/customers/${customerId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching customer:', error);
      throw error;
    }
  }

  async getCustomerOrders(customerId, params = {}) {
    try {
      const response = await api.get(`/customers/${customerId}/orders`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching customer orders:', error);
      throw error;
    }
  }

  async getCustomerVisits(customerId, params = {}) {
    try {
      const response = await api.get(`/customers/${customerId}/visits`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching customer visits:', error);
      throw error;
    }
  }

  async updateCustomer(customerId, customerData) {
    try {
      const response = await api.put(`/customers/${customerId}`, customerData);
      return response.data;
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  }

  async getCustomerContacts(customerId) {
    try {
      const response = await api.get(`/customers/${customerId}/contacts`);
      return response.data;
    } catch (error) {
      console.error('Error fetching customer contacts:', error);
      throw error;
    }
  }

  async updateCustomerContact(customerId, contactId, contactData) {
    try {
      const response = await api.put(`/customers/${customerId}/contacts/${contactId}`, contactData);
      return response.data;
    } catch (error) {
      console.error('Error updating customer contact:', error);
      throw error;
    }
  }

  async deleteCustomerContact(customerId, contactId) {
    try {
      const response = await api.delete(`/customers/${customerId}/contacts/${contactId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting customer contact:', error);
      throw error;
    }
  }

  // Search and filter helpers
  async searchCustomers(searchTerm, filters = {}) {
    try {
      const params = {
        search: searchTerm,
        ...filters
      };
      return await this.getCustomers(params);
    } catch (error) {
      console.error('Error searching customers:', error);
      throw error;
    }
  }

  async getNearbyCustomers(latitude, longitude, radius = 5000) {
    try {
      const response = await api.get('/locations/nearby', {
        params: { lat: latitude, lng: longitude, radius }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching nearby customers:', error);
      throw error;
    }
  }

  // Analytics and insights
  async getCustomerInsights(customerId) {
    try {
      const [orders, visits, activities] = await Promise.all([
        this.getCustomerOrders(customerId),
        this.getCustomerVisits(customerId),
        // You would add customer activities endpoint here
      ]);

      return {
        orders,
        visits,
        activities: activities || [],
        totalValue: orders.total_amount || 0,
        visitFrequency: this.calculateVisitFrequency(visits),
        lastInteraction: this.getLastInteractionDate(visits, activities),
      };
    } catch (error) {
      console.error('Error fetching customer insights:', error);
      throw error;
    }
  }

  calculateVisitFrequency(visits) {
    if (!visits || visits.length < 2) return 0;
    
    const sortedVisits = visits
      .filter(v => v.status === 'completed')
      .sort((a, b) => new Date(a.planned_date) - new Date(b.planned_date));
    
    if (sortedVisits.length < 2) return 0;
    
    const firstVisit = new Date(sortedVisits[0].planned_date);
    const lastVisit = new Date(sortedVisits[sortedVisits.length - 1].planned_date);
    const daysDiff = (lastVisit - firstVisit) / (1000 * 60 * 60 * 24);
    
    return daysDiff > 0 ? sortedVisits.length / (daysDiff / 30) : 0; // visits per month
  }

  getLastInteractionDate(visits, activities) {
    const allDates = [];
    
    if (visits) {
      visits.forEach(visit => {
        if (visit.status === 'completed' && visit.checkout_time) {
          allDates.push(new Date(visit.checkout_time));
        }
      });
    }
    
    if (activities) {
      activities.forEach(activity => {
        allDates.push(new Date(activity.activity_date));
      });
    }
    
    return allDates.length > 0 ? new Date(Math.max(...allDates)) : null;
  }
}

export const customerService = new CustomerService();
