'use client';

import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Modal, Button, message, Typography, Space, Alert, Avatar } from 'antd';
import { ExclamationCircleOutlined, UserOutlined } from '@ant-design/icons';

import { apiService } from '../../services/api';
import { Customer } from '../../types';

const { Text, Title } = Typography;

interface DeleteCustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer: Customer | null;
}

export default function DeleteCustomerModal({ isOpen, onClose, customer }: DeleteCustomerModalProps) {
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);

  const deleteCustomerMutation = useMutation({
    mutationFn: (customerId: number) => {
      console.log('🗑️ Deleting customer with ID:', customerId);
      return apiService.deleteCustomer(customerId);
    },
    onSuccess: () => {
      console.log('✅ Customer deleted successfully');
      queryClient.invalidateQueries({ 
        queryKey: ['customers'],
        exact: false 
      });
      queryClient.refetchQueries({ 
        queryKey: ['customers'],
        exact: false 
      });
      message.success('Customer deleted successfully!');
      setLoading(false);
      onClose();
    },
    onError: (error: any) => {
      console.error('❌ Customer deletion failed:', error);
      if (error.response?.data?.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error('Failed to delete customer');
      }
      setLoading(false);
    },
  });

  const handleDelete = async () => {
    if (!customer) return;
    
    try {
      setLoading(true);
      deleteCustomerMutation.mutate(customer.id);
    } catch (error) {
      console.error('❌ Delete operation error:', error);
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setLoading(false);
    onClose();
  };

  if (!customer) return null;

  return (
    <Modal
      title={
        <Space>
          <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
          <span>Delete Customer</span>
        </Space>
      }
      open={isOpen}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel} disabled={loading}>
          Cancel
        </Button>,
        <Button 
          key="delete" 
          type="primary" 
          danger 
          loading={loading}
          onClick={handleDelete}
        >
          Delete Customer
        </Button>,
      ]}
      width={500}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Alert
          message="Warning"
          description="This action cannot be undone. The customer will be permanently removed from the system."
          type="warning"
          showIcon
        />

        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Avatar 
            size={64} 
            style={{ backgroundColor: '#1890ff', marginBottom: 16 }}
            icon={<UserOutlined />}
          >
            {customer.name.charAt(0).toUpperCase()}
          </Avatar>
          
          <Title level={4} style={{ margin: '0 0 8px 0' }}>
            {customer.name}
          </Title>
          
          <Text type="secondary" style={{ display: 'block', marginBottom: 4 }}>
            Customer Code: {customer.code}
          </Text>
          
          {customer.email && (
            <Text type="secondary" style={{ display: 'block', marginBottom: 4 }}>
              Email: {customer.email}
            </Text>
          )}
          
          {customer.phone && (
            <Text type="secondary" style={{ display: 'block', marginBottom: 4 }}>
              Phone: {customer.phone}
            </Text>
          )}
          
          {customer.city && (
            <Text type="secondary" style={{ display: 'block' }}>
              Location: {customer.city}
            </Text>
          )}
        </div>

        <Text style={{ textAlign: 'center', display: 'block' }}>
          Are you sure you want to delete this customer? This will also remove all related data including visits, orders, and contacts.
        </Text>
      </Space>
    </Modal>
  );
}
