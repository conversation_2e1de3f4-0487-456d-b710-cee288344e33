import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  Paragraph,
  Button,
  ActivityIndicator,
  Chip,
  Divider,
  Surface,
  FAB,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { format, parseISO } from 'date-fns';
import { visitService } from '../../services/visitService.js';
import { useNavigation, useRoute } from '@react-navigation/native';

const VisitDetailScreen = ({ navigation, route }) => {
  const { visitId } = route.params;
  const [visit, setVisit] = useState(null);
  const [photos, setPhotos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [photosLoading, setPhotosLoading] = useState(false);

  useEffect(() => {
    loadVisitDetails();
    loadVisitPhotos();
  }, []);

  const loadVisitDetails = async () => {
    try {
      const visitData = await visitService.getVisit(visitId);
      setVisit(visitData);
    } catch (error) {
      Alert.alert('Error', 'Failed to load visit details');
      console.error('Load visit error:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadVisitPhotos = async () => {
    try {
      setPhotosLoading(true);
      const photosData = await visitService.getVisitPhotos(visitId);
      setPhotos(photosData);
    } catch (error) {
      console.error('Load photos error:', error);
    } finally {
      setPhotosLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'planned':
        return '#2196F3';
      case 'in_progress':
        return '#FF9800';
      case 'completed':
        return '#4CAF50';
      case 'cancelled':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'planned':
        return 'Planned';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const formatDateTime = (dateString) => {
    return format(parseISO(dateString), 'MMM dd, yyyy - HH:mm');
  };

  const calculateVisitDuration = () => {
    if (!visit?.actual_start_time || !visit?.actual_end_time) {
      return null;
    }
    const start = parseISO(visit.actual_start_time);
    const end = parseISO(visit.actual_end_time);
    const durationMs = end.getTime() - start.getTime();
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const handleCheckIn = () => {
    if (visit?.status !== 'planned') {
      Alert.alert('Error', 'Can only check in to planned visits');
      return;
    }
    navigation.navigate('VisitCheckIn', { visitId: visit.id });
  };

  const handleEdit = () => {
    navigation.navigate('EditVisit', { visitId: visit.id });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Loading visit details...</Text>
      </View>
    );
  }

  if (!visit) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Visit not found</Text>
        <Button onPress={() => navigation.goBack()}>Go Back</Button>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
      {/* Visit Info Card */}
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.header}>
            <View style={styles.customerInfo}>
              <Title style={styles.customerName}>
                {visit.customer?.name || 'Unknown Customer'}
              </Title>
              <Paragraph style={styles.visitTime}>
                {formatDateTime(visit.planned_date)}
              </Paragraph>
            </View>
            <Chip
              mode="outlined"
              style={[styles.statusChip, { borderColor: getStatusColor(visit.status) }]}
              textStyle={{ color: getStatusColor(visit.status) }}
            >
              {getStatusLabel(visit.status)}
            </Chip>
          </View>

          {visit.purpose && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Purpose</Text>
              <Paragraph>{visit.purpose}</Paragraph>
            </View>
          )}

          {visit.notes && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Notes</Text>
              <Paragraph>{visit.notes}</Paragraph>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Visit Timeline Card */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.cardTitle}>Visit Timeline</Title>
          
          <View style={styles.timelineItem}>
            <MaterialIcons name="schedule" size={20} color="#2196F3" />
            <View style={styles.timelineContent}>
              <Text style={styles.timelineLabel}>Planned</Text>
              <Text style={styles.timelineValue}>
                {formatDateTime(visit.planned_date)}
              </Text>
            </View>
          </View>

          {visit.actual_start_time && (
            <View style={styles.timelineItem}>
              <MaterialIcons name="play-arrow" size={20} color="#4CAF50" />
              <View style={styles.timelineContent}>
                <Text style={styles.timelineLabel}>Check In</Text>
                <Text style={styles.timelineValue}>
                  {formatDateTime(visit.actual_start_time)}
                </Text>
              </View>
            </View>
          )}

          {visit.actual_end_time && (
            <View style={styles.timelineItem}>
              <MaterialIcons name="stop" size={20} color="#FF9800" />
              <View style={styles.timelineContent}>
                <Text style={styles.timelineLabel}>Check Out</Text>
                <Text style={styles.timelineValue}>
                  {formatDateTime(visit.actual_end_time)}
                </Text>
              </View>
            </View>
          )}

          {calculateVisitDuration() && (
            <View style={styles.durationContainer}>
              <Text style={styles.durationLabel}>Duration: </Text>
              <Text style={styles.durationValue}>{calculateVisitDuration()}</Text>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Visit Outcome Card */}
      {(visit.outcome || visit.next_action) && (
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.cardTitle}>Visit Outcome</Title>
            
            {visit.outcome && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Outcome</Text>
                <Paragraph>{visit.outcome}</Paragraph>
              </View>
            )}

            {visit.next_action && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Next Action</Text>
                <Paragraph>{visit.next_action}</Paragraph>
              </View>
            )}
          </Card.Content>
        </Card>
      )}

      {/* Photos Section */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.cardTitle}>Photos</Title>
          {photosLoading ? (
            <ActivityIndicator size="small" color="#2196F3" />
          ) : photos.length > 0 ? (
            <ScrollView horizontal style={styles.photosContainer}>
              {photos.map((photo) => (
                <TouchableOpacity key={photo.id} style={styles.photoItem}>
                  <Image source={{ uri: photo.photo_url }} style={styles.photoImage} />
                  {photo.caption && (
                    <Text style={styles.photoCaption}>{photo.caption}</Text>
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          ) : (
            <Text style={styles.noPhotosText}>No photos available</Text>
          )}
        </Card.Content>
      </Card>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        {visit.status === 'planned' && (
          <Button
            mode="contained"
            onPress={handleCheckIn}
            style={styles.checkInButton}
          >
            Check In
          </Button>
        )}
        
        <Button
          mode="outlined"
          onPress={handleEdit}
          style={styles.editButton}
        >
          Edit Visit
        </Button>
      </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    marginBottom: 20,
    textAlign: 'center',
  },
  card: {
    margin: 16,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  customerInfo: {
    flex: 1,
    marginRight: 12,
  },
  customerName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  visitTime: {
    fontSize: 14,
    color: '#666',
  },
  statusChip: {
    height: 32,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  timelineContent: {
    marginLeft: 12,
    flex: 1,
  },
  timelineLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  timelineValue: {
    fontSize: 14,
    color: '#666',
  },
  durationContainer: {
    flexDirection: 'row',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  durationLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  durationValue: {
    fontSize: 14,
    color: '#2196F3',
    fontWeight: 'bold',
  },
  photosContainer: {
    marginTop: 8,
  },
  photoItem: {
    marginRight: 12,
    width: 120,
  },
  photoImage: {
    width: 120,
    height: 90,
    borderRadius: 8,
  },
  photoCaption: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    textAlign: 'center',
  },
  noPhotosText: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
  },
  actionContainer: {
    padding: 16,
    gap: 12,
  },
  checkInButton: {
    backgroundColor: '#4CAF50',
  },
  editButton: {
    borderColor: '#2196F3',
  },
});

export default VisitDetailScreen;
