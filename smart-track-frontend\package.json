{"name": "smart-track-dashboard", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "NODE_OPTIONS='--max-old-space-size=4096' next build", "start:prod": "NODE_OPTIONS='--max-old-space-size=4096' next start", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next out", "clean:prod": "rm -rf .next out && rm -rf node_modules"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/nextjs-registry": "^1.1.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.1.5", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.12.2", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "antd": "^5.27.0", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "dayjs": "^1.11.10", "lucide-react": "^0.294.0", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.62.0", "recharts": "^2.8.0", "tailwind-merge": "^2.6.0", "typescript": "^5.3.3", "zod": "^3.25.76", "zustand": "^4.5.7"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.84.2", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.5.6", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss": "^4.1.11"}, "pnpm": {"prefer-frozen-lockfile": true, "strict-peer-dependencies": false, "use-node-version": "22", "onlyBuiltDependencies": ["@tailwindcss/oxide", "unrs-resolver"]}}