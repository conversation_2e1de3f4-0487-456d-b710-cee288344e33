/* Enhanced Visit Tracking Styles */

.visitTrackingContainer {
  padding: 0 24px;
}

.headerCard {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.statsCard {
  border-radius: 8px;
  transition: transform 0.2s ease-in-out;
}

.statsCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.filterCard {
  border-radius: 8px;
  background: linear-gradient(135deg, #f6f9ff 0%, #f0f7ff 100%);
}

.tableCard {
  border-radius: 8px;
  overflow: hidden;
}

/* Status Badge Animations */
.statusBadge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Loading Animation */
.loadingContainer {
  text-align: center;
  padding: 60px 0;
  background: linear-gradient(135deg, #f6f9ff 0%, #f0f7ff 100%);
  border-radius: 12px;
}

/* Custom Button Styles */
.actionButton {
  transition: all 0.3s ease;
}

.actionButton:hover {
  transform: scale(1.1);
}

/* Table Container */
.tableContainer {
  border-radius: 8px;
  overflow: hidden;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .visitTrackingContainer {
    padding: 0 12px;
  }
  
  .filterCard .ant-row {
    gap: 12px;
  }
}
