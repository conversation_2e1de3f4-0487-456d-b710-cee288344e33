import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { LoginRequest, LoginResponse, User, Customer, Visit, WeeklyPlan, DashboardStats } from '../types';

// Helper function to get token with expiry check
const getTokenWithExpiry = (key: string): string | null => {
  if (typeof window === 'undefined') return null;
  
  const itemStr = localStorage.getItem(key);
  if (!itemStr) return null;
  
  try {
    const item = JSON.parse(itemStr);
    const now = new Date();
    
    if (now.getTime() > item.expiry) {
      localStorage.removeItem(key);
      return null;
    }
    
    return item.value;
  } catch (error) {
    // If parsing fails, treat as regular string token (backward compatibility)
    return itemStr;
  }
};

class ApiService {
  private api: AxiosInstance;

  constructor() {
    // Use the base URL from environment or default
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8004/api/v1';
      
    console.log('🔧 API Service initialized with baseURL:', baseUrl);
      
    this.api = axios.create({
      baseURL: baseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
      maxRedirects: 5, // Handle redirects properly
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = getTokenWithExpiry('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle auth errors
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired, clear tokens and redirect
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('user_data');
          if (typeof window !== 'undefined') {
            window.location.href = '/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  // Authentication
  async login(credentials: LoginRequest): Promise<AxiosResponse<LoginResponse>> {
    const formData = new FormData();
    formData.append('username', credentials.username);
    formData.append('password', credentials.password);
    
    return this.api.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
  }

  async refreshToken(token: string): Promise<AxiosResponse<LoginResponse>> {
    return this.api.post('/auth/refresh', { refresh_token: token });
  }

  async getCurrentUser(): Promise<AxiosResponse<User>> {
    return this.api.get('/auth/me');
  }

  // Users
  async getUsers(page: number = 1, limit: number = 10): Promise<AxiosResponse<User[]>> {
    const skip = (page - 1) * limit;
    console.log('🔥 getUsers called with URL:', this.api.defaults.baseURL + `/users/?skip=${skip}&limit=${limit}`);
    return this.api.get(`/users/?skip=${skip}&limit=${limit}`);
  }

  async getUserById(id: number): Promise<AxiosResponse<User>> {
    return this.api.get(`/users/${id}`);
  }

  async createUser(user: Partial<User>): Promise<AxiosResponse<User>> {
    console.log('🔥 createUser called with URL:', this.api.defaults.baseURL + '/users');
    console.log('📝 User data:', user);
    return this.api.post('/users/', user);
  }

  async updateUser(id: number, user: Partial<User>): Promise<AxiosResponse<User>> {
    return this.api.put(`/users/${id}`, user);
  }

  async deleteUser(id: number): Promise<AxiosResponse<void>> {
    return this.api.delete(`/users/${id}`);
  }

  // Roles
  async getRoles(): Promise<AxiosResponse<any[]>> {
    console.log('🔥 getRoles called with URL:', this.api.defaults.baseURL + '/users/roles');
    return this.api.get('/users/roles');
  }

  // Customers
  async getCustomers(page: number = 1, limit: number = 10): Promise<AxiosResponse<Customer[]>> {
    const skip = (page - 1) * limit;
    console.log('🔥 getCustomers called with URL:', this.api.defaults.baseURL + `/customers/?skip=${skip}&limit=${limit}`);
    return this.api.get(`/customers/?skip=${skip}&limit=${limit}`);
  }

  async getCustomerById(id: number): Promise<AxiosResponse<Customer>> {
    return this.api.get(`/customers/${id}`);
  }

  async createCustomer(customer: Partial<Customer>): Promise<AxiosResponse<Customer>> {
    console.log('🔥 createCustomer called with URL:', this.api.defaults.baseURL + '/customers/');
    console.log('📝 Customer data:', customer);
    return this.api.post('/customers/', customer);
  }

  async updateCustomer(id: number, customer: Partial<Customer>): Promise<AxiosResponse<Customer>> {
    return this.api.put(`/customers/${id}`, customer);
  }

  async deleteCustomer(id: number): Promise<AxiosResponse<void>> {
    return this.api.delete(`/customers/${id}`);
  }

  // Visits
  async getVisits(page: number = 1, limit: number = 10, status?: string, searchTerm?: string): Promise<AxiosResponse<Visit[]>> {
    const params = new URLSearchParams();
    params.append('skip', ((page - 1) * limit).toString());
    params.append('limit', limit.toString());
    if (status) params.append('status', status);
    if (searchTerm) params.append('search', searchTerm);
    // Use admin endpoint for dashboard that doesn't require authentication for specific user
    return this.api.get(`/visits/admin/all?${params.toString()}`);
  }

  async getVisitById(id: number): Promise<AxiosResponse<Visit>> {
    return this.api.get(`/visits/admin/${id}`);
  }

  async createVisit(visit: Partial<Visit>): Promise<AxiosResponse<Visit>> {
    return this.api.post('/visits/admin/create', visit);
  }

  async updateVisit(id: number, visit: Partial<Visit>): Promise<AxiosResponse<Visit>> {
    return this.api.put(`/visits/admin/${id}`, visit);
  }

  async deleteVisit(id: number): Promise<AxiosResponse<void>> {
    return this.api.delete(`/visits/admin/${id}`);
  }

  // Weekly Plans
  async getWeeklyPlans(userId?: number): Promise<AxiosResponse<WeeklyPlan[]>> {
    const params = userId ? `?user_id=${userId}` : '';
    return this.api.get(`/weekly-plans${params}`);
  }

  async createWeeklyPlan(plan: Partial<WeeklyPlan>): Promise<AxiosResponse<WeeklyPlan>> {
    return this.api.post('/weekly-plans', plan);
  }

  async updateWeeklyPlan(id: number, plan: Partial<WeeklyPlan>): Promise<AxiosResponse<WeeklyPlan>> {
    return this.api.put(`/weekly-plans/${id}`, plan);
  }

  async deleteWeeklyPlan(id: number): Promise<AxiosResponse<void>> {
    return this.api.delete(`/weekly-plans/${id}`);
  }

  // Dashboard
  async getDashboardStats(): Promise<AxiosResponse<DashboardStats>> {
    return this.api.get('/dashboard/');
  }

  // Distribution Channels
  async getDistributionChannels(): Promise<AxiosResponse<any[]>> {
    return this.api.get('/distribution-channels');
  }

  // Branches (for customers)
  async getBranches(): Promise<AxiosResponse<any[]>> {
    return this.api.get('/branches');
  }

  async getManagerTeam(managerId: number): Promise<AxiosResponse<{ supervisors: User[], salesreps: User[] }>> {
    return this.api.get(`/users/manager/${managerId}/team`);
  }

  // Weekly Plans - Supervisor functionality
  async getSupervisorTeam(supervisorId: number): Promise<AxiosResponse<any>> {
    return this.api.get(`/supervisor/${supervisorId}/my-team`);
  }

  async getSupervisorWeeklyPlans(supervisorId: number, weekStart?: string): Promise<AxiosResponse<any>> {
    const params = new URLSearchParams({ supervisor_id: supervisorId.toString() });
    if (weekStart) {
      params.append('week_start', weekStart);
    }
    return this.api.get(`/weekly-plans/?${params}`);
  }

  async createVisitForSalesrep(supervisorId: number, salesrepId: number, visit: Partial<Visit>): Promise<AxiosResponse<Visit>> {
    return this.api.post(`/weekly-plans/visits?supervisor_id=${supervisorId}&salesrep_id=${salesrepId}`, visit);
  }

  async updateTeamVisit(visitId: number, visit: Partial<Visit>): Promise<AxiosResponse<Visit>> {
    return this.api.put(`/weekly-plans/visits/${visitId}`, visit);
  }

  async deleteTeamVisit(supervisorId: number, visitId: number): Promise<AxiosResponse<void>> {
    return this.api.delete(`/weekly-plans/supervisor/${supervisorId}/visits/${visitId}`);
  }

  async getSupervisorCustomers(supervisorId: number): Promise<AxiosResponse<Customer[]>> {
    return this.api.get(`/weekly-plans/supervisor/${supervisorId}/customers`);
  }

  // Supervisor visit tracking
  async getSupervisorTeamVisits(
    supervisorId: number, 
    skip: number = 0, 
    limit: number = 100, 
    status?: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<AxiosResponse<Visit[]>> {
    const params = new URLSearchParams();
    if (skip) params.append('skip', skip.toString());
    if (limit) params.append('limit', limit.toString());
    if (status) params.append('status', status);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    
    const queryString = params.toString();
    const url = `/weekly-plans/supervisor/${supervisorId}/visits${queryString ? `?${queryString}` : ''}`;
    return this.api.get(url);
  }
}

export const apiService = new ApiService();
export const api = apiService; // Add this alias for backward compatibility
