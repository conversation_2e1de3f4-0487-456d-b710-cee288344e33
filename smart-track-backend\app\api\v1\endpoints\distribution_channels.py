from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.customer import DistributionChannel, CustomerDistributionChannel, Customer
from app.schemas.customer import (
    DistributionChannelResponse,
    CustomerResponse,
    CustomerDistributionChannelResponse
)

router = APIRouter()

@router.get("/", response_model=List[DistributionChannelResponse])
async def list_distribution_channels(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get list of distribution channels"""
    query = db.query(DistributionChannel)
    
    if is_active is not None:
        query = query.filter(DistributionChannel.is_active == is_active)
    
    channels = query.offset(skip).limit(limit).all()
    return channels

@router.get("/{channel_id}", response_model=DistributionChannelResponse)
async def get_distribution_channel(
    channel_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get distribution channel by ID"""
    channel = db.query(DistributionChannel).filter(
        DistributionChannel.id == channel_id
    ).first()
    
    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Distribution channel not found"
        )
    
    return channel

@router.get("/{channel_id}/customers", response_model=List[CustomerResponse])
async def get_channel_customers(
    channel_id: int,
    skip: int = 0,
    limit: int = 100,
    sales_rep_id: Optional[int] = Query(None, description="Filter by sales rep"),
    is_active: Optional[bool] = Query(None, description="Filter by active assignments"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get customers assigned to a distribution channel"""
    # Verify channel exists
    channel = db.query(DistributionChannel).filter(
        DistributionChannel.id == channel_id
    ).first()
    
    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Distribution channel not found"
        )
    
    # Build query for customers in this channel
    query = db.query(Customer).join(CustomerDistributionChannel).filter(
        CustomerDistributionChannel.distribution_channel_id == channel_id
    )
    
    if sales_rep_id:
        query = query.filter(CustomerDistributionChannel.sales_rep_id == sales_rep_id)
    
    if is_active is not None:
        query = query.filter(CustomerDistributionChannel.is_active == is_active)
    
    # Only show active customers by default
    query = query.filter(Customer.is_active == True)
    
    customers = query.offset(skip).limit(limit).all()
    return customers

@router.get("/{channel_id}/assignments", response_model=List[CustomerDistributionChannelResponse])
async def get_channel_assignments(
    channel_id: int,
    skip: int = 0,
    limit: int = 100,
    sales_rep_id: Optional[int] = Query(None, description="Filter by sales rep"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get customer-distribution channel assignments for a specific channel"""
    # Verify channel exists
    channel = db.query(DistributionChannel).filter(
        DistributionChannel.id == channel_id
    ).first()
    
    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Distribution channel not found"
        )
    
    query = db.query(CustomerDistributionChannel).filter(
        CustomerDistributionChannel.distribution_channel_id == channel_id
    )
    
    if sales_rep_id:
        query = query.filter(CustomerDistributionChannel.sales_rep_id == sales_rep_id)
    
    assignments = query.offset(skip).limit(limit).all()
    return assignments

@router.get("/{channel_id}/sales-reps", response_model=List[dict])
async def get_channel_sales_reps(
    channel_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get unique sales reps assigned to customers in this distribution channel"""
    # Verify channel exists
    channel = db.query(DistributionChannel).filter(
        DistributionChannel.id == channel_id
    ).first()
    
    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Distribution channel not found"
        )
    
    # Get unique sales reps with customer counts
    sales_reps = db.query(
        User.id,
        User.sap_code,
        User.username,
        User.full_name,
        db.func.count(CustomerDistributionChannel.customer_id).label('customer_count')
    ).join(CustomerDistributionChannel).filter(
        CustomerDistributionChannel.distribution_channel_id == channel_id,
        CustomerDistributionChannel.is_active == True
    ).group_by(User.id, User.sap_code, User.username, User.full_name).all()
    
    return [
        {
            "id": rep.id,
            "sap_code": rep.sap_code,
            "username": rep.username,
            "full_name": rep.full_name,
            "customer_count": rep.customer_count
        }
        for rep in sales_reps
    ]
