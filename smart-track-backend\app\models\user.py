from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    sap_code = Column(String(20), unique=True, index=True, nullable=False)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=False)
    phone = Column(String(15))
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    role_id = Column(Integer, ForeignKey("roles.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    role = relationship("Role", back_populates="users")
    profile = relationship("UserProfile", back_populates="user", uselist=False, foreign_keys="UserProfile.user_id")
    visits = relationship("Visit", back_populates="user")
    # Supervisor relationship - users that this user supervises
    supervised_users = relationship("UserProfile", foreign_keys="UserProfile.supervisor_id", back_populates="supervisor")
    # Distribution channels that this user manages
    managed_distribution_channels = relationship("DistributionChannel", foreign_keys="DistributionChannel.manager_id", back_populates="manager")

class Role(Base):
    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    users = relationship("User", back_populates="role")
    permissions = relationship("Permission", secondary="role_permissions", back_populates="roles")

class Permission(Base):
    __tablename__ = "permissions"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(Text)
    resource = Column(String(50), nullable=False)  # e.g., 'visits', 'customers', 'reports'
    action = Column(String(20), nullable=False)    # e.g., 'create', 'read', 'update', 'delete'
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    roles = relationship("Role", secondary="role_permissions", back_populates="permissions")

class RolePermission(Base):
    __tablename__ = "role_permissions"

    role_id = Column(Integer, ForeignKey("roles.id"), primary_key=True)
    permission_id = Column(Integer, ForeignKey("permissions.id"), primary_key=True)

class UserProfile(Base):
    __tablename__ = "user_profiles"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, nullable=False)
    distribution_channel_id = Column(Integer, ForeignKey("distribution_channels.id"))
    role_type = Column(String(20))  # 'manager', 'supervisor', 'salesrep'
    supervisor_id = Column(Integer, ForeignKey("users.id"))  # Many sales reps can have one supervisor
    profile_image = Column(String(255))
    address = Column(Text)
    emergency_contact = Column(String(15))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="profile", foreign_keys=[user_id])
    supervisor = relationship("User", foreign_keys=[supervisor_id], remote_side="User.id")
    distribution_channel = relationship("DistributionChannel", back_populates="users")
