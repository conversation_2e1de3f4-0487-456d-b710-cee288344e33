"""Create visits table

Revision ID: 005
Revises: 004
Create Date: 2025-08-10 10:04:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '005'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create visits table
    op.create_table('visits',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('customer_id', sa.Integer(), nullable=False),
    sa.Column('planned_date', sa.DateTime(), nullable=False),
    sa.Column('actual_start_time', sa.DateTime(), nullable=True),
    sa.Column('actual_end_time', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('checkin_latitude', sa.Float(), nullable=True),
    sa.Column('checkin_longitude', sa.Float(), nullable=True),
    sa.Column('checkout_latitude', sa.Float(), nullable=True),
    sa.Column('checkout_longitude', sa.Float(), nullable=True),
    sa.Column('purpose', sa.String(length=100), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('outcome', sa.Text(), nullable=True),
    sa.Column('next_action', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_visits_id'), 'visits', ['id'], unique=False)


def downgrade() -> None:
    op.drop_table('visits')
