// Test component to verify the enhanced Team Dashboard
import React from 'react';
import TeamOverview from '@/components/supervisor/TeamOverview';

// Mock user data for testing
const mockSupervisorUser = {
  id: 1,
  username: 'supervisor1',
  email: '<EMAIL>',
  full_name: 'John Supervisor',
  sap_code: 'SUP001',
  is_active: true,
  is_verified: true,
  role_id: 2,
  created_at: '2024-01-01T00:00:00Z',
  role: {
    id: 2,
    name: 'supervisor',
    description: 'Team Supervisor',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z'
  }
};

export default function TeamDashboardTest() {
  return (
    <div style={{ padding: '20px', background: '#f5f5f5', minHeight: '100vh' }}>
      <h1>Enhanced Team Dashboard Test</h1>
      <TeamOverview user={mockSupervisorUser} />
    </div>
  );
}
