# Smart Track Backend

A FastAPI-based backend for the Smart Track sales management system with distribution channel support.

## 🚀 Quick Start with Docker

### Prerequisites
- Docker and Docker Compose
- Git

### Development Setup
```bash
# Clone and navigate to the project
git clone <repository-url>
cd smart-track-backend

# Start development environment
docker-compose up -d

# View logs
docker-compose logs -f backend
```

The API will be available at `http://localhost:8000`

### Production Deployment
```bash
# Build and run production containers
docker-compose -f docker-compose.prod.yml up -d

# Or with custom environment variables
POSTGRES_PASSWORD=your_secure_password docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 Development with UV

This project uses [uv](https://docs.astral.sh/uv/) for ultra-fast Python package management.

### Local Development Setup
```bash
# Install uv (if not already installed)
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install dependencies
uv sync

# Activate virtual environment
source .venv/bin/activate  # Linux/macOS
# or
.venv\Scripts\activate     # Windows

# Run the application
uv run uvicorn app.main:app --reload

# Run tests
uv run pytest

# Install new packages
uv add package-name

# Install development dependencies
uv add --dev package-name
```

### Database Setup
```bash
# Initialize database (run once)
uv run python init_database.py

# Run migrations (when available)
uv run alembic upgrade head
```

## 📚 API Documentation

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## 🏗️ Project Structure

```
smart-track-backend/
├── app/
│   ├── api/v1/endpoints/          # API endpoints
│   ├── core/                      # Core functionality
│   ├── models/                    # Database models
│   └── schemas/                   # Pydantic schemas
├── tests/                         # Test files
├── docs/                          # Documentation
├── scripts/                       # Utility scripts
├── pyproject.toml                 # Project configuration (uv)
├── uv.lock                        # Locked dependencies
├── Dockerfile                     # Multi-stage Docker build
├── docker-compose.yml             # Development environment
├── docker-compose.prod.yml        # Production environment
└── docker-compose.override.yml    # Development overrides
```

## 🔄 Available Commands

### Docker Commands
```bash
# Development
docker-compose up -d                    # Start development environment
docker-compose down                     # Stop and remove containers
docker-compose logs -f backend          # View backend logs
docker-compose exec backend bash        # Access backend container

# Production
docker-compose -f docker-compose.prod.yml up -d
docker-compose -f docker-compose.prod.yml down

# Rebuild after changes
docker-compose build --no-cache backend
```

### UV Commands
```bash
# Package management
uv add fastapi                          # Add package
uv remove package-name                  # Remove package
uv sync                                 # Install all dependencies
uv sync --frozen                        # Install exact versions from lock

# Running applications
uv run python script.py                 # Run Python script
uv run uvicorn app.main:app --reload    # Run FastAPI with reload
uv run pytest                           # Run tests
uv run pytest --cov                     # Run tests with coverage

# Environment management
uv venv                                 # Create virtual environment
uv pip list                             # List installed packages
```

## 🌟 Features

- **Ultra-fast dependency management** with uv
- **Multi-stage Docker builds** for optimized production images
- **Distribution channel management** system
- **User role-based access control**
- **Customer relationship management**
- **Visit tracking and management**
- **RESTful API** with automatic documentation
- **PostgreSQL database** with SQLAlchemy ORM
- **Redis caching** support
- **Comprehensive test suite**

## 🔐 Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/dbname
REDIS_URL=redis://localhost:6379/0

# Authentication
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Environment
ENVIRONMENT=development  # or production
DEBUG=true              # or false
```

## 🚀 Performance

### Why UV?
- **10-100x faster** than pip for package resolution
- **Consistent, reproducible** builds with `uv.lock`
- **Smaller Docker images** with multi-stage builds
- **Better dependency resolution** algorithm

### Docker Optimization
- Multi-stage builds for minimal production images
- Efficient layer caching with uv
- Development vs production configurations
- Volume mounts optimized for uv workflows

## 📖 API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh token
- `GET /api/v1/auth/me` - Get current user

### Users
- `GET /api/v1/users/` - List users
- `POST /api/v1/users/` - Create user
- `GET /api/v1/users/{id}` - Get user details
- `PUT /api/v1/users/{id}` - Update user

### Customers
- `GET /api/v1/customers/` - List customers
- `POST /api/v1/customers/` - Create customer
- `GET /api/v1/customers/{id}/distribution-channels` - Get customer channels

### Distribution Channels
- `GET /api/v1/distribution-channels/` - List channels
- `GET /api/v1/distribution-channels/{id}/customers` - Get channel customers

### Visits
- `GET /api/v1/visits/` - List visits
- `POST /api/v1/visits/` - Create visit
- `POST /api/v1/visits/{id}/checkin` - Check in to visit

## 🧪 Testing

```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=app

# Run specific test file
uv run pytest tests/test_auth.py

# Run tests in Docker
docker-compose exec backend uv run pytest
```

## 📄 License

This project is licensed under the MIT License.