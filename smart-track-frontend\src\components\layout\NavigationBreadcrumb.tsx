'use client';

import { usePathname } from 'next/navigation';
import { Breadcrumb } from 'antd';
import { HomeOutlined } from '@ant-design/icons';
import Link from 'next/link';

interface BreadcrumbItem {
  title: string;
  href?: string;
  icon?: React.ReactNode;
}

interface NavigationBreadcrumbProps {
  customItems?: BreadcrumbItem[];
  darkMode?: boolean;
}

export default function NavigationBreadcrumb({ 
  customItems, 
  darkMode = false 
}: NavigationBreadcrumbProps) {
  const pathname = usePathname();

  // Generate breadcrumb items from pathname if custom items not provided
  const generateBreadcrumbItems = (): BreadcrumbItem[] => {
    if (customItems) return customItems;

    const pathSegments = pathname.split('/').filter(Boolean);
    const items: BreadcrumbItem[] = [
      {
        title: 'Home',
        href: '/dashboard',
        icon: <HomeOutlined />,
      },
    ];

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      // Skip the first segment if it's 'dashboard'
      if (segment === 'dashboard' && index === 0) return;

      // Capitalize and format segment name
      const title = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      items.push({
        title,
        href: index === pathSegments.length - 1 ? undefined : currentPath, // Last item shouldn't be clickable
      });
    });

    return items;
  };

  const breadcrumbItems = generateBreadcrumbItems().map((item, index) => ({
    key: index,
    title: item.href ? (
      <Link 
        href={item.href}
        className={`${
          darkMode 
            ? 'text-gray-300 hover:text-white' 
            : 'text-gray-600 hover:text-gray-900'
        } transition-colors`}
      >
        <span className="flex items-center space-x-1">
          {item.icon && <span>{item.icon}</span>}
          <span>{item.title}</span>
        </span>
      </Link>
    ) : (
      <span className={`flex items-center space-x-1 ${
        darkMode ? 'text-gray-100' : 'text-gray-900'
      }`}>
        {item.icon && <span>{item.icon}</span>}
        <span>{item.title}</span>
      </span>
    ),
  }));

  return (
    <div className={`px-4 lg:px-6 py-3 border-b ${
      darkMode 
        ? 'bg-gray-800 border-gray-700' 
        : 'bg-white border-gray-200'
    }`}>
      <Breadcrumb
        items={breadcrumbItems}
        className={darkMode ? 'text-gray-300' : 'text-gray-600'}
      />
    </div>
  );
}
