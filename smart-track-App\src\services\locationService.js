import * as Location from 'expo-location';
import { Platform, Alert, Linking } from 'react-native';

class LocationService {
  constructor() {
    this.watchId = null;
  }

  async requestLocationPermission() {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      return status === 'granted';
    } catch (err) {
      console.warn('Location permission request error:', err);
      return false;
    }
  }

  async getCurrentLocation() {
    const hasPermission = await this.requestLocationPermission();
    if (!hasPermission) {
      throw new Error('Location permission denied');
    }

    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        timeout: 15000,
        maximumAge: 10000,
      });

      return {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy,
        altitude: location.coords.altitude || undefined,
        heading: location.coords.heading || undefined,
        speed: location.coords.speed || undefined,
        timestamp: location.timestamp,
      };
    } catch (error) {
      console.error('Location error:', error);
      throw new Error(`Failed to get location: ${error.message}`);
    }
  }

  async watchLocation(onLocationUpdate, onError) {
    const hasPermission = await this.requestLocationPermission();
    if (!hasPermission) {
      onError?.({ code: 1, message: 'Location permission denied' });
      return false;
    }

    try {
      this.watchId = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 5000, // Update every 5 seconds
          distanceInterval: 10, // Update every 10 meters
        },
        (location) => {
          onLocationUpdate({
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy,
            altitude: location.coords.altitude || undefined,
            heading: location.coords.heading || undefined,
            speed: location.coords.speed || undefined,
            timestamp: location.timestamp,
          });
        }
      );
      return true;
    } catch (error) {
      onError?.({
        code: error.code || 999,
        message: this.getLocationErrorMessage(error.code || 999),
      });
      return false;
    }
  }

  stopWatchingLocation() {
    if (this.watchId !== null) {
      this.watchId.remove();
      this.watchId = null;
    }
  }

  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371000; // Earth's radius in meters
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return distance;
  }

  toRadians(degrees) {
    return degrees * (Math.PI / 180);
  }

  getLocationErrorMessage(code) {
    switch (code) {
      case 1:
        return 'Location permission denied';
      case 2:
        return 'Location unavailable';
      case 3:
        return 'Location request timeout';
      default:
        return 'Unknown location error';
    }
  }

  async validateLocationForCustomer(customerLatitude, customerLongitude, maxDistance = 500) {
    try {
      const currentLocation = await this.getCurrentLocation();
      const distance = this.calculateDistance(
        currentLocation.latitude,
        currentLocation.longitude,
        customerLatitude,
        customerLongitude
      );

      return {
        isValid: distance <= maxDistance,
        distance: distance,
        accuracy: currentLocation.accuracy,
        currentLocation: currentLocation,
      };
    } catch (error) {
      throw new Error(`Location validation failed: ${error.message}`);
    }
  }

  showLocationRequiredAlert() {
    Alert.alert(
      'Location Required',
      'This feature requires location access. Please enable location services in your device settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Settings', onPress: () => this.openAppSettings() },
      ]
    );
  }

  openAppSettings() {
    if (Platform.OS === 'ios') {
      Linking.openURL('app-settings:');
    } else {
      Linking.openURL('package:com.yourapp.name');
    }
  }

  formatDistance(meters) {
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    } else {
      return `${(meters / 1000).toFixed(1)}km`;
    }
  }

  getLocationAccuracyDescription(accuracy) {
    if (accuracy <= 5) return 'Excellent';
    if (accuracy <= 10) return 'Good';
    if (accuracy <= 20) return 'Fair';
    return 'Poor';
  }
}

// Export as default
export default new LocationService();
