from sqlalchemy import Column, Integer, String, Bo<PERSON>an, DateTime, ForeignKey, Text, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class Visit(Base):
    __tablename__ = "visits"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    planned_date = Column(DateTime, nullable=False)
    planned_time = Column(String(10))  # Time in HH:MM format
    actual_start_time = Column(DateTime)
    actual_end_time = Column(DateTime)
    status = Column(String(20), default="planned")  # planned, in_progress, completed, cancelled
    
    # GPS Coordinates
    checkin_latitude = Column(Float)
    checkin_longitude = Column(Float)
    checkout_latitude = Column(Float)
    checkout_longitude = Column(Float)
    
    # Visit Details
    purpose = Column(String(100))
    notes = Column(Text)
    outcome = Column(Text)
    next_action = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="visits")
    customer = relationship("Customer", back_populates="visits")
    photos = relationship("VisitPhoto", back_populates="visit")
    notes_records = relationship("VisitNote", back_populates="visit")

class VisitPhoto(Base):
    __tablename__ = "visit_photos"

    id = Column(Integer, primary_key=True, index=True)
    visit_id = Column(Integer, ForeignKey("visits.id"), nullable=False)
    photo_url = Column(String(255), nullable=False)
    photo_type = Column(String(50))  # 'general', 'product_display', 'competitor', 'issue'
    caption = Column(Text)
    latitude = Column(Float)
    longitude = Column(Float)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    visit = relationship("Visit", back_populates="photos")

class VisitNote(Base):
    __tablename__ = "visit_notes"

    id = Column(Integer, primary_key=True, index=True)
    visit_id = Column(Integer, ForeignKey("visits.id"), nullable=False)
    note_type = Column(String(50))  # 'general', 'issue', 'opportunity', 'feedback'
    title = Column(String(100))
    content = Column(Text, nullable=False)
    priority = Column(String(20), default="medium")  # low, medium, high, urgent
    is_resolved = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    visit = relationship("Visit", back_populates="notes_records")


