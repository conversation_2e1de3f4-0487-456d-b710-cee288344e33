from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date, timedelta

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.visit import Visit
from app.models.customer import Customer
from app.schemas.visit import VisitStatus
from app.schemas.dashboard import DashboardData, DashboardKPIs, DashboardTask, DashboardStats

router = APIRouter()

@router.get(
    "/stats", 
    response_model=DashboardStats, 
    summary="Get Dashboard Statistics", 
    description="Get basic dashboard statistics including visit counts, completion rates, and customer metrics",
    tags=["dashboard", "statistics"]
)
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get simple dashboard statistics"""
    
    # Get current date and calculate date ranges
    today = date.today()
    month_start = today.replace(day=1)
    
    # Get visits for current month
    this_month_visits = db.query(Visit).filter(
        Visit.user_id == current_user.id,
        Visit.planned_date >= month_start,
        Visit.planned_date <= today
    ).all()
    
    # Calculate basic stats
    total_visits = len(this_month_visits)
    completed_visits = len([v for v in this_month_visits if v.status == 'completed'])
    pending_visits = len([v for v in this_month_visits if v.status == 'planned'])
    
    # Get total customers count
    total_customers = db.query(Customer).count()
    
    # Calculate completion rate
    completion_rate = (completed_visits / total_visits * 100) if total_visits > 0 else 0
    
    # For active sales reps, count unique users with visits this month (placeholder)
    active_sales_reps = 1  # Current user for now
    
    return DashboardStats(
        total_visits=total_visits,
        completed_visits=completed_visits,
        pending_visits=pending_visits,
        total_customers=total_customers,
        active_sales_reps=active_sales_reps,
        completion_rate=round(completion_rate, 1)
    )

@router.get(
    "/", 
    response_model=DashboardData, 
    summary="Get Dashboard Data", 
    description="Get comprehensive dashboard data including KPIs, upcoming visits, and pending tasks",
    tags=["dashboard", "analytics"]
)
async def get_dashboard_data(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get dashboard data including KPIs, upcoming visits, and pending tasks"""
    
    # Get current date and calculate date ranges
    today = date.today()
    week_start = today - timedelta(days=today.weekday())
    month_start = today.replace(day=1)
    
    # Get visits for different time periods
    this_month_visits = db.query(Visit).filter(
        Visit.user_id == current_user.id,
        Visit.planned_date >= month_start,
        Visit.planned_date <= today
    ).all()
    
    this_week_visits = db.query(Visit).filter(
        Visit.user_id == current_user.id,
        Visit.planned_date >= week_start,
        Visit.planned_date <= today
    ).all()
    
    # Get upcoming visits (next 7 days)
    upcoming_visits = db.query(Visit).filter(
        Visit.user_id == current_user.id,
        Visit.planned_date >= today,
        Visit.planned_date <= today + timedelta(days=7),
        Visit.status.in_(['planned', 'in_progress'])  # Use string values instead of enum
    ).order_by(Visit.planned_date).limit(10).all()
    
    # Calculate KPIs
    completed_visits = len([v for v in this_month_visits if v.status == 'completed'])  # Use string value
    total_planned_visits = len(this_month_visits)
    target_visits = 30  # This could be configurable per user
    completion_percentage = (completed_visits / target_visits * 100) if target_visits > 0 else 0
    
    # Calculate revenue (this would need order/sales data integration)
    revenue = 15750  # Placeholder - integrate with sales/order system
    revenue_target = 20000
    
    # Get unique customers visited this month
    visited_customers = len(set([v.customer_id for v in this_month_visits if v.status == 'completed']))  # Use string value
    total_customers = db.query(Customer).count()  # This could be filtered by user's territory
    
    # Create KPIs object
    kpis = DashboardKPIs(
        completed_visits=completed_visits,
        target_visits=target_visits,
        target_percentage=completion_percentage,
        monthly_target=target_visits,
        revenue=revenue,
        revenue_target=revenue_target,
        clients_visited=visited_customers,
        total_clients=min(total_customers, 25)  # Limit for demo
    )
    
    # Format upcoming visits for dashboard
    formatted_upcoming_visits = []
    for visit in upcoming_visits:
        customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
        
        # Determine visit date display
        if visit.planned_date.date() == today:
            date_display = "Today"
        elif visit.planned_date.date() == today + timedelta(days=1):
            date_display = "Tomorrow"
        else:
            date_display = visit.planned_date.strftime("%a, %b %d")
        
        # Determine priority based on visit type and date
        priority = "High" if visit.planned_date.date() <= today + timedelta(days=1) else "Medium"
        
        # Format time safely - handle cases where time might be missing
        try:
            time_display = visit.planned_date.strftime("%I:%M %p")
        except (AttributeError, ValueError):
            time_display = "9:00 AM"  # Default time if formatting fails
        
        formatted_visit = {
            "id": visit.id,
            "client": customer.name if customer else "Unknown Customer",
            "time": time_display,
            "location": customer.address if customer else "No address",
            "type": visit.purpose or "Scheduled",  # Use purpose field instead of visit_type
            "date": date_display,
            "priority": priority,
            "duration": "45 min",  # Default duration
            "contact": customer.phone if customer else "No contact"
        }
        formatted_upcoming_visits.append(formatted_visit)
    
    # Generate pending tasks based on visits and other activities
    pending_tasks = []
    
    # Add pending visits (visits planned 2 weeks ago that are still planned)
    two_weeks_ago = datetime.now() - timedelta(weeks=2)
    pending_visits = db.query(Visit).filter(
        Visit.user_id == current_user.id,
        Visit.status == 'planned',
        Visit.planned_date <= two_weeks_ago
    ).limit(5).all()
    
    for visit in pending_visits:
        customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
        days_overdue = (datetime.now() - visit.planned_date).days
        task = DashboardTask(
            id=f"pending_visit_{visit.id}",
            title=f"Overdue visit to {customer.name if customer else 'Customer'} ({days_overdue} days)",
            priority="High" if days_overdue > 21 else "Medium",
            due_date=f"{days_overdue} days overdue",
            category="Pending Visit",
            status="Overdue"
        )
        pending_tasks.append(task)
    
    # Add overdue visit reports
    overdue_visits = db.query(Visit).filter(
        Visit.user_id == current_user.id,
        Visit.status == 'completed',  # Use string value
        Visit.actual_end_time.isnot(None),
        Visit.actual_end_time < datetime.now() - timedelta(days=1)
    ).limit(3).all()
    
    for visit in overdue_visits:
        customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
        # Check if visit needs a report (this logic can be customized)
        if not visit.outcome or len(visit.outcome.strip()) < 10:
            task = DashboardTask(
                id=f"report_{visit.id}",
                title=f"Submit visit report for {customer.name if customer else 'Customer'}",
                priority="High",
                due_date="Today",
                category="Report",
                status="Pending"
            )
            pending_tasks.append(task)
    
    # Add follow-up tasks for recent visits
    recent_completed_visits = db.query(Visit).filter(
        Visit.user_id == current_user.id,
        Visit.status == 'completed',  # Use string value
        Visit.actual_end_time.isnot(None),
        Visit.actual_end_time >= datetime.now() - timedelta(days=3)
    ).limit(2).all()
    
    for visit in recent_completed_visits:
        customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
        task = DashboardTask(
            id=f"followup_{visit.id}",
            title=f"Follow up on {customer.name if customer else 'Customer'} visit",
            priority="Medium",
            due_date="Tomorrow",
            category="Follow-up",
            status="Pending"
        )
        pending_tasks.append(task)
    
    return DashboardData(
        kpis=kpis,
        upcoming_visits=formatted_upcoming_visits,
        pending_tasks=pending_tasks[:4]  # Limit to 4 tasks
    )