"""add planned_time to visits

Revision ID: 007_add_planned_time
Revises: 006
Create Date: 2025-01-18 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = '007_add_planned_time'
down_revision = '006'
branch_labels = None
depends_on = None

def upgrade():
    """Add planned_time column to visits table"""
    op.add_column('visits', sa.Column('planned_time', sa.String(10), nullable=True))

def downgrade():
    """Remove planned_time column from visits table"""
    op.drop_column('visits', 'planned_time')
