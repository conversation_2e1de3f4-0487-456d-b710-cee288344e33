'use client';

import { useState, useEffect } from 'react';
import { 
  Dropdown, 
  Badge, 
  List, 
  Button, 
  Typography, 
  Space,
  Divider,
  Empty,
  Tooltip
} from 'antd';
import { 
  BellOutlined, 
  CheckOutlined, 
  DeleteOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { formatDistanceToNow } from 'date-fns';

const { Text } = Typography;

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  createdAt: Date;
  actionUrl?: string;
}

interface NotificationDropdownProps {
  darkMode?: boolean;
}

export default function NotificationDropdown({ darkMode = false }: NotificationDropdownProps) {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      title: 'New Visit Scheduled',
      message: 'A new visit has been scheduled for tomorrow at 10:00 AM',
      type: 'info',
      read: false,
      createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      actionUrl: '/visits/schedule'
    },
    {
      id: '2',
      title: 'Report Generated',
      message: 'Your monthly sales report is ready for review',
      type: 'success',
      read: false,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      actionUrl: '/reports/sales'
    },
    {
      id: '3',
      title: 'System Maintenance',
      message: 'Scheduled maintenance will occur tonight from 2-4 AM',
      type: 'warning',
      read: true,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    }
  ]);

  const [visible, setVisible] = useState(false);

  const unreadCount = notifications.filter(n => !n.read).length;

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const getNotificationIcon = (type: Notification['type']) => {
    const iconProps = { className: 'w-4 h-4' };
    switch (type) {
      case 'success':
        return <CheckOutlined {...iconProps} style={{ color: '#52c41a' }} />;
      case 'warning':
        return <BellOutlined {...iconProps} style={{ color: '#faad14' }} />;
      case 'error':
        return <DeleteOutlined {...iconProps} style={{ color: '#ff4d4f' }} />;
      default:
        return <BellOutlined {...iconProps} style={{ color: '#1890ff' }} />;
    }
  };

  const dropdownContent = (
    <div className={`w-80 max-w-sm ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg border ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
      {/* Header */}
      <div className={`px-4 py-3 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between">
          <Text strong className={darkMode ? 'text-white' : 'text-gray-900'}>
            Notifications
          </Text>
          <Space>
            {unreadCount > 0 && (
              <Button 
                type="link" 
                size="small" 
                onClick={markAllAsRead}
                className={`text-xs ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                Mark all read
              </Button>
            )}
            <Tooltip title="Notification Settings">
              <Button 
                type="text" 
                size="small" 
                icon={<SettingOutlined />}
                className={darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'}
              />
            </Tooltip>
          </Space>
        </div>
      </div>

      {/* Notifications List */}
      <div className="max-h-96 overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="p-8">
            <Empty 
              description={
                <Text className={darkMode ? 'text-gray-400' : 'text-gray-500'}>
                  No notifications
                </Text>
              }
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        ) : (
          <List
            dataSource={notifications}
            renderItem={(notification) => (
              <List.Item
                className={`px-4 py-3 border-none cursor-pointer transition-colors ${
                  !notification.read 
                    ? (darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-blue-50 hover:bg-blue-100')
                    : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50')
                }`}
                onClick={() => markAsRead(notification.id)}
              >
                <div className="flex items-start space-x-3 w-full">
                  <div className="mt-1">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <Text 
                          strong={!notification.read}
                          className={`text-sm ${
                            darkMode ? 'text-white' : 'text-gray-900'
                          }`}
                        >
                          {notification.title}
                        </Text>
                        <p className={`text-xs mt-1 ${
                          darkMode ? 'text-gray-300' : 'text-gray-600'
                        }`}>
                          {notification.message}
                        </p>
                        <Text className={`text-xs ${
                          darkMode ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          {formatDistanceToNow(notification.createdAt, { addSuffix: true })}
                        </Text>
                      </div>
                      <div className="flex flex-col space-y-1 ml-2">
                        {!notification.read && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                        <Button
                          type="text"
                          size="small"
                          icon={<DeleteOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteNotification(notification.id);
                          }}
                          className={`text-xs ${
                            darkMode 
                              ? 'text-gray-400 hover:text-red-400' 
                              : 'text-gray-500 hover:text-red-500'
                          }`}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </List.Item>
            )}
          />
        )}
      </div>

      {/* Footer */}
      {notifications.length > 0 && (
        <>
          <Divider className="my-0" />
          <div className="px-4 py-3">
            <Space className="w-full justify-between">
              <Button 
                type="link" 
                size="small"
                className={`text-xs ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
              >
                View all notifications
              </Button>
              <Button 
                type="link" 
                size="small" 
                danger
                onClick={clearAllNotifications}
                className="text-xs"
              >
                Clear all
              </Button>
            </Space>
          </div>
        </>
      )}
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      open={visible}
      onOpenChange={setVisible}
      placement="bottomRight"
    >
      <Badge count={unreadCount} size="small">
        <Button
          type="text"
          icon={<BellOutlined />}
          className={`${
            darkMode 
              ? 'text-gray-300 hover:text-white hover:bg-gray-700' 
              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
          } border-none`}
        />
      </Badge>
    </Dropdown>
  );
}
