from fastapi import APIRouter, HTTPException, status, Depends
from sqlalchemy.orm import Session
from typing import List

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User, UserProfile
from app.schemas.user import UserDetailResponse

router = APIRouter()

# Simple test endpoint to verify routing works
@router.get("/test-debug", 
           summary="Test Debug Endpoint",
           description="Simple test to verify supervisor routing works",
           tags=["supervisor"])
async def test_debug():
    """Simple test endpoint to verify routing"""
    return {"message": "Supervisor routing works!", "endpoint": "/my-team"}

# Updated endpoint for supervisor team access - v3
# This endpoint should now be properly registered
@router.get("/my-team", 
           response_model=List[UserDetailResponse],
           summary="Get My Team",
           description="Get team members assigned to the currently logged-in supervisor",
           tags=["supervisor"])
async def get_my_team(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get team members for the currently logged-in supervisor"""
    
    # Check if the current user has supervisor role
    if not current_user.profile or current_user.profile.role_type not in ["supervisor", "manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied. User is not a supervisor or manager."
        )
    
    # Get all active users who have this supervisor_id in their profile
    team_members = (
        db.query(User)
        .join(UserProfile, User.id == UserProfile.user_id)
        .filter(
            UserProfile.supervisor_id == current_user.id,
            User.is_active == True
        )
        .all()
    )
    
    return team_members


# New endpoint with supervisor_id parameter - More flexible approach
@router.get("/{supervisor_id}/my-team", 
           response_model=List[UserDetailResponse],
           summary="Get Team by Supervisor ID",
           description="Get team members assigned to a specific supervisor by supervisor ID",
           tags=["supervisor"])
async def get_team_by_supervisor_id(
    supervisor_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get team members for a specific supervisor by supervisor ID"""
    
    # Check if the current user has supervisor/manager role OR is requesting their own team
    if not current_user.profile or current_user.profile.role_type not in ["supervisor", "manager"]:
        # If not a supervisor/manager, only allow access to their own team data
        if current_user.id != supervisor_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied. You can only view your own team or you need supervisor/manager role."
            )
    
    # Verify the supervisor exists and has the right role
    supervisor = db.query(User).filter(User.id == supervisor_id, User.is_active == True).first()
    if not supervisor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supervisor not found or inactive."
        )
    
    if not supervisor.profile or supervisor.profile.role_type not in ["supervisor", "manager"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is not a supervisor or manager."
        )
    
    # Get all active users who have this supervisor_id in their profile
    team_members = (
        db.query(User)
        .join(UserProfile, User.id == UserProfile.user_id)
        .filter(
            UserProfile.supervisor_id == supervisor_id,
            User.is_active == True
        )
        .all()
    )
    
    return team_members
