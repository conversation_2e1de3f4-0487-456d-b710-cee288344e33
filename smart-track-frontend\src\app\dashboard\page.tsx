'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/auth';
import AdminDashboard from '@/components/dashboards/AdminDashboard';
import SupervisorDashboard from '@/components/dashboards/SupervisorDashboard';
import ManagerDashboard from '@/components/dashboards/ManagerDashboard';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

export default function DashboardPage() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, getCurrentUser } = useAuthStore();

  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      router.push('/login');
      return;
    }
    
    if (isAuthenticated && !user) {
      getCurrentUser();
    }
  }, [isAuthenticated, isLoading, user, router, getCurrentUser]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated || !user) {
    return null; // Will redirect to login
  }

  const renderDashboard = () => {
    // Check for admin role first (from role.name), then check profile.role_type
    const roleName = user.role?.name?.toLowerCase();
    const roleType = user.profile?.role_type;
    
    // Determine the user's role for dashboard routing
    let userRole = null;
    
    if (roleName === 'admin') {
      userRole = 'admin';
    } else if (roleType) {
      userRole = roleType;
    }
    
    switch (userRole) {
      case 'admin':
        return <AdminDashboard user={user} />;
      case 'supervisor':
        return <SupervisorDashboard user={user} />;
      case 'manager':
        return <ManagerDashboard user={user} />;
      default:
        return (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Unauthorized Access
              </h2>
              <p className="text-gray-600">
                Your role does not have access to this dashboard.
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Role: {roleName || 'Unknown'} | Type: {roleType || 'Unknown'}
              </p>
            </div>
          </div>
        );
    }
  };

  return renderDashboard();
}
