'use client';

import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Form, 
  Input, 
  Select, 
  Button, 
  message, 
  Space,
  DatePicker,
  Row,
  Col
} from 'antd';
import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';

import { apiService } from '../../services/api';
import { Visit, Customer, User } from '../../types';

const { Option } = Select;
const { TextArea } = Input;

interface EditVisitModalProps {
  isOpen: boolean;
  onClose: () => void;
  visit: Visit | null;
}

export default function EditVisitModal({ isOpen, onClose, visit }: EditVisitModalProps) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Get customers for selection
  const { data: customersData } = useQuery({
    queryKey: ['customers-for-visit'],
    queryFn: () => apiService.getCustomers(1, 100),
    enabled: isOpen,
  });

  // Get users for selection
  const { data: usersData } = useQuery({
    queryKey: ['users-for-visit'],
    queryFn: () => apiService.getUsers(1, 100),
    enabled: isOpen,
  });

  const customers = customersData?.data || [];
  const users = usersData?.data || [];

  const handleSubmit = async (values: any) => {
    if (!visit) return;

    try {
      setLoading(true);
      console.log('📝 Updating visit with values:', values);

      const visitData = {
        customer_id: values.customer_id,
        user_id: values.user_id,
        planned_date: values.planned_date.toISOString(),
        purpose: values.purpose,
        notes: values.notes,
        status: values.status,
        outcome: values.outcome,
        next_action: values.next_action
      };

      console.log('📝 Visit data to send:', visitData);

      const response = await apiService.updateVisit(visit.id, visitData);
      console.log('✅ Visit updated successfully:', response.data);

      message.success('Visit updated successfully!');
      onClose();
    } catch (error: any) {
      console.error('❌ Failed to update visit:', error);
      const errorMessage = error.response?.data?.detail || error.message || 'Failed to update visit';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  useEffect(() => {
    if (isOpen && visit) {
      form.setFieldsValue({
        customer_id: visit.customer_id,
        user_id: visit.user_id,
        planned_date: dayjs(visit.planned_date),
        purpose: visit.purpose,
        notes: visit.notes,
        status: visit.status,
        outcome: visit.outcome,
        next_action: visit.next_action
      });
    } else if (!isOpen) {
      form.resetFields();
    }
  }, [isOpen, visit, form]);

  return (
    <Modal
      title="Edit Visit"
      open={isOpen}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        requiredMark={false}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="customer_id"
              label="Customer"
              rules={[{ required: true, message: 'Please select a customer' }]}
            >
              <Select
                placeholder="Select customer"
                showSearch
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {customers.map((customer: Customer) => (
                  <Option key={customer.id} value={customer.id}>
                    {customer.name} - {customer.code}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              name="user_id"
              label="Assigned User"
              rules={[{ required: true, message: 'Please select a user' }]}
            >
              <Select
                placeholder="Select user"
                showSearch
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {users.map((user: User) => (
                  <Option key={user.id} value={user.id}>
                    {user.full_name} - {user.email}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="planned_date"
              label="Planned Date & Time"
              rules={[{ required: true, message: 'Please select planned date and time' }]}
            >
              <DatePicker
                showTime
                style={{ width: '100%' }}
                placeholder="Select date and time"
              />
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              name="status"
              label="Status"
              rules={[{ required: true, message: 'Please select status' }]}
            >
              <Select placeholder="Select status">
                <Option value="planned">Planned</Option>
                <Option value="in_progress">In Progress</Option>
                <Option value="completed">Completed</Option>
                <Option value="cancelled">Cancelled</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="purpose"
          label="Purpose"
          rules={[{ required: true, message: 'Please enter visit purpose' }]}
        >
          <Input placeholder="Enter visit purpose" />
        </Form.Item>

        <Form.Item
          name="notes"
          label="Notes"
        >
          <TextArea 
            rows={2} 
            placeholder="Additional notes (optional)"
          />
        </Form.Item>

        <Form.Item
          name="outcome"
          label="Outcome"
        >
          <TextArea 
            rows={2} 
            placeholder="Visit outcome (optional)"
          />
        </Form.Item>

        <Form.Item
          name="next_action"
          label="Next Action"
        >
          <TextArea 
            rows={2} 
            placeholder="Next action required (optional)"
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Space>
            <Button onClick={handleCancel}>
              Cancel
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              Update Visit
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
}
