'use client';

import { useState, useEffect } from 'react';
import { format, startOfWeek, addDays, parse, isValid } from 'date-fns';
import { User, Visit, Customer } from '@/types';
import { api } from '@/services/api';
import { 
  Card, 
  Row, 
  Col, 
  Avatar, 
  Progress, 
  Button, 
  Typography, 
  Statistic, 
  Calendar,
  Badge,
  Space, 
  Skeleton,
  Empty,
  Tag,
  Tooltip,
  Modal,
  Form,
  Select,
  Input,
  TimePicker,
  message,
  Dropdown,
  Popconfirm,
  Divider,
  DatePicker,
  Alert,
  Spin
} from 'antd';
import { 
  UserOutlined, 
  TeamOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined,
  ReloadOutlined,
  PlusOutlined,
  CalendarOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  LeftOutlined,
  RightOutlined,
  BankOutlined,
  ScheduleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface WeeklyPlanData {
  week_start: string;
  week_end: string;
  supervisor_id: number;
  team_plans: TeamPlan[];
}

interface TeamPlan {
  user_id: number;
  user_name: string;
  user_email: string;
  total_visits: number;
  completed_visits: number;
  daily_plans: Record<string, DailyVisit[]>;
}

interface DailyVisit {
  id: number;
  customer_id: number;
  customer_name: string;
  customer_city: string;
  planned_date: string;
  planned_time?: string;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  purpose?: string;
  notes?: string;
}

interface NewVisit {
  customer_id: number;
  planned_date: string;
  planned_time: string;
  purpose: string;
  notes: string;
}

interface WeeklyPlanManagerProps {
  user?: User;
}

export default function WeeklyPlanManager({ user: propUser }: WeeklyPlanManagerProps) {
  const [weeklyPlans, setWeeklyPlans] = useState<WeeklyPlanData | null>(null);
  const [selectedWeek, setSelectedWeek] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));
  const [team, setTeam] = useState<User[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedSalesrep, setSelectedSalesrep] = useState<number | null>(null);
  const [selectedDay, setSelectedDay] = useState<string>('');
  const [form] = Form.useForm();

  // Use prop user if available
  const user = propUser;

  const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  useEffect(() => {
    if (user?.id) {
      fetchData();
    } else {
      setLoading(false);
    }
  }, [selectedWeek, user?.id]);

  const fetchData = async () => {
    if (!user?.id) {
      console.error('No user ID available');
      setLoading(false);
      return;
    }

    const weekStart = format(selectedWeek, 'yyyy-MM-dd');

    try {
      setLoading(true);
      
      const [weeklyPlansRes, teamRes, customersRes] = await Promise.all([
        api.getSupervisorWeeklyPlans(user.id, weekStart),
        api.getSupervisorTeam(user.id),
        api.getSupervisorCustomers(user.id)
      ]);

      setWeeklyPlans(weeklyPlansRes.data);
      setTeam(teamRes.data);
      setCustomers(customersRes.data);
    } catch (error) {
      console.error('Error fetching weekly plan data:', error);
      message.error('Failed to load weekly plans');
      setWeeklyPlans({
        week_start: weekStart,
        week_end: format(addDays(selectedWeek, 6), 'yyyy-MM-dd'),
        supervisor_id: user.id,
        team_plans: []
      });
      setTeam([]);
      setCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  const navigateWeek = (direction: number) => {
    const newWeek = addDays(selectedWeek, direction * 7);
    setSelectedWeek(newWeek);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'processing';
      case 'planned': return 'warning';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Completed';
      case 'in_progress': return 'In Progress';
      case 'planned': return 'Planned';
      case 'cancelled': return 'Cancelled';
      default: return status;
    }
  };

  const handleAddVisit = (salesrepId: number, day: string) => {
    const salesrep = team.find(member => member.id === salesrepId);
    setSelectedSalesrep(salesrepId);
    setSelectedDay(day);
    
    const dayIndex = weekDays.indexOf(day);
    const plannedDate = addDays(selectedWeek, dayIndex);
    
    form.setFieldsValue({
      customer_id: undefined,
      planned_time: dayjs('09:00', 'HH:mm'),
      purpose: 'Regular visit',
      notes: ''
    });
    
    setShowAddModal(true);
  };

  const handleSubmitVisit = async (values: any) => {
    try {
      if (!selectedSalesrep || !user) return;

      const dayIndex = weekDays.indexOf(selectedDay);
      const plannedDate = addDays(selectedWeek, dayIndex);

      await api.createVisitForSalesrep(user.id, selectedSalesrep, {
        customer_id: values.customer_id,
        planned_date: format(plannedDate, 'yyyy-MM-dd') + 'T' + values.planned_time.format('HH:mm'),
        purpose: values.purpose || 'Regular visit',
        notes: values.notes || ''
      });

      message.success('Visit added successfully!');
      await fetchData();
      setShowAddModal(false);
      form.resetFields();
    } catch (error) {
      console.error('Error creating visit:', error);
      message.error('Failed to create visit. Please try again.');
    }
  };

  const handleDeleteVisit = async (visitId: number) => {
    try {
      if (!user) return;
      await api.deleteTeamVisit(user.id, visitId);
      message.success('Visit deleted successfully!');
      await fetchData();
    } catch (error) {
      console.error('Error deleting visit:', error);
      message.error('Failed to delete visit. Please try again.');
    }
  };

  const renderWeeklyStats = () => {
    if (!weeklyPlans) return null;

    const totalTeamMembers = weeklyPlans.team_plans.length;
    const totalVisits = weeklyPlans.team_plans.reduce((sum, plan) => sum + plan.total_visits, 0);
    const completedVisits = weeklyPlans.team_plans.reduce((sum, plan) => sum + plan.completed_visits, 0);
    const avgCompletion = totalVisits > 0 ? Math.round((completedVisits / totalVisits) * 100) : 0;

    return (
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Team Members"
              value={totalTeamMembers}
              prefix={<TeamOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Visits"
              value={totalVisits}
              prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Completed"
              value={completedVisits}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Completion Rate"
              value={avgCompletion}
              suffix="%"
              prefix={<ClockCircleOutlined style={{ color: '#fa8c16' }} />}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  const renderTeamMemberPlan = (teamPlan: TeamPlan) => {
    const completionRate = teamPlan.total_visits > 0 
      ? Math.round((teamPlan.completed_visits / teamPlan.total_visits) * 100) 
      : 0;

    return (
      <Card 
        key={teamPlan.user_id}
        style={{ marginBottom: 16 }}
        title={
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                <Avatar 
                  size={40} 
                  style={{ backgroundColor: '#1890ff' }}
                  icon={<UserOutlined />}
                >
                  {teamPlan.user_name.split(' ').map(n => n[0]).join('')}
                </Avatar>
                <div>
                  <Title level={5} style={{ margin: 0 }}>{teamPlan.user_name}</Title>
                  <Text type="secondary">{teamPlan.user_email}</Text>
                </div>
              </Space>
            </Col>
            <Col>
              <Space direction="vertical" size={4} style={{ textAlign: 'right' }}>
                <Text strong>
                  {teamPlan.completed_visits}/{teamPlan.total_visits} visits
                </Text>
                <Progress 
                  percent={completionRate} 
                  size="small" 
                  style={{ width: 120 }}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </Space>
            </Col>
          </Row>
        }
      >
        <Row gutter={8}>
          {weekDays.map((day) => {
            const dayVisits = teamPlan.daily_plans[day] || [];
            return (
              <Col span={24 / 7} key={day} style={{ minHeight: 200 }}>
                <Card 
                  size="small" 
                  style={{ height: '100%' }}
                  title={
                    <Row justify="space-between" align="middle">
                      <Col>
                        <Text strong style={{ fontSize: 12 }}>{day}</Text>
                      </Col>
                      <Col>
                        <Button
                          type="text"
                          size="small"
                          icon={<PlusOutlined />}
                          onClick={() => handleAddVisit(teamPlan.user_id, day)}
                          style={{ fontSize: 10 }}
                        />
                      </Col>
                    </Row>
                  }
                  bodyStyle={{ padding: 8 }}
                >
                  <Space direction="vertical" size={4} style={{ width: '100%' }}>
                    {dayVisits.length === 0 ? (
                      <Text type="secondary" style={{ fontSize: 11 }}>
                        No visits
                      </Text>
                    ) : (
                      dayVisits.map((visit) => (
                        <Card 
                          key={visit.id}
                          size="small"
                          style={{ fontSize: 10 }}
                          bodyStyle={{ padding: 6 }}
                        >
                          <Space direction="vertical" size={2} style={{ width: '100%' }}>
                            <Row justify="space-between" align="top">
                              <Col flex={1}>
                                <Text strong style={{ fontSize: 10 }}>
                                  {visit.customer_name}
                                </Text>
                              </Col>
                              <Col>
                                <Popconfirm
                                  title="Delete this visit?"
                                  onConfirm={() => handleDeleteVisit(visit.id)}
                                  okText="Yes"
                                  cancelText="No"
                                >
                                  <Button
                                    type="text"
                                    size="small"
                                    icon={<DeleteOutlined />}
                                    style={{ fontSize: 8, padding: 0, minWidth: 16, height: 16 }}
                                    danger
                                  />
                                </Popconfirm>
                              </Col>
                            </Row>
                            <Tag 
                              color={getStatusColor(visit.status)} 
                              style={{ fontSize: 9, margin: 0 }}
                            >
                              {getStatusText(visit.status)}
                            </Tag>
                            {visit.planned_time && (
                              <Text style={{ fontSize: 9 }} type="secondary">
                                🕐 {visit.planned_time}
                              </Text>
                            )}
                            {visit.purpose && (
                              <Text style={{ fontSize: 9 }} type="secondary">
                                📋 {visit.purpose}
                              </Text>
                            )}
                          </Space>
                        </Card>
                      ))
                    )}
                  </Space>
                </Card>
              </Col>
            );
          })}
        </Row>
      </Card>
    );
  };

  if (loading) {
    return (
      <div style={{ padding: 24 }}>
        <Title level={2}>Weekly Plan Manager</Title>
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          {[1, 2, 3, 4].map(i => (
            <Col xs={24} sm={12} md={6} key={i}>
              <Card>
                <Skeleton active />
              </Card>
            </Col>
          ))}
        </Row>
        <Card>
          <Skeleton active paragraph={{ rows: 6 }} />
        </Card>
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      {/* Header */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Title level={2} style={{ margin: 0 }}>
            Weekly Plan Manager
          </Title>
          <Text type="secondary">
            Plan and manage your team's weekly visits
          </Text>
        </Col>
        <Col>
          <Space>
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchData}
              loading={loading}
            >
              Refresh
            </Button>
          </Space>
        </Col>
      </Row>

      {/* Week Navigation */}
      <Card style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Button
              icon={<LeftOutlined />}
              onClick={() => navigateWeek(-1)}
              type="text"
            >
              Previous Week
            </Button>
          </Col>
          <Col>
            <Space direction="vertical" size={0} style={{ textAlign: 'center' }}>
              <Title level={4} style={{ margin: 0 }}>
                {format(selectedWeek, 'MMM d')} - {format(addDays(selectedWeek, 6), 'MMM d, yyyy')}
              </Title>
              <Text type="secondary">Week {format(selectedWeek, 'w')}</Text>
            </Space>
          </Col>
          <Col>
            <Button
              icon={<RightOutlined />}
              onClick={() => navigateWeek(1)}
              type="text"
            >
              Next Week
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Weekly Statistics */}
      {renderWeeklyStats()}

      {/* Team Plans */}
      {weeklyPlans && weeklyPlans.team_plans.length > 0 ? (
        <div>
          <Title level={3} style={{ marginBottom: 16 }}>
            Team Plans ({weeklyPlans.team_plans.length} members)
          </Title>
          {weeklyPlans.team_plans.map(renderTeamMemberPlan)}
        </div>
      ) : (
        <Card style={{ textAlign: 'center', padding: 48 }}>
          <Empty
            description={
              <div>
                <Title level={4}>No Team Members</Title>
                <Text type="secondary">
                  No team members are currently assigned to this supervisor.
                </Text>
              </div>
            }
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </Card>
      )}

      {/* Add Visit Modal */}
      <Modal
        title={
          <Space>
            <CalendarOutlined />
            Add Visit for {selectedDay}
            {selectedSalesrep && (
              <Text type="secondary">
                - {team.find(m => m.id === selectedSalesrep)?.full_name}
              </Text>
            )}
          </Space>
        }
        open={showAddModal}
        onCancel={() => {
          setShowAddModal(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmitVisit}
          requiredMark={false}
        >
          <Form.Item
            name="customer_id"
            label="Customer"
            rules={[{ required: true, message: 'Please select a customer' }]}
          >
            <Select
              placeholder="Select a customer"
              showSearch
              optionFilterProp="children"
              size="large"
            >
              {customers.map((customer) => (
                <Option key={customer.id} value={customer.id}>
                  <Space>
                    <BankOutlined />
                    <div>
                      <div>{customer.name}</div>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {customer.city} • {customer.address}
                      </Text>
                    </div>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="planned_time"
                label="Visit Time"
                rules={[{ required: true, message: 'Please select time' }]}
              >
                <TimePicker
                  format="HH:mm"
                  placeholder="Select time"
                  size="large"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="purpose"
                label="Visit Purpose"
                rules={[{ required: true, message: 'Please enter purpose' }]}
              >
                <Select placeholder="Select purpose" size="large">
                  <Option value="Regular visit">Regular visit</Option>
                  <Option value="Product demo">Product demo</Option>
                  <Option value="Follow-up">Follow-up</Option>
                  <Option value="New customer">New customer</Option>
                  <Option value="Problem resolution">Problem resolution</Option>
                  <Option value="Order collection">Order collection</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="Additional Notes"
          >
            <TextArea
              rows={3}
              placeholder="Enter any additional notes or instructions..."
              size="large"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button 
                onClick={() => {
                  setShowAddModal(false);
                  form.resetFields();
                }}
              >
                Cancel
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                icon={<PlusOutlined />}
              >
                Add Visit
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
