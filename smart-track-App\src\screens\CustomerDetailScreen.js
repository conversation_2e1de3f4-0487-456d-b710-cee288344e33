import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

import { customerService } from '../services/customerService';
import { visitService } from '../services/visitService';
import { theme } from '../theme';

const CustomerDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { customer: initialCustomer } = route.params;

  const [customer, setCustomer] = useState(initialCustomer);
  const [loading, setLoading] = useState(false);
  const [orderHistory, setOrderHistory] = useState(null);
  const [recentVisits, setRecentVisits] = useState([]);
  const [salesActivities, setSalesActivities] = useState([]);
  const [activeTab, setActiveTab] = useState('info');

  useEffect(() => {
    loadCustomerDetails();
  }, []);

  const loadCustomerDetails = async () => {
    try {
      setLoading(true);
      
      // Load customer details, order history, and visits in parallel
      const [
        customerData,
        orderData,
        visitsData
      ] = await Promise.all([
        customerService.getCustomer(customer.id),
        customerService.getCustomerOrders(customer.id),
        visitService.getCustomerVisits(customer.id)
      ]);

      setCustomer(customerData);
      setOrderHistory(orderData);
      setRecentVisits(visitsData);
      // Sales activities removed
      setSalesActivities([]);
    } catch (error) {
      console.error('Error loading customer details:', error);
      Alert.alert('Error', 'Failed to load customer details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCall = () => {
    if (customer.phone) {
      Linking.openURL(`tel:${customer.phone}`);
    } else {
      Alert.alert('No Phone', 'No phone number available for this customer.');
    }
  };

  const handleCreateVisit = () => {
    navigation.navigate('CreateVisit', { 
      preselectedCustomer: customer 
    });
  };

  const handleCreateOrder = () => {
    navigation.navigate('CreateOrder', { 
      customer: customer 
    });
  };

  const handleViewOrders = () => {
    navigation.navigate('CustomerOrders', { 
      customer: customer,
      orderHistory: orderHistory 
    });
  };

  const handleVisitPress = (visit) => {
    navigation.navigate('VisitDetail', { visitId: visit.id });
  };

  const getCreditUtilization = () => {
    if (!customer.credit_limit) return 0;
    return ((customer.credit_used || 0) / customer.credit_limit) * 100;
  };

  const getCreditStatusColor = () => {
    const utilization = getCreditUtilization();
    if (utilization >= 90) return theme.colors.error;
    if (utilization >= 70) return theme.colors.warning;
    return theme.colors.success;
  };

  const renderInfoTab = () => (
    <View style={styles.tabContent}>
      {/* Contact Information */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Contact Information</Text>
        <View style={styles.infoRow}>
          <MaterialIcons name="person" size={20} color={theme.colors.textSecondary} />
          <Text style={styles.infoText}>{customer.name}</Text>
        </View>
        <View style={styles.infoRow}>
          <MaterialIcons name="business" size={20} color={theme.colors.textSecondary} />
          <Text style={styles.infoText}>{customer.code}</Text>
        </View>
        <View style={styles.infoRow}>
          <MaterialIcons name="location-on" size={20} color={theme.colors.textSecondary} />
          <Text style={styles.infoText}>{customer.address}, {customer.city}</Text>
        </View>
        <TouchableOpacity style={styles.infoRow} onPress={handleCall}>
          <MaterialIcons name="phone" size={20} color={theme.colors.primary} />
          <Text style={[styles.infoText, { color: theme.colors.primary }]}>
            {customer.phone || 'No phone number'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Credit Information */}
      {customer.credit_limit && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Credit Information</Text>
          <View style={styles.creditContainer}>
            <View style={styles.creditRow}>
              <Text style={styles.creditLabel}>Credit Limit</Text>
              <Text style={styles.creditValue}>${customer.credit_limit}</Text>
            </View>
            <View style={styles.creditRow}>
              <Text style={styles.creditLabel}>Used</Text>
              <Text style={styles.creditValue}>${customer.credit_used || 0}</Text>
            </View>
            <View style={styles.creditRow}>
              <Text style={styles.creditLabel}>Available</Text>
              <Text style={[styles.creditValue, { color: getCreditStatusColor() }]}>
                ${customer.credit_limit - (customer.credit_used || 0)}
              </Text>
            </View>
            
            {/* Credit Utilization Bar */}
            <View style={styles.creditBarContainer}>
              <Text style={styles.creditUtilization}>
                {getCreditUtilization().toFixed(1)}% utilized
              </Text>
              <View style={styles.creditBar}>
                <View 
                  style={[
                    styles.creditBarFill, 
                    { 
                      width: `${getCreditUtilization()}%`,
                      backgroundColor: getCreditStatusColor() 
                    }
                  ]} 
                />
              </View>
            </View>
          </View>
        </View>
      )}

      {/* Quick Stats */}
      {orderHistory && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Stats</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{orderHistory.total_orders}</Text>
              <Text style={styles.statLabel}>Total Orders</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>${orderHistory.total_amount}</Text>
              <Text style={styles.statLabel}>Total Sales</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>${orderHistory.average_order_value}</Text>
              <Text style={styles.statLabel}>Avg Order</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>
                {orderHistory.last_order_date ? 
                  Math.floor((new Date() - new Date(orderHistory.last_order_date)) / (1000 * 60 * 60 * 24)) : 
                  'N/A'
                }
              </Text>
              <Text style={styles.statLabel}>Days Since Last Order</Text>
            </View>
          </View>
        </View>
      )}
    </View>
  );

  const renderOrdersTab = () => (
    <View style={styles.tabContent}>
      {orderHistory && orderHistory.orders.length > 0 ? (
        <>
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recent Orders</Text>
              <TouchableOpacity onPress={handleViewOrders}>
                <Text style={styles.viewAllText}>View All</Text>
              </TouchableOpacity>
            </View>
            
            {orderHistory.orders.slice(0, 5).map((order) => (
              <View key={order.id} style={styles.orderItem}>
                <View style={styles.orderHeader}>
                  <Text style={styles.orderNumber}>{order.order_number}</Text>
                  <Text style={[styles.orderStatus, { color: getOrderStatusColor(order.status) }]}>
                    {order.status.replace('_', ' ').toUpperCase()}
                  </Text>
                </View>
                <View style={styles.orderDetails}>
                  <Text style={styles.orderDate}>
                    {new Date(order.order_date).toLocaleDateString()}
                  </Text>
                  <Text style={styles.orderAmount}>${order.total_amount}</Text>
                </View>
              </View>
            ))}
          </View>
        </>
      ) : (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="shopping-cart" size={64} color={theme.colors.textSecondary} />
          <Text style={styles.emptyText}>No orders yet</Text>
          <Text style={styles.emptySubtext}>Create the first order for this customer</Text>
        </View>
      )}
    </View>
  );

  const renderVisitsTab = () => (
    <View style={styles.tabContent}>
      {recentVisits.length > 0 ? (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Visits</Text>
          {recentVisits.slice(0, 5).map((visit) => (
            <TouchableOpacity 
              key={visit.id} 
              style={styles.visitItem}
              onPress={() => handleVisitPress(visit)}
            >
              <View style={styles.visitHeader}>
                <Text style={styles.visitDate}>
                  {new Date(visit.planned_date).toLocaleDateString()}
                </Text>
                <Text style={[styles.visitStatus, { color: getVisitStatusColor(visit.status) }]}>
                  {visit.status.replace('_', ' ').toUpperCase()}
                </Text>
              </View>
              <Text style={styles.visitPurpose}>{visit.purpose || 'Regular visit'}</Text>
              {visit.duration && (
                <Text style={styles.visitDuration}>Duration: {visit.duration} minutes</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>
      ) : (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="location-on" size={64} color={theme.colors.textSecondary} />
          <Text style={styles.emptyText}>No visits yet</Text>
          <Text style={styles.emptySubtext}>Schedule the first visit to this customer</Text>
        </View>
      )}
    </View>
  );

  const getOrderStatusColor = (status) => {
    switch (status) {
      case 'approved': return theme.colors.success;
      case 'pending': return theme.colors.warning;
      case 'cancelled': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  const getVisitStatusColor = (status) => {
    switch (status) {
      case 'completed': return theme.colors.success;
      case 'in_progress': return theme.colors.warning;
      case 'planned': return theme.colors.primary;
      default: return theme.colors.textSecondary;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading customer details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialIcons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Customer Details</Text>
        <TouchableOpacity onPress={handleCall}>
          <MaterialIcons name="phone" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity style={styles.actionButton} onPress={handleCreateVisit}>
          <MaterialIcons name="location-on" size={20} color="#FFFFFF" />
          <Text style={styles.actionButtonText}>Visit</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={handleCreateOrder}>
          <MaterialIcons name="shopping-cart" size={20} color="#FFFFFF" />
          <Text style={styles.actionButtonText}>Order</Text>
        </TouchableOpacity>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'info' && styles.activeTab]}
          onPress={() => setActiveTab('info')}
        >
          <Text style={[styles.tabText, activeTab === 'info' && styles.activeTabText]}>
            Info
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'orders' && styles.activeTab]}
          onPress={() => setActiveTab('orders')}
        >
          <Text style={[styles.tabText, activeTab === 'orders' && styles.activeTabText]}>
            Orders
          </Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'visits' && styles.activeTab]}
          onPress={() => setActiveTab('visits')}
        >
          <Text style={[styles.tabText, activeTab === 'visits' && styles.activeTabText]}>
            Visits
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {activeTab === 'info' && renderInfoTab()}
        {activeTab === 'orders' && renderOrdersTab()}
        {activeTab === 'visits' && renderVisitsTab()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  actionContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    marginHorizontal: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
  },
  tabText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  activeTabText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  tabContent: {
    paddingHorizontal: 16,
  },
  section: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  viewAllText: {
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: 12,
  },
  creditContainer: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
  },
  creditRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  creditLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  creditValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  creditBarContainer: {
    marginTop: 12,
  },
  creditUtilization: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  creditBar: {
    height: 8,
    backgroundColor: theme.colors.border,
    borderRadius: 4,
  },
  creditBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 0.48,
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
    textAlign: 'center',
  },
  orderItem: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  orderStatus: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  orderDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  orderDate: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  visitItem: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  visitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  visitDate: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  visitStatus: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  visitPurpose: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  visitDuration: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textSecondary,
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default CustomerDetailScreen;
