'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '../../stores/auth';
import { LogIn, Loader2, Shield } from 'lucide-react';

export default function LoginPage() {
  const router = useRouter();
  const { login, isLoading, error, isAuthenticated, isInitializing } = useAuthStore();
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [rememberMe, setRememberMe] = useState(true);

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  // Show loading during authentication initialization
  if (isInitializing) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 font-medium">Initializing...</p>
        </div>
      </div>
    );
  }

  // Show loading if user is already authenticated
  if (isAuthenticated) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 font-medium">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await login(formData);
      router.push('/dashboard');
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  // Handle Enter key press for quick login
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isLoading) {
      handleSubmit(e as any);
    }
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #eff6ff 0%, #ffffff 50%, #eef2ff 100%)',
      padding: '48px 16px'
    }}>
      <div style={{ maxWidth: '400px', width: '100%', display: 'flex', flexDirection: 'column', gap: '32px' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            margin: '0 auto 16px',
            height: '64px',
            width: '64px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'linear-gradient(to right, #2563eb, #4f46e5)',
            borderRadius: '50%',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}>
            <Shield style={{ height: '32px', width: '32px', color: 'white' }} />
          </div>
          <h1 style={{
            fontSize: '30px',
            fontWeight: '800',
            background: 'linear-gradient(to right, #2563eb, #4f46e5)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            marginBottom: '8px'
          }}>
            Smart Track
          </h1>
          <p style={{ fontSize: '14px', color: '#6b7280' }}>
            Sales Force Automation Dashboard
          </p>
        </div>
        
        <div style={{
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(10px)',
          borderRadius: '12px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          padding: '32px',
        }}>
          <div style={{ marginBottom: '24px', textAlign: 'center' }}>
            <h2 style={{ fontSize: '24px', fontWeight: '700', color: '#111827', marginBottom: '8px' }}>Welcome Back</h2>
            <p style={{ color: '#6b7280', fontSize: '14px' }}>
              Enter your credentials to access the dashboard
            </p>
          </div>
          
          <div>
            <form style={{ display: 'flex', flexDirection: 'column', gap: '24px' }} onSubmit={handleSubmit} onKeyDown={handleKeyPress}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                <div>
                  <label htmlFor="username" style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '8px' }}>
                    Username or Email
                  </label>
                  <input
                    id="username"
                    name="username"
                    type="text"
                    required
                    value={formData.username}
                    onChange={handleInputChange}
                    style={{
                      width: '100%',
                      height: '44px',
                      padding: '12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      transition: 'border-color 0.15s ease-in-out',
                    }}
                    placeholder="Enter your username or email"
                    onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                    onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                  />
                </div>
                
                <div>
                  <label htmlFor="password" style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '8px' }}>
                    Password
                  </label>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    required
                    value={formData.password}
                    onChange={handleInputChange}
                    style={{
                      width: '100%',
                      height: '44px',
                      padding: '12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      transition: 'border-color 0.15s ease-in-out',
                    }}
                    placeholder="Enter your password"
                    onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                    onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                  />
                </div>
              </div>

              {error && (
                <div className="text-red-600 text-sm text-center bg-red-50 p-2 rounded">
                  {error}
                </div>
              )}

              <button
                type="submit"
                disabled={isLoading}
                style={{
                  width: '100%',
                  height: '48px',
                  background: 'linear-gradient(to right, #2563eb, #1d4ed8)',
                  color: 'white',
                  fontWeight: '600',
                  borderRadius: '8px',
                  border: 'none',
                  cursor: isLoading ? 'not-allowed' : 'pointer',
                  opacity: isLoading ? 0.5 : 1,
                  transition: 'all 0.2s',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                }}
                onMouseEnter={(e) => {
                  if (!isLoading) {
                    e.currentTarget.style.background = 'linear-gradient(to right, #1d4ed8, #1e40af)';
                    e.currentTarget.style.transform = 'scale(1.02)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isLoading) {
                    e.currentTarget.style.background = 'linear-gradient(to right, #2563eb, #1d4ed8)';
                    e.currentTarget.style.transform = 'scale(1)';
                  }
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px', color: 'white' }}>
                  {isLoading ? (
                    <>
                      <Loader2 style={{ width: '20px', height: '20px', color: 'white' }} className="animate-spin" />
                      <span style={{ color: 'white' }}>Signing in...</span>
                    </>
                  ) : (
                    <>
                      <LogIn style={{ width: '20px', height: '20px', color: 'white' }} />
                      <span style={{ color: 'white' }}>Sign in to Dashboard</span>
                    </>
                  )}
                </div>
              </button>
              
              <div className="text-center">
                <p className="text-xs text-gray-500 mt-2">
                  Press <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">Enter</kbd> to sign in quickly
                </p>
              </div>
            </form>
            
            <div className="mt-8 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
              <h3 className="text-sm font-semibold text-blue-900 mb-3 flex items-center">
                <Shield className="h-4 w-4 mr-2" />
                Demo Credentials:
              </h3>
              <div className="text-xs text-blue-700 space-y-2">
                <div className="flex justify-between items-center p-2 bg-white/50 rounded">
                  <strong>Admin:</strong> 
                  <span className="font-mono bg-blue-100 px-2 py-1 rounded text-xs">admin / password123</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white/50 rounded">
                  <strong>Supervisor:</strong> 
                  <span className="font-mono bg-blue-100 px-2 py-1 rounded text-xs">supervisor1 / password123</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white/50 rounded">
                  <strong>Manager:</strong> 
                  <span className="font-mono bg-blue-100 px-2 py-1 rounded text-xs">manager1 / password123</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white/50 rounded">
                  <strong>Sales Rep:</strong> 
                  <span className="font-mono bg-blue-100 px-2 py-1 rounded text-xs">salesrep1 / password123</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}