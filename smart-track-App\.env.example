# Smart Track Mobile App Environment Configuration - Example
# Copy this file to .env and update the values for your environment

# Backend API Configuration
EXPO_PUBLIC_API_BASE_URL=http://YOUR_COMPUTER_IP:8000/api/v1
EXPO_PUBLIC_API_TIMEOUT=20000

# Alternative URLs for different environments
# EXPO_PUBLIC_API_LOCALHOST=http://localhost:8000/api/v1
# EXPO_PUBLIC_API_ANDROID_EMULATOR=http://********:8000/api/v1
# EXPO_PUBLIC_API_IOS_LOCALHOST=http://127.0.0.1:8000/api/v1

# Environment Configuration
EXPO_PUBLIC_ENVIRONMENT=development
EXPO_PUBLIC_DEBUG_MODE=true

# Authentication Configuration
EXPO_PUBLIC_TOKEN_REFRESH_ENABLED=true
EXPO_PUBLIC_AUTO_LOGIN_ENABLED=false

# Instructions:
# 1. Copy this file to .env
# 2. Replace YOUR_COMPUTER_IP with your actual IP address
# 3. To find your IP address:
#    - Windows: Run `ipconfig` in command prompt
#    - Mac/Linux: Run `ifconfig` in terminal
# 4. Update other values as needed for your environment
