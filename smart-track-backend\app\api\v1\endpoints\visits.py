from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date
import math
import uuid

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.visit import Visit, VisitPhoto, VisitNote
from app.models.customer import Customer
from app.schemas.visit import (
    VisitCreate, VisitAdminCreate, VisitUpdate, Visit as VisitSchema, VisitDetail,
    VisitCheckIn, VisitCheckOut, VisitStatistics,
    VisitPhotoCreate, VisitPhoto as VisitPhotoSchema,
    VisitNoteCreate, VisitNoteUpdate, VisitNote as VisitNoteSchema,
    LocationValidation, LocationValidationResponse,
    NearbyCustomersResponse, NearbyCustomer,
    VisitStatus
)

router = APIRouter()

# Helper function to calculate distance between two points
def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """Calculate distance between two GPS coordinates in meters"""
    R = 6371000  # Earth's radius in meters
    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lon = math.radians(lon2 - lon1)
    
    a = (math.sin(delta_lat / 2) * math.sin(delta_lat / 2) +
         math.cos(lat1_rad) * math.cos(lat2_rad) *
         math.sin(delta_lon / 2) * math.sin(delta_lon / 2))
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    
    return R * c

@router.get("/", response_model=List[VisitSchema])
async def list_visits(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,  # Changed to str to handle comma-separated values
    date_from: Optional[date] = None,
    date_to: Optional[date] = None,
    start_date: Optional[date] = None,  # Also accept mobile app parameter names
    end_date: Optional[date] = None,
    customer_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """List user visits with optional filters"""
    query = db.query(Visit).join(Customer, Visit.customer_id == Customer.id).filter(Visit.user_id == current_user.id)
    
    if status:
        # Handle comma-separated status values
        if ',' in status:
            status_list = [s.strip() for s in status.split(',')]
            # Map mobile app status values to actual enum values
            mapped_statuses = []
            for s in status_list:
                if s == 'scheduled':
                    mapped_statuses.append(VisitStatus.PLANNED)
                elif s == 'pending':
                    mapped_statuses.append(VisitStatus.PLANNED)  # Both map to PLANNED
                elif s == 'planned':
                    mapped_statuses.append(VisitStatus.PLANNED)
                elif s == 'in_progress':
                    mapped_statuses.append(VisitStatus.IN_PROGRESS)
                elif s == 'completed':
                    mapped_statuses.append(VisitStatus.COMPLETED)
                elif s == 'cancelled':
                    mapped_statuses.append(VisitStatus.CANCELLED)
            if mapped_statuses:
                query = query.filter(Visit.status.in_(mapped_statuses))
        else:
            # Single status value
            if status == 'scheduled':
                query = query.filter(Visit.status == VisitStatus.PLANNED)
            elif status == 'pending':
                query = query.filter(Visit.status == VisitStatus.PLANNED)
            elif status == 'planned':
                query = query.filter(Visit.status == VisitStatus.PLANNED)
            elif status == 'in_progress':
                query = query.filter(Visit.status == VisitStatus.IN_PROGRESS)
            elif status == 'completed':
                query = query.filter(Visit.status == VisitStatus.COMPLETED)
            elif status == 'cancelled':
                query = query.filter(Visit.status == VisitStatus.CANCELLED)
    
    # Handle date filtering - use start_date/end_date if provided, otherwise use date_from/date_to
    if start_date or date_from:
        filter_date = start_date if start_date else date_from
        query = query.filter(Visit.planned_date >= filter_date)
    
    if end_date or date_to:
        filter_date = end_date if end_date else date_to
        query = query.filter(Visit.planned_date <= filter_date)
    
    if customer_id:
        query = query.filter(Visit.customer_id == customer_id)
    
    query = query.order_by(Visit.planned_date.desc())
    visits = query.offset(skip).limit(limit).all()
    
    # Convert visits to dictionaries and add customer data
    result = []
    for visit in visits:
        visit_dict = {
            'id': visit.id,
            'user_id': visit.user_id,
            'customer_id': visit.customer_id,
            'planned_date': visit.planned_date,
            'purpose': visit.purpose,
            'notes': visit.notes,
            'status': visit.status,
            'actual_start_time': visit.actual_start_time,
            'actual_end_time': visit.actual_end_time,
            'checkin_latitude': visit.checkin_latitude,
            'checkin_longitude': visit.checkin_longitude,
            'checkout_latitude': visit.checkout_latitude,
            'checkout_longitude': visit.checkout_longitude,
            'outcome': visit.outcome,
            'next_action': visit.next_action,
            'created_at': visit.created_at,
            'updated_at': visit.updated_at
        }
        
        # Add customer data
        if visit.customer_id:
            customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
            if customer:
                visit_dict['customer'] = {
                    'id': customer.id,
                    'name': customer.name,
                    'address': customer.address,
                    'city': customer.city,
                    'province': customer.province,
                    'phone': customer.phone,
                    'email': customer.email,
                    'latitude': customer.latitude,
                    'longitude': customer.longitude
                }
        
        result.append(visit_dict)
    
    return result

@router.get("/pending", response_model=List[VisitSchema])
async def get_pending_visits(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get visits that were planned 2 weeks ago and still have 'planned' status"""
    from datetime import datetime, timedelta
    
    # Calculate date 2 weeks ago
    two_weeks_ago = datetime.now() - timedelta(weeks=2)
    
    # Get visits planned 2 weeks ago that are still in planned status
    query = db.query(Visit).join(Customer, Visit.customer_id == Customer.id).filter(
        Visit.user_id == current_user.id,
        Visit.status == VisitStatus.PLANNED,
        Visit.planned_date <= two_weeks_ago
    ).order_by(Visit.planned_date.asc())
    
    visits = query.offset(skip).limit(limit).all()
    
    # Convert visits to dictionaries and add customer data
    result = []
    for visit in visits:
        # Calculate days overdue properly
        planned_date = visit.planned_date
        if planned_date.tzinfo is not None:
            # If timezone-aware, convert to naive datetime
            planned_date = planned_date.replace(tzinfo=None)
        
        days_overdue = max(0, (datetime.now() - planned_date).days)
        
        visit_dict = {
            'id': visit.id,
            'user_id': visit.user_id,
            'customer_id': visit.customer_id,
            'planned_date': visit.planned_date,
            'purpose': visit.purpose,
            'notes': visit.notes,
            'status': visit.status,
            'actual_start_time': visit.actual_start_time,
            'actual_end_time': visit.actual_end_time,
            'checkin_latitude': visit.checkin_latitude,
            'checkin_longitude': visit.checkin_longitude,
            'checkout_latitude': visit.checkout_latitude,
            'checkout_longitude': visit.checkout_longitude,
            'outcome': visit.outcome,
            'next_action': visit.next_action,
            'created_at': visit.created_at,
            'updated_at': visit.updated_at,
            'days_overdue': days_overdue
        }
        
        # Add customer data
        if visit.customer_id:
            customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
            if customer:
                visit_dict['customer'] = {
                    'id': customer.id,
                    'name': customer.name,
                    'address': customer.address,
                    'city': customer.city,
                    'province': customer.province,
                    'phone': customer.phone,
                    'email': customer.email,
                    'latitude': customer.latitude,
                    'longitude': customer.longitude
                }
        
        result.append(visit_dict)
    
    return result

@router.get("/admin/all", response_model=List[VisitSchema])
async def admin_list_all_visits(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    search: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Admin endpoint to list all visits - requires authentication"""
    query = db.query(Visit).join(Customer, Visit.customer_id == Customer.id)
    
    if status:
        # Handle single status value for admin
        if status == 'planned':
            query = query.filter(Visit.status == VisitStatus.PLANNED)
        elif status == 'in_progress':
            query = query.filter(Visit.status == VisitStatus.IN_PROGRESS)
        elif status == 'completed':
            query = query.filter(Visit.status == VisitStatus.COMPLETED)
        elif status == 'cancelled':
            query = query.filter(Visit.status == VisitStatus.CANCELLED)
    
    if search:
        query = query.filter(
            (Customer.name.ilike(f"%{search}%")) |
            (Visit.purpose.ilike(f"%{search}%")) |
            (Visit.notes.ilike(f"%{search}%"))
        )
    
    query = query.order_by(Visit.planned_date.desc())
    visits = query.offset(skip).limit(limit).all()
    
    # Convert visits to dictionaries and add customer data
    result = []
    for visit in visits:
        visit_dict = {
            'id': visit.id,
            'user_id': visit.user_id,
            'customer_id': visit.customer_id,
            'planned_date': visit.planned_date,
            'purpose': visit.purpose,
            'notes': visit.notes,
            'status': visit.status,
            'actual_start_time': visit.actual_start_time,
            'actual_end_time': visit.actual_end_time,
            'checkin_latitude': visit.checkin_latitude,
            'checkin_longitude': visit.checkin_longitude,
            'checkout_latitude': visit.checkout_latitude,
            'checkout_longitude': visit.checkout_longitude,
            'outcome': visit.outcome,
            'next_action': visit.next_action,
            'created_at': visit.created_at,
            'updated_at': visit.updated_at
        }
        
        # Add customer data
        if visit.customer_id:
            customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
            if customer:
                visit_dict['customer'] = {
                    'id': customer.id,
                    'name': customer.name,
                    'address': customer.address,
                    'city': customer.city,
                    'province': customer.province,
                    'phone': customer.phone,
                    'email': customer.email,
                    'latitude': customer.latitude,
                    'longitude': customer.longitude
                }
        
        result.append(visit_dict)
    
    return result

@router.post("/", response_model=VisitSchema)
async def create_visit(
    visit: VisitCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new visit plan"""
    # Verify customer exists
    customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    db_visit = Visit(
        user_id=current_user.id,
        **visit.dict()
    )
    db.add(db_visit)
    db.commit()
    db.refresh(db_visit)
    
    # Return visit data with customer information
    return {
        'id': db_visit.id,
        'user_id': db_visit.user_id,
        'customer_id': db_visit.customer_id,
        'planned_date': db_visit.planned_date,
        'purpose': db_visit.purpose,
        'notes': db_visit.notes,
        'status': db_visit.status,
        'actual_start_time': db_visit.actual_start_time,
        'actual_end_time': db_visit.actual_end_time,
        'checkin_latitude': db_visit.checkin_latitude,
        'checkin_longitude': db_visit.checkin_longitude,
        'checkout_latitude': db_visit.checkout_latitude,
        'checkout_longitude': db_visit.checkout_longitude,
        'outcome': db_visit.outcome,
        'next_action': db_visit.next_action,
        'created_at': db_visit.created_at,
        'updated_at': db_visit.updated_at,
        'customer': {
            'id': customer.id,
            'name': customer.name,
            'address': customer.address,
            'city': customer.city,
            'province': customer.province,
            'phone': customer.phone,
            'email': customer.email,
            'latitude': customer.latitude,
            'longitude': customer.longitude
        }
    }

@router.get("/{visit_id}", response_model=VisitDetail)
async def get_visit(
    visit_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get visit details including photos and notes"""
    visit = db.query(Visit).filter(
        Visit.id == visit_id,
        Visit.user_id == current_user.id
    ).first()
    
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found"
        )
    
    # Load customer information
    customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
    visit_dict = visit.__dict__.copy()
    visit_dict['customer'] = {
        'id': customer.id,
        'name': customer.name,
        'address': customer.address,
        'city': customer.city,
        'province': customer.province,
        'phone': customer.phone,
        'email': customer.email,
        'latitude': customer.latitude,
        'longitude': customer.longitude
    } if customer else None
    
    return visit_dict

@router.put("/{visit_id}", response_model=VisitSchema)
async def update_visit(
    visit_id: int,
    visit_update: VisitUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update visit information"""
    visit = db.query(Visit).filter(
        Visit.id == visit_id,
        Visit.user_id == current_user.id
    ).first()
    
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found"
        )
    
    update_data = visit_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(visit, field, value)
    
    db.commit()
    db.refresh(visit)
    
    # Populate customer data
    customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
    if customer:
        visit.customer = {
            'id': customer.id,
            'name': customer.name,
            'address': customer.address,
            'city': customer.city,
            'province': customer.province,
            'phone': customer.phone,
            'email': customer.email,
            'latitude': customer.latitude,
            'longitude': customer.longitude,
        }
    
    return visit

@router.post("/{visit_id}/checkin", response_model=VisitSchema)
async def checkin_visit(
    visit_id: int,
    checkin_data: VisitCheckIn,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """GPS check-in for a visit"""
    visit = db.query(Visit).filter(
        Visit.id == visit_id,
        Visit.user_id == current_user.id
    ).first()
    
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found"
        )
    
    if visit.status != VisitStatus.PLANNED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Visit must be in planned status to check in"
        )
    
    # Validate location (optional - you can add customer location validation here)
    customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
    if customer and customer.latitude and customer.longitude:
        distance = calculate_distance(
            checkin_data.latitude, checkin_data.longitude,
            customer.latitude, customer.longitude
        )
        # Allow check-in within 50000 meters (50km) for testing - increased from 500m
        # TODO: Change back to 500 for production
        if distance > 50000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"You are {distance:.0f} meters away from customer location. Please get closer to check in."
            )
    
    visit.checkin_latitude = checkin_data.latitude
    visit.checkin_longitude = checkin_data.longitude
    visit.actual_start_time = datetime.utcnow()
    visit.status = VisitStatus.IN_PROGRESS
    
    db.commit()
    db.refresh(visit)
    
    # Create response dict manually to avoid SQLAlchemy object serialization issues
    response_data = {
        'id': visit.id,
        'user_id': visit.user_id,
        'customer_id': visit.customer_id,
        'planned_date': visit.planned_date.isoformat() if visit.planned_date else None,
        'actual_start_time': visit.actual_start_time.isoformat() if visit.actual_start_time else None,
        'actual_end_time': visit.actual_end_time.isoformat() if visit.actual_end_time else None,
        'status': visit.status,
        'checkin_latitude': visit.checkin_latitude,
        'checkin_longitude': visit.checkin_longitude,
        'checkout_latitude': visit.checkout_latitude,
        'checkout_longitude': visit.checkout_longitude,
        'purpose': visit.purpose,
        'notes': visit.notes,
        'outcome': visit.outcome,
        'next_action': visit.next_action,
        'created_at': visit.created_at.isoformat() if visit.created_at else None,
        'updated_at': visit.updated_at.isoformat() if visit.updated_at else None,
        'customer': {
            'id': customer.id,
            'name': customer.name,
            'address': customer.address,
            'city': customer.city,
            'province': customer.province,
            'phone': customer.phone,
            'email': customer.email,
            'latitude': customer.latitude,
            'longitude': customer.longitude
        } if customer else None
    }
    
    return response_data

@router.post("/{visit_id}/checkout", response_model=VisitSchema)
async def checkout_visit(
    visit_id: int,
    checkout_data: VisitCheckOut,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """GPS check-out for a visit"""
    visit = db.query(Visit).filter(
        Visit.id == visit_id,
        Visit.user_id == current_user.id
    ).first()
    
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found"
        )
    
    if visit.status != 'in_progress':
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Visit must be in progress to check out. Current status: {visit.status}"
        )
    
    visit.checkout_latitude = checkout_data.latitude
    visit.checkout_longitude = checkout_data.longitude
    visit.actual_end_time = datetime.utcnow()
    visit.status = 'completed'
    
    if checkout_data.outcome:
        visit.outcome = checkout_data.outcome
    if checkout_data.next_action:
        visit.next_action = checkout_data.next_action
    
    db.commit()
    db.refresh(visit)
    
    # Get customer data for response
    customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
    
    # Return visit data with populated customer
    response_data = {
        'id': visit.id,
        'user_id': visit.user_id,
        'customer_id': visit.customer_id,
        'planned_date': visit.planned_date,
        'actual_start_time': visit.actual_start_time,
        'actual_end_time': visit.actual_end_time,
        'status': visit.status,
        'checkin_latitude': visit.checkin_latitude,
        'checkin_longitude': visit.checkin_longitude,
        'checkout_latitude': visit.checkout_latitude,
        'checkout_longitude': visit.checkout_longitude,
        'purpose': visit.purpose,
        'notes': visit.notes,
        'outcome': visit.outcome,
        'next_action': visit.next_action,
        'created_at': visit.created_at,
        'updated_at': visit.updated_at,
        'customer': {
            'id': customer.id,
            'name': customer.name,
            'address': customer.address,
            'city': customer.city,
            'province': customer.province,
            'phone': customer.phone,
            'email': customer.email,
            'latitude': customer.latitude,
            'longitude': customer.longitude
        } if customer else None
    }
    
    return response_data

@router.post("/{visit_id}/photos", response_model=VisitPhotoSchema)
async def upload_visit_photo(
    visit_id: int,
    file: UploadFile = File(...),
    photo_type: str = Form("general"),
    caption: Optional[str] = Form(None),
    latitude: Optional[float] = Form(None),
    longitude: Optional[float] = Form(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Upload a photo for a visit"""
    visit = db.query(Visit).filter(
        Visit.id == visit_id,
        Visit.user_id == current_user.id
    ).first()
    
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found"
        )
    
    # Validate file type
    allowed_types = ["image/jpeg", "image/png", "image/jpg"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only JPEG and PNG images are allowed"
        )
    
    # For now, we'll store a placeholder URL
    # In production, you would save the file and store the actual URL
    photo_url = f"/uploads/visit_photos/{visit_id}_{uuid.uuid4().hex}.jpg"
    
    # Create database record
    db_photo = VisitPhoto(
        visit_id=visit_id,
        photo_url=photo_url,
        photo_type=photo_type,
        caption=caption,
        latitude=latitude,
        longitude=longitude
    )
    
    db.add(db_photo)
    db.commit()
    db.refresh(db_photo)
    
    return db_photo

@router.get("/{visit_id}/photos", response_model=List[VisitPhotoSchema])
async def get_visit_photos(
    visit_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all photos for a visit"""
    visit = db.query(Visit).filter(
        Visit.id == visit_id,
        Visit.user_id == current_user.id
    ).first()
    
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found"
        )
    
    photos = db.query(VisitPhoto).filter(VisitPhoto.visit_id == visit_id).all()
    return photos

@router.get("/statistics", response_model=VisitStatistics)
async def get_visit_statistics(
    date_from: Optional[date] = None,
    date_to: Optional[date] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get visit performance statistics"""
    query = db.query(Visit).filter(Visit.user_id == current_user.id)
    
    if date_from:
        query = query.filter(Visit.planned_date >= date_from)
    if date_to:
        query = query.filter(Visit.planned_date <= date_to)
    
    visits = query.all()
    
    total_visits = len(visits)
    completed_visits = len([v for v in visits if v.status == VisitStatus.COMPLETED])
    planned_visits = len([v for v in visits if v.status == VisitStatus.PLANNED])
    in_progress_visits = len([v for v in visits if v.status == VisitStatus.IN_PROGRESS])
    
    completion_rate = (completed_visits / total_visits * 100) if total_visits > 0 else 0
    
    # Calculate average visit duration for completed visits
    completed_with_times = [
        v for v in visits 
        if v.status == VisitStatus.COMPLETED and v.actual_start_time and v.actual_end_time
    ]
    
    avg_visit_duration = None
    if completed_with_times:
        total_duration = sum([
            (v.actual_end_time - v.actual_start_time).total_seconds() / 60
            for v in completed_with_times
        ])
        avg_visit_duration = total_duration / len(completed_with_times)
    
    return VisitStatistics(
        total_visits=total_visits,
        completed_visits=completed_visits,
        planned_visits=planned_visits,
        in_progress_visits=in_progress_visits,
        completion_rate=completion_rate,
        avg_visit_duration=avg_visit_duration
    )

# Visit Notes endpoints
@router.post("/{visit_id}/notes", response_model=VisitNoteSchema)
async def create_visit_note(
    visit_id: int,
    note: VisitNoteCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a note for a visit"""
    visit = db.query(Visit).filter(
        Visit.id == visit_id,
        Visit.user_id == current_user.id
    ).first()
    
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found"
        )
    
    db_note = VisitNote(visit_id=visit_id, **note.dict())
    db.add(db_note)
    db.commit()
    db.refresh(db_note)
    
    return db_note

@router.get("/{visit_id}/notes", response_model=List[VisitNoteSchema])
async def get_visit_notes(
    visit_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all notes for a visit"""
    visit = db.query(Visit).filter(
        Visit.id == visit_id,
        Visit.user_id == current_user.id
    ).first()
    
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found"
        )
    
    notes = db.query(VisitNote).filter(VisitNote.visit_id == visit_id).all()
    return notes

# Admin endpoints for dashboard management (without authentication)

@router.post("/admin/create", response_model=VisitSchema)
async def admin_create_visit(
    visit: VisitAdminCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Admin endpoint to create a new visit - requires authentication"""
    # Verify customer exists
    customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Verify user exists
    user = db.query(User).filter(User.id == visit.user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    db_visit = Visit(**visit.dict())
    db.add(db_visit)
    db.commit()
    db.refresh(db_visit)
    
    # Return visit with customer data
    return {
        'id': db_visit.id,
        'user_id': db_visit.user_id,
        'customer_id': db_visit.customer_id,
        'planned_date': db_visit.planned_date,
        'purpose': db_visit.purpose,
        'notes': db_visit.notes,
        'status': db_visit.status,
        'actual_start_time': db_visit.actual_start_time,
        'actual_end_time': db_visit.actual_end_time,
        'checkin_latitude': db_visit.checkin_latitude,
        'checkin_longitude': db_visit.checkin_longitude,
        'checkout_latitude': db_visit.checkout_latitude,
        'checkout_longitude': db_visit.checkout_longitude,
        'outcome': db_visit.outcome,
        'next_action': db_visit.next_action,
        'created_at': db_visit.created_at,
        'updated_at': db_visit.updated_at,
        'customer': {
            'id': customer.id,
            'name': customer.name,
            'address': customer.address,
            'city': customer.city,
            'province': customer.province,
            'phone': customer.phone,
            'email': customer.email,
            'latitude': customer.latitude,
            'longitude': customer.longitude
        }
    }

@router.get("/admin/{visit_id}", response_model=VisitSchema)
async def admin_get_visit(
    visit_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Admin endpoint to get visit details - requires authentication"""
    visit = db.query(Visit).filter(Visit.id == visit_id).first()
    
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found"
        )
    
    # Load customer information
    customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
    
    return {
        'id': visit.id,
        'user_id': visit.user_id,
        'customer_id': visit.customer_id,
        'planned_date': visit.planned_date,
        'purpose': visit.purpose,
        'notes': visit.notes,
        'status': visit.status,
        'actual_start_time': visit.actual_start_time,
        'actual_end_time': visit.actual_end_time,
        'checkin_latitude': visit.checkin_latitude,
        'checkin_longitude': visit.checkin_longitude,
        'checkout_latitude': visit.checkout_latitude,
        'checkout_longitude': visit.checkout_longitude,
        'outcome': visit.outcome,
        'next_action': visit.next_action,
        'created_at': visit.created_at,
        'updated_at': visit.updated_at,
        'customer': {
            'id': customer.id,
            'name': customer.name,
            'address': customer.address,
            'city': customer.city,
            'province': customer.province,
            'phone': customer.phone,
            'email': customer.email,
            'latitude': customer.latitude,
            'longitude': customer.longitude
        } if customer else None
    }

@router.put("/admin/{visit_id}", response_model=VisitSchema)
async def admin_update_visit(
    visit_id: int,
    visit_update: VisitUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Admin endpoint to update visit - requires authentication"""
    visit = db.query(Visit).filter(Visit.id == visit_id).first()
    
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found"
        )
    
    update_data = visit_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(visit, field, value)
    
    db.commit()
    db.refresh(visit)
    
    # Load customer information
    customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
    
    return {
        'id': visit.id,
        'user_id': visit.user_id,
        'customer_id': visit.customer_id,
        'planned_date': visit.planned_date,
        'purpose': visit.purpose,
        'notes': visit.notes,
        'status': visit.status,
        'actual_start_time': visit.actual_start_time,
        'actual_end_time': visit.actual_end_time,
        'checkin_latitude': visit.checkin_latitude,
        'checkin_longitude': visit.checkin_longitude,
        'checkout_latitude': visit.checkout_latitude,
        'checkout_longitude': visit.checkout_longitude,
        'outcome': visit.outcome,
        'next_action': visit.next_action,
        'created_at': visit.created_at,
        'updated_at': visit.updated_at,
        'customer': {
            'id': customer.id,
            'name': customer.name,
            'address': customer.address,
            'city': customer.city,
            'province': customer.province,
            'phone': customer.phone,
            'email': customer.email,
            'latitude': customer.latitude,
            'longitude': customer.longitude
        } if customer else None
    }

@router.delete("/admin/{visit_id}")
async def admin_delete_visit(
    visit_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Admin endpoint to delete visit - requires authentication"""
    visit = db.query(Visit).filter(Visit.id == visit_id).first()
    
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found"
        )
    
    db.delete(visit)
    db.commit()
    
    return {"message": "Visit deleted successfully"}
 
