'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { 
  Menu, 
  Layout,
  Badge
} from 'antd';
import { 
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  ShopOutlined,
  FileTextOutlined,
  SettingOutlined,
  Bar<PERSON><PERSON>Outlined,
  CalendarOutlined,
  MessageOutlined,
  BellOutlined
} from '@ant-design/icons';
import { User } from '@/types';

const { Sider } = Layout;

interface NavigationSidebarProps {
  user: User;
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
  darkMode?: boolean;
}

export default function NavigationSidebar({ 
  user, 
  collapsed, 
  onCollapse, 
  darkMode = false 
}: NavigationSidebarProps) {
  const router = useRouter();
  const pathname = usePathname();

  // Get user role for menu filtering
  const userRole = user.role?.name?.toLowerCase() || user.profile?.role_type;

  // Common menu items
  const commonMenuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: 'Dashboard',
    },
    {
      key: '/reports',
      icon: <BarChartOutlined />,
      label: 'Reports',
      children: [
        {
          key: '/reports/sales',
          label: 'Sales Reports',
        },
        {
          key: '/reports/visits',
          label: 'Visit Reports',
        },
        {
          key: '/reports/performance',
          label: 'Performance',
        },
      ],
    },
    {
      key: '/calendar',
      icon: <CalendarOutlined />,
      label: 'Calendar',
    },
  ];

  // Admin-specific menu items
  const adminMenuItems = [
    ...commonMenuItems,
    {
      key: '/users',
      icon: <UserOutlined />,
      label: 'User Management',
      children: [
        {
          key: '/users/list',
          label: 'All Users',
        },
        {
          key: '/users/roles',
          label: 'Roles & Permissions',
        },
      ],
    },
    {
      key: '/customers',
      icon: <ShopOutlined />,
      label: 'Customer Management',
    },
    {
      key: '/system',
      icon: <SettingOutlined />,
      label: 'System Settings',
      children: [
        {
          key: '/system/general',
          label: 'General Settings',
        },
        {
          key: '/system/backup',
          label: 'Backup & Restore',
        },
        {
          key: '/system/logs',
          label: 'System Logs',
        },
      ],
    },
  ];

  // Manager-specific menu items
  const managerMenuItems = [
    ...commonMenuItems,
    {
      key: '/team',
      icon: <TeamOutlined />,
      label: 'Team Management',
      children: [
        {
          key: '/team/supervisors',
          label: 'Supervisors',
        },
        {
          key: '/team/performance',
          label: 'Team Performance',
        },
      ],
    },
    {
      key: '/customers',
      icon: <ShopOutlined />,
      label: 'Customers',
    },
  ];

  // Supervisor-specific menu items
  const supervisorMenuItems = [
    ...commonMenuItems,
    {
      key: '/team',
      icon: <TeamOutlined />,
      label: 'My Team',
    },
    {
      key: '/visits',
      icon: <FileTextOutlined />,
      label: 'Visit Management',
      children: [
        {
          key: '/visits/schedule',
          label: 'Schedule Visits',
        },
        {
          key: '/visits/track',
          label: 'Track Visits',
        },
        {
          key: '/visits/history',
          label: 'Visit History',
        },
      ],
    },
    {
      key: '/customers',
      icon: <ShopOutlined />,
      label: 'Customers',
    },
  ];

  // Get menu items based on user role
  const getMenuItems = () => {
    switch (userRole) {
      case 'admin':
        return adminMenuItems;
      case 'manager':
        return managerMenuItems;
      case 'supervisor':
        return supervisorMenuItems;
      default:
        return commonMenuItems;
    }
  };

  const handleMenuClick = ({ key }: { key: string }) => {
    router.push(key);
  };

  const selectedKeys = [pathname];
  const openKeys = [pathname.split('/').slice(0, 2).join('/')];

  return (
    <Sider 
      trigger={null}
      collapsible 
      collapsed={collapsed}
      onCollapse={onCollapse}
      theme={darkMode ? 'dark' : 'light'}
      className={`${darkMode ? 'bg-gray-800' : 'bg-white'} border-r border-gray-200 dark:border-gray-700`}
      width={256}
      collapsedWidth={80}
    >
      {/* Logo Section */}
      <div className={`h-16 flex items-center justify-center border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        {!collapsed ? (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-sm font-bold">ST</span>
            </div>
            <span className={`font-bold text-lg ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Smart Track
            </span>
          </div>
        ) : (
          <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white text-sm font-bold">ST</span>
          </div>
        )}
      </div>

      {/* User Info Section - when not collapsed */}
      {!collapsed && (
        <div className={`p-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">
                {user.full_name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className={`text-sm font-medium truncate ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {user.full_name}
              </p>
              <p className={`text-xs truncate ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {user.role?.name || user.profile?.role_type}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Navigation Menu */}
      <div className="flex-1 overflow-y-auto">
        <Menu
          theme={darkMode ? 'dark' : 'light'}
          mode="inline"
          selectedKeys={selectedKeys}
          defaultOpenKeys={openKeys}
          items={getMenuItems()}
          onClick={handleMenuClick}
          className="border-none"
        />
      </div>

      {/* Notifications Section - when not collapsed */}
      {!collapsed && (
        <div className={`p-4 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <BellOutlined className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Notifications
              </span>
            </div>
            <Badge count={3} size="small" />
          </div>
        </div>
      )}
    </Sider>
  );
}
