'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import providers to avoid SSR issues
const QueryProvider = dynamic(() => import('@/providers/QueryProvider'), { ssr: false });
const AuthProvider = dynamic(() => import('@/providers/AuthProvider'), { ssr: false });
const AntdProvider = dynamic(() => import('@/components/providers/AntdProvider'), { ssr: false });

export default function ClientWrapper({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div>{children}</div>;
  }

  return (
    <AntdProvider>
      <QueryProvider>
        <AuthProvider>
          {children}
        </AuthProvider>
      </QueryProvider>
    </AntdProvider>
  );
}
