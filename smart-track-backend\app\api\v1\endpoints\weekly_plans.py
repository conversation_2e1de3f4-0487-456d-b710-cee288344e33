from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from typing import List, Optional
from datetime import datetime, date, timedelta

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User, UserProfile
from app.models.visit import Visit
from app.models.customer import Customer
from app.schemas.visit import VisitCreate, VisitUpdate, Visit as VisitSchema

router = APIRouter()

@router.get("/", 
           summary="Get Weekly Plans",
           description="Get weekly plans for a supervisor's team",
           tags=["weekly-plans"])
async def get_weekly_plans(
       supervisor_id: int = Query(..., description="Supervisor ID"),
    week_start: Optional[str] = Query(None, description="Week start date (YYYY-MM-DD)"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get weekly plans for supervisor's team"""
    
    # Verify supervisor exists and current user has permission
    supervisor = db.query(User).filter(User.id == supervisor_id).first()
    if not supervisor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supervisor not found"
        )
    
    # Check permission - user must be the supervisor or have manager role
    if current_user.id != supervisor_id:
        if not current_user.profile or current_user.profile.role_type != "manager":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
    
    # Calculate week dates
    if week_start:
        week_start_date = datetime.strptime(week_start, '%Y-%m-%d').date()
    else:
        today = date.today()
        week_start_date = today - timedelta(days=today.weekday())
    
    week_end_date = week_start_date + timedelta(days=6)
    
    # Get team members
    team_members = (
        db.query(User)
        .join(UserProfile, User.id == UserProfile.user_id)
        .filter(
            UserProfile.supervisor_id == supervisor_id,
            User.is_active == True
        )
        .all()
    )
    
    # Get visits for the week for all team members
    team_member_ids = [member.id for member in team_members]
    
    visits = db.query(Visit).join(Customer, Visit.customer_id == Customer.id).filter(
        Visit.user_id.in_(team_member_ids),
        Visit.planned_date >= week_start_date,
        Visit.planned_date <= week_end_date
    ).all()
    
    # Group visits by team member and day
    team_plans = []
    for member in team_members:
        member_visits = [v for v in visits if v.user_id == member.id]
        
        # Group visits by day of week
        daily_plans = {
            'Monday': [],
            'Tuesday': [],
            'Wednesday': [],
            'Thursday': [],
            'Friday': [],
            'Saturday': [],
            'Sunday': []
        }
        
        total_visits = len(member_visits)
        completed_visits = len([v for v in member_visits if v.status == 'completed'])
        
        for visit in member_visits:
            day_name = visit.planned_date.strftime('%A')
            if day_name in daily_plans:
                daily_plans[day_name].append({
                    'id': visit.id,
                    'customer_id': visit.customer_id,
                    'customer_name': visit.customer.name,
                    'customer_city': visit.customer.city,
                    'planned_date': visit.planned_date.isoformat(),
                    'planned_time': visit.planned_time,
                    'status': visit.status,
                    'purpose': visit.purpose,
                    'notes': visit.notes
                })
        
        team_plans.append({
            'user_id': member.id,
            'user_name': member.full_name,
            'user_email': member.email,
            'total_visits': total_visits,
            'completed_visits': completed_visits,
            'daily_plans': daily_plans
        })
    
    return {
        'week_start': week_start_date.isoformat(),
        'week_end': week_end_date.isoformat(),
        'supervisor_id': supervisor_id,
        'team_plans': team_plans
    }

@router.post("/visits",
            response_model=VisitSchema,
            summary="Create Visit for Team Member",
            description="Create a visit for a team member",
            tags=["weekly-plans"])
async def create_visit_for_salesrep(
    visit: VisitCreate,
    supervisor_id: int = Query(..., description="Supervisor ID"),
    salesrep_id: int = Query(..., description="Salesrep ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a visit for a team member"""
    
    # Verify supervisor exists and current user has permission
    if current_user.id != supervisor_id:
        if not current_user.profile or current_user.profile.role_type != "manager":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
    
    # Verify salesrep is in supervisor's team
    salesrep = (
        db.query(User)
        .join(UserProfile, User.id == UserProfile.user_id)
        .filter(
            User.id == salesrep_id,
            UserProfile.supervisor_id == supervisor_id,
            User.is_active == True
        )
        .first()
    )
    
    if not salesrep:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Salesrep not found in supervisor's team"
        )
    
    # Verify customer exists
    customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Create the visit
    db_visit = Visit(
        user_id=salesrep_id,
        customer_id=visit.customer_id,
        planned_date=visit.planned_date,
        planned_time=visit.planned_time,
        purpose=visit.purpose or 'Regular visit',
        notes=visit.notes,
        status='planned'
    )
    
    db.add(db_visit)
    db.commit()
    db.refresh(db_visit)
    
    # Return visit with customer data
    return {
        'id': db_visit.id,
        'user_id': db_visit.user_id,
        'customer_id': db_visit.customer_id,
        'planned_date': db_visit.planned_date,
        'planned_time': db_visit.planned_time,
        'purpose': db_visit.purpose,
        'notes': db_visit.notes,
        'status': db_visit.status,
        'actual_start_time': db_visit.actual_start_time,
        'actual_end_time': db_visit.actual_end_time,
        'checkin_latitude': db_visit.checkin_latitude,
        'checkin_longitude': db_visit.checkin_longitude,
        'checkout_latitude': db_visit.checkout_latitude,
        'checkout_longitude': db_visit.checkout_longitude,
        'outcome': db_visit.outcome,
        'next_action': db_visit.next_action,
        'created_at': db_visit.created_at,
        'updated_at': db_visit.updated_at,
        'customer': {
            'id': customer.id,
            'code': customer.code,
            'name': customer.name,
            'email': customer.email,
            'phone': customer.phone,
            'address': customer.address,
            'city': customer.city,
            'province': customer.province,
            'region': customer.region
        } if customer else None
    }

@router.put("/visits/{visit_id}",
           response_model=VisitSchema,
           summary="Update Team Visit",
           description="Update a visit for a team member",
           tags=["weekly-plans"])
async def update_team_visit(
    visit_id: int,
    visit_data: VisitUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a visit for a team member"""
    
    # Get the visit and verify it exists
    visit = db.query(Visit).filter(Visit.id == visit_id).first()
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found"
        )
    
    # Get the visit owner and their supervisor
    visit_user = (
        db.query(User)
        .join(UserProfile, User.id == UserProfile.user_id)
        .filter(User.id == visit.user_id)
        .first()
    )
    
    if not visit_user or not visit_user.profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit owner not found"
        )
    
    # Check permission - user must be the supervisor of the visit owner or have manager role
    if current_user.id != visit_user.profile.supervisor_id:
        if not current_user.profile or current_user.profile.role_type != "manager":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
    
    # Update visit fields
    update_data = visit_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(visit, field, value)
    
    db.commit()
    db.refresh(visit)
    
    # Get customer data for response
    customer = db.query(Customer).filter(Customer.id == visit.customer_id).first()
    
    # Return updated visit with customer data
    return {
        'id': visit.id,
        'user_id': visit.user_id,
        'customer_id': visit.customer_id,
        'planned_date': visit.planned_date,
        'planned_time': visit.planned_time,
        'purpose': visit.purpose,
        'notes': visit.notes,
        'status': visit.status,
        'actual_start_time': visit.actual_start_time,
        'actual_end_time': visit.actual_end_time,
        'checkin_latitude': visit.checkin_latitude,
        'checkin_longitude': visit.checkin_longitude,
        'checkout_latitude': visit.checkout_latitude,
        'checkout_longitude': visit.checkout_longitude,
        'outcome': visit.outcome,
        'next_action': visit.next_action,
        'created_at': visit.created_at,
        'updated_at': visit.updated_at,
        'customer': {
            'id': customer.id,
            'code': customer.code,
            'name': customer.name,
            'email': customer.email,
            'phone': customer.phone,
            'address': customer.address,
            'city': customer.city,
            'province': customer.province,
            'region': customer.region
        } if customer else None
    }

@router.delete("/supervisor/{supervisor_id}/visits/{visit_id}",
              summary="Delete Team Visit",
              description="Delete a visit for a team member",
              tags=["weekly-plans"])
async def delete_team_visit(
    supervisor_id: int,
    visit_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a visit for a team member"""
    
    # Verify current user has permission
    if current_user.id != supervisor_id:
        if not current_user.profile or current_user.profile.role_type != "manager":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
    
    # Get the visit and verify it belongs to supervisor's team
    visit = (
        db.query(Visit)
        .join(User, Visit.user_id == User.id)
        .join(UserProfile, User.id == UserProfile.user_id)
        .filter(
            Visit.id == visit_id,
            UserProfile.supervisor_id == supervisor_id
        )
        .first()
    )
    
    if not visit:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Visit not found or not in supervisor's team"
        )
    
    db.delete(visit)
    db.commit()
    
    return {"message": "Visit deleted successfully"}

@router.get("/supervisor/{supervisor_id}/customers",
           response_model=List,
           summary="Get Supervisor's Customers",
           description="Get all customers accessible by supervisor's team",
           tags=["weekly-plans"])
async def get_supervisor_customers(
    supervisor_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all customers for supervisor's territory"""
    
    # Verify current user has permission
    if current_user.id != supervisor_id:
        if not current_user.profile or current_user.profile.role_type != "manager":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
    
    # For now, return all active customers
    # In a real implementation, you might filter by territory/region
    customers = db.query(Customer).filter(Customer.is_active == True).all()
    
    return [
        {
            'id': customer.id,
            'code': customer.code,
            'name': customer.name,
            'email': customer.email,
            'phone': customer.phone,
            'address': customer.address,
            'city': customer.city,
            'province': customer.province,
            'region': customer.region,
            'customer_type': customer.customer_type,
            'is_active': customer.is_active
        }
        for customer in customers
    ]

@router.get("/supervisor/{supervisor_id}/visits",
           response_model=List[VisitSchema],
           summary="Get Team Visits for Supervisor",
           description="Get all visits for supervisor's team members with detailed information",
           tags=["weekly-plans"])
async def get_supervisor_team_visits(
    supervisor_id: int,
    skip: int = Query(0, description="Number of records to skip"),
    limit: int = Query(100, description="Maximum number of records to return"),
    status: Optional[str] = Query(None, description="Filter by visit status"),
    date_from: Optional[date] = Query(None, description="Filter visits from this date"),
    date_to: Optional[date] = Query(None, description="Filter visits to this date"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all visits for supervisor's team members"""
    
    # Verify current user has permission
    if current_user.id != supervisor_id:
        if not current_user.profile or current_user.profile.role_type != "manager":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
    
    # Get team member IDs
    team_members = (
        db.query(User)
        .join(UserProfile, User.id == UserProfile.user_id)
        .filter(
            UserProfile.supervisor_id == supervisor_id,
            User.is_active == True
        )
        .all()
    )
    
    if not team_members:
        return []
    
    team_member_ids = [member.id for member in team_members]
    
    # Build query for team visits
    query = (
        db.query(Visit)
        .options(
            joinedload(Visit.customer),
            joinedload(Visit.user)
        )
        .filter(Visit.user_id.in_(team_member_ids))
    )
    
    # Apply filters
    if status:
        query = query.filter(Visit.status == status)
    
    if date_from:
        query = query.filter(Visit.planned_date >= date_from)
    
    if date_to:
        query = query.filter(Visit.planned_date <= date_to)
    
    # Order by planned date (newest first)
    query = query.order_by(Visit.planned_date.desc())
    
    # Apply pagination
    visits = query.offset(skip).limit(limit).all()
    
    # Format response with user and customer information
    formatted_visits = []
    for visit in visits:
        user_info = next((member for member in team_members if member.id == visit.user_id), None)
        
        formatted_visit = {
            'id': visit.id,
            'user_id': visit.user_id,
            'customer_id': visit.customer_id,
            'planned_date': visit.planned_date,
            'planned_time': visit.planned_time,
            'purpose': visit.purpose,
            'notes': visit.notes,
            'status': visit.status,
            'actual_start_time': visit.actual_start_time,
            'actual_end_time': visit.actual_end_time,
            'checkin_latitude': visit.checkin_latitude,
            'checkin_longitude': visit.checkin_longitude,
            'checkout_latitude': visit.checkout_latitude,
            'checkout_longitude': visit.checkout_longitude,
            'outcome': visit.outcome,
            'next_action': visit.next_action,
            'created_at': visit.created_at,
            'updated_at': visit.updated_at,
            'customer': {
                'id': visit.customer.id,
                'code': visit.customer.code,
                'name': visit.customer.name,
                'email': visit.customer.email,
                'phone': visit.customer.phone,
                'address': visit.customer.address,
                'city': visit.customer.city,
                'province': visit.customer.province,
                'region': visit.customer.region
            } if visit.customer else None,
            'user': {
                'id': user_info.id,
                'full_name': user_info.full_name,
                'email': user_info.email,
                'phone': user_info.phone
            } if user_info else None
        }
        formatted_visits.append(formatted_visit)
    
    return formatted_visits
