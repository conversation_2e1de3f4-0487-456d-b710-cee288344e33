from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from decimal import Decimal

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.customer import Customer, DistributionChannel, CustomerDistributionChannel, CustomerContact, Branch
from app.models.user import User
from app.models.visit import Visit
from app.schemas.customer import (
    CustomerResponse, 
    CustomerCreate, 
    CustomerUpdate,
    DistributionChannelResponse,
    CustomerDistributionChannelResponse,
    CustomerDistributionChannelCreate,
    CustomerContactCreate,
    CustomerContactResponse,
    BranchCreate,
    BranchUpdate,
    BranchResponse
)
from app.schemas.visit import Visit as VisitSchema

router = APIRouter()

@router.get("/", response_model=List[CustomerResponse])
async def get_customers(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="Search by customer name or code"),
    distribution_channel_id: Optional[int] = Query(None, description="Filter by distribution channel"),
    city: Optional[str] = Query(None, description="Filter by city"),
    province: Optional[str] = Query(None, description="Filter by province"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get list of customers with optional filtering"""
    query = db.query(Customer)
    
    if search:
        query = query.filter(
            (Customer.name.ilike(f"%{search}%")) | 
            (Customer.code.ilike(f"%{search}%"))
        )
    
    if city:
        query = query.filter(Customer.city.ilike(f"%{city}%"))
        
    if province:
        query = query.filter(Customer.province.ilike(f"%{province}%"))
    
    if distribution_channel_id:
        query = query.join(CustomerDistributionChannel).filter(
            CustomerDistributionChannel.distribution_channel_id == distribution_channel_id
        )
    
    # Removed filter for is_active == True to include all customers regardless of active status
    # query = query.filter(Customer.is_active == True)
    # Order by creation date descending (newest first)
    query = query.order_by(Customer.created_at.desc())
    customers = query.offset(skip).limit(limit).all()
    return customers

@router.get("/{customer_id}", response_model=CustomerResponse)
async def get_customer(
    customer_id: int, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get customer by ID"""
    customer = db.query(Customer).filter(
        Customer.id == customer_id
        # Removed is_active == True filter to allow retrieval of inactive customers
    ).first()
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    return customer

@router.post("/", response_model=CustomerResponse)
async def create_customer(
    customer: CustomerCreate, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new customer - requires admin or manager role"""
    # Check if current user has permission to create customers
    if current_user.role and current_user.role.name.lower() not in ['admin', 'administrator', 'manager']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators and managers can create customers"
        )
    
    # Check if customer code already exists
    existing = db.query(Customer).filter(Customer.code == customer.code).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Customer code already exists"
        )
    
    db_customer = Customer(**customer.dict())
    db.add(db_customer)
    db.commit()
    db.refresh(db_customer)
    
    return db_customer

@router.put("/{customer_id}", response_model=CustomerResponse)
async def update_customer(
    customer_id: int, 
    customer_update: CustomerUpdate, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update customer information"""
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Check for unique customer code if being updated
    update_data = customer_update.dict(exclude_unset=True)
    if 'code' in update_data and update_data['code'] != customer.code:
        existing = db.query(Customer).filter(
            Customer.code == update_data['code'],
            Customer.id != customer_id
        ).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Customer code already exists"
            )
    
    for field, value in update_data.items():
        setattr(customer, field, value)
    
    db.commit()
    db.refresh(customer)
    
    return customer

@router.delete("/{customer_id}")
async def delete_customer(
    customer_id: int, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Soft delete customer (set is_active to False) - requires admin role"""
    # Check if current user has permission to delete customers
    if current_user.role and current_user.role.name.lower() not in ['admin', 'administrator']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can delete customers"
        )
    
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    customer.is_active = False
    db.commit()
    
    return {"message": "Customer deleted successfully"}

@router.get("/{customer_id}/distribution-channels", response_model=List[CustomerDistributionChannelResponse])
async def get_customer_distribution_channels(
    customer_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get customer's distribution channels"""
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    assignments = db.query(CustomerDistributionChannel).filter(
        CustomerDistributionChannel.customer_id == customer_id,
        CustomerDistributionChannel.is_active == True
    ).all()
    
    return assignments

@router.post("/{customer_id}/distribution-channels", response_model=CustomerDistributionChannelResponse)
async def assign_customer_to_distribution_channel(
    customer_id: int,
    assignment_data: CustomerDistributionChannelCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Assign customer to a distribution channel with a sales rep"""
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Validate distribution channel exists
    channel = db.query(DistributionChannel).filter(
        DistributionChannel.id == assignment_data.distribution_channel_id
    ).first()
    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Distribution channel not found"
        )
    
    # Validate sales rep exists
    sales_rep = db.query(User).filter(
        User.id == assignment_data.sales_rep_id
    ).first()
    if not sales_rep:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sales representative not found"
        )
    
    # Check if assignment already exists
    existing = db.query(CustomerDistributionChannel).filter(
        CustomerDistributionChannel.customer_id == customer_id,
        CustomerDistributionChannel.distribution_channel_id == assignment_data.distribution_channel_id
    ).first()
    
    if existing:
        if existing.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Customer is already assigned to this distribution channel"
            )
        else:
            # Reactivate existing assignment
            existing.is_active = True
            existing.sales_rep_id = assignment_data.sales_rep_id
            existing.manager_code = assignment_data.manager_code
            db.commit()
            db.refresh(existing)
            return existing
    
    # Create new assignment
    new_assignment = CustomerDistributionChannel(
        customer_id=customer_id,
        distribution_channel_id=assignment_data.distribution_channel_id,
        sales_rep_id=assignment_data.sales_rep_id,
        manager_code=assignment_data.manager_code,
        is_active=True
    )
    
    db.add(new_assignment)
    db.commit()
    db.refresh(new_assignment)
    
    return new_assignment

@router.delete("/{customer_id}/distribution-channels/{channel_id}")
async def remove_customer_from_distribution_channel(
    customer_id: int,
    channel_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Remove customer from a distribution channel (soft delete)"""
    assignment = db.query(CustomerDistributionChannel).filter(
        CustomerDistributionChannel.customer_id == customer_id,
        CustomerDistributionChannel.distribution_channel_id == channel_id
    ).first()
    
    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer distribution channel assignment not found"
        )
    
    assignment.is_active = False
    db.commit()
    
    return {"message": "Customer removed from distribution channel successfully"}

# Customer Visit History
@router.get("/{customer_id}/visits", response_model=List[VisitSchema])
async def get_customer_visits(
    customer_id: int,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get visit history for a specific customer"""
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    visits = db.query(Visit).filter(
        Visit.customer_id == customer_id,
        Visit.is_active == True
    ).order_by(Visit.planned_date.desc()).offset(skip).limit(limit).all()
    
    return visits

# Customer Contacts Management
@router.post("/{customer_id}/contacts", response_model=CustomerContactResponse)
async def create_customer_contact(
    customer_id: int,
    contact: CustomerContactCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new contact for a customer"""
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # If this is set as primary, unset other primary contacts
    if contact.is_primary:
        db.query(CustomerContact).filter(
            CustomerContact.customer_id == customer_id,
            CustomerContact.is_primary == True
        ).update({"is_primary": False})
    
    contact_data = contact.dict()
    contact_data["customer_id"] = customer_id
    
    db_contact = CustomerContact(**contact_data)
    db.add(db_contact)
    db.commit()
    db.refresh(db_contact)
    
    return db_contact

@router.get("/{customer_id}/contacts", response_model=List[CustomerContactResponse])
async def get_customer_contacts(
    customer_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get contacts for a specific customer"""
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    contacts = db.query(CustomerContact).filter(
        CustomerContact.customer_id == customer_id
    ).order_by(CustomerContact.is_primary.desc(), CustomerContact.created_at.desc()).all()
    
    return contacts

@router.put("/{customer_id}/contacts/{contact_id}", response_model=CustomerContactResponse)
async def update_customer_contact(
    customer_id: int,
    contact_id: int,
    contact_update: CustomerContactCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a customer contact"""
    contact = db.query(CustomerContact).filter(
        CustomerContact.id == contact_id,
        CustomerContact.customer_id == customer_id
    ).first()
    
    if not contact:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer contact not found"
        )
    
    # If this is set as primary, unset other primary contacts
    if contact_update.is_primary and not contact.is_primary:
        db.query(CustomerContact).filter(
            CustomerContact.customer_id == customer_id,
            CustomerContact.is_primary == True
        ).update({"is_primary": False})
    
    update_data = contact_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(contact, field, value)
    
    db.commit()
    db.refresh(contact)
    
    return contact

@router.delete("/{customer_id}/contacts/{contact_id}")
async def delete_customer_contact(
    customer_id: int,
    contact_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a customer contact"""
    contact = db.query(CustomerContact).filter(
        CustomerContact.id == contact_id,
        CustomerContact.customer_id == customer_id
    ).first()
    
    if not contact:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer contact not found"
        )
    
    db.delete(contact)
    db.commit()
    
    return {"message": "Customer contact deleted successfully"}

# Customer Branch Management Endpoints

@router.get("/{customer_id}/branches/", response_model=List[BranchResponse])
async def get_customer_branches(
    customer_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all branches for a customer"""
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    branches = db.query(Branch).filter(Branch.customer_id == customer_id).all()
    return branches

@router.post("/{customer_id}/branches/", response_model=BranchResponse)
async def create_customer_branch(
    customer_id: int,
    branch: BranchCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new branch for a customer"""
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    db_branch = Branch(
        customer_id=customer_id,
        **branch.dict(exclude={'customer_id'})
    )
    db.add(db_branch)
    db.commit()
    db.refresh(db_branch)
    
    return db_branch

@router.get("/{customer_id}/branches/{branch_id}", response_model=BranchResponse)
async def get_customer_branch(
    customer_id: int,
    branch_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific branch for a customer"""
    branch = db.query(Branch).filter(
        Branch.id == branch_id,
        Branch.customer_id == customer_id
    ).first()
    
    if not branch:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Branch not found"
        )
    
    return branch

@router.put("/{customer_id}/branches/{branch_id}", response_model=BranchResponse)
async def update_customer_branch(
    customer_id: int,
    branch_id: int,
    branch_update: BranchUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a customer branch"""
    branch = db.query(Branch).filter(
        Branch.id == branch_id,
        Branch.customer_id == customer_id
    ).first()
    
    if not branch:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Branch not found"
        )
    
    update_data = branch_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(branch, field, value)
    
    db.commit()
    db.refresh(branch)
    
    return branch

@router.delete("/{customer_id}/branches/{branch_id}")
async def delete_customer_branch(
    customer_id: int,
    branch_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a customer branch"""
    branch = db.query(Branch).filter(
        Branch.id == branch_id,
        Branch.customer_id == customer_id
    ).first()
    
    if not branch:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Branch not found"
        )
    
    db.delete(branch)
    db.commit()
    
    return {"message": "Branch deleted successfully"}
