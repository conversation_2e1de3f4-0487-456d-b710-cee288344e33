export interface User {
  id: number;
  sap_code: string;
  username: string;
  email: string;
  full_name: string;
  phone?: string;
  is_active: boolean;
  is_verified: boolean;
  role_id: number;
  created_at: string;
  updated_at?: string;
  role: Role;
  profile?: UserProfile;
}

export interface Role {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
}

export interface UserProfile {
  id: number;
  user_id: number;
  distribution_channel_id?: number;
  role_type: 'manager' | 'supervisor' | 'salesrep' | 'admin';
  supervisor_id?: number;
  profile_image?: string;
  address?: string;
  emergency_contact?: string;
  created_at: string;
  updated_at?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface Customer {
  id: number;
  code: string;
  name: string;
  customer_group?: string;
  address?: string;
  city?: string;
  province?: string;
  region?: string;
  phone?: string;
  email?: string;
  customer_type?: string;
  credit_limit?: number;
  current_balance?: number;
  customer_statistics_group?: string;
  sdst?: string;
  latitude?: number;
  longitude?: number;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

export interface Visit {
  id: number;
  user_id: number;
  customer_id: number;
  planned_date: string;
  actual_start_time?: string;
  actual_end_time?: string;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  purpose?: string;
  notes?: string;
  outcome?: string;
  next_action?: string;
  checkin_latitude?: number;
  checkin_longitude?: number;
  checkout_latitude?: number;
  checkout_longitude?: number;
  created_at: string;
  updated_at?: string;
  user?: User;
  customer?: Customer;
}

export interface WeeklyPlan {
  id: number;
  user_id: number;
  week_start_date: string;
  week_end_date: string;
  status: 'draft' | 'approved' | 'active' | 'completed';
  created_by: number;
  created_at: string;
  updated_at?: string;
  visits: Visit[];
}

export interface DashboardStats {
  total_visits: number;
  completed_visits: number;
  pending_visits: number;
  total_customers: number;
  active_sales_reps: number;
  completion_rate: number;
}
