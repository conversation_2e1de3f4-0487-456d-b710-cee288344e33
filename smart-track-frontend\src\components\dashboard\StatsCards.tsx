'use client';

import { useQuery } from '@tanstack/react-query';
import { apiService } from '@/services/api';
import { Card, Statistic, Row, Col, Skeleton, Space } from 'antd';
import { 
  FileTextOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined, 
  ShopOutlined, 
  TeamOutlined, 
  BarChartOutlined 
} from '@ant-design/icons';

export default function StatsCards() {
  const { data: stats, isLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: () => apiService.getDashboardStats(),
  });

  const statsData = stats?.data || {
    total_visits: 0,
    completed_visits: 0,
    pending_visits: 0,
    total_customers: 0,
    active_sales_reps: 0,
    completion_rate: 0,
  };

  const cards = [
    {
      title: 'Total Visits',
      value: statsData.total_visits,
      icon: <FileTextOutlined style={{ color: '#1890ff' }} />,
      valueStyle: { color: '#1890ff' },
    },
    {
      title: 'Completed Visits',
      value: statsData.completed_visits,
      icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      valueStyle: { color: '#52c41a' },
    },
    {
      title: 'Pending Visits',
      value: statsData.pending_visits,
      icon: <ClockCircleOutlined style={{ color: '#faad14' }} />,
      valueStyle: { color: '#faad14' },
    },
    {
      title: 'Total Customers',
      value: statsData.total_customers,
      icon: <ShopOutlined style={{ color: '#722ed1' }} />,
      valueStyle: { color: '#722ed1' },
    },
    {
      title: 'Active Sales Reps',
      value: statsData.active_sales_reps,
      icon: <TeamOutlined style={{ color: '#eb2f96' }} />,
      valueStyle: { color: '#eb2f96' },
    },
    {
      title: 'Completion Rate',
      value: Math.round(statsData.completion_rate),
      suffix: '%',
      icon: <BarChartOutlined style={{ color: '#13c2c2' }} />,
      valueStyle: { color: '#13c2c2' },
    },
  ];

  if (isLoading) {
    return (
      <Row gutter={[16, 16]}>
        {Array.from({ length: 6 }).map((_, index) => (
          <Col xs={24} sm={12} md={8} lg={6} xl={4} key={index}>
            <Card>
              <Skeleton active />
            </Card>
          </Col>
        ))}
      </Row>
    );
  }

  return (
    <Row gutter={[16, 16]}>
      {cards.map((card, index) => (
        <Col xs={24} sm={12} md={8} lg={6} xl={4} key={index}>
          <Card hoverable>
            <Statistic
              title={card.title}
              value={card.value}
              suffix={card.suffix}
              valueStyle={card.valueStyle}
              prefix={card.icon}
            />
          </Card>
        </Col>
      ))}
    </Row>
  );
}
