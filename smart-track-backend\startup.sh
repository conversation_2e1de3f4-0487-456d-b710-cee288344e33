#!/bin/sh

# Smart Track Backend Startup Script
# This script runs database migrations and starts the application

set -e  # Exit on any error

echo "🚀 Starting Smart Track Backend..."

# Print environment info (without sensitive data)
echo "📍 Environment: ${ENVIRONMENT:-development}"
echo "🗄️  Database: postgresql://${DATABASE_USERNAME}:***@${DATABASE_HOSTNAME}:${DATABASE_PORT}/${DATABASE_NAME}"

# Function to wait for database
wait_for_db() {
    echo "⏳ Waiting for database to be ready..."
    while ! pg_isready -h ${DATABASE_HOSTNAME} -p ${DATABASE_PORT} -U ${DATABASE_USERNAME}; do
        echo "   Database not ready, waiting 2 seconds..."
        sleep 2
    done
    echo "✅ Database is ready!"
}

# Function to run migrations
run_migrations() {
    echo "🔄 Running database migrations..."
    alembic upgrade head
    if [ $? -eq 0 ]; then
        echo "✅ Migrations completed successfully!"
    else
        echo "❌ Migrations failed!"
        exit 1
    fi
}

# Function to start the application
start_app() {
    echo "🎯 Starting FastAPI application..."
    if [ "${ENVIRONMENT}" = "development" ]; then
        echo "🔧 Running in development mode with auto-reload"
        exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --workers 1
    else
        echo "🏭 Running in production mode"
        exec gunicorn -w 4 -k uvicorn.workers.UvicornWorker app.main:app --bind 0.0.0.0:8000
    fi
}

# Main execution
main() {
    # Wait for database to be available
    wait_for_db
    
    # Run database migrations
    run_migrations
    
    # Start the application
    start_app
}

# Run main function
main
