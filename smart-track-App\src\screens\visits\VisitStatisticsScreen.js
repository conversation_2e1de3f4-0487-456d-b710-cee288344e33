import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  Paragraph,
  ActivityIndicator,
  Chip,
  Surface,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { visitService } from '../../services/visitService.js';
import { useNavigation } from '@react-navigation/native';

const VisitStatisticsScreen = ({ navigation }) => {
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('month'); // week, month, quarter

  useEffect(() => {
    loadStatistics();
  }, [timeframe]);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      
      // Calculate date range based on timeframe
      const now = new Date();
      let dateFrom;
      
      switch (timeframe) {
        case 'week':
          dateFrom = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          dateFrom = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'quarter':
          const quarterStart = Math.floor(now.getMonth() / 3) * 3;
          dateFrom = new Date(now.getFullYear(), quarterStart, 1);
          break;
        default:
          dateFrom = new Date(now.getFullYear(), now.getMonth(), 1);
      }

      const params = {
        date_from: dateFrom.toISOString(),
        date_to: now.toISOString(),
      };

      const data = await visitService.getVisitStatistics(params);
      setStatistics(data);
    } catch (error) {
      console.error('Load statistics error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTimeframeTitle = () => {
    switch (timeframe) {
      case 'week':
        return 'This Week';
      case 'month':
        return 'This Month';
      case 'quarter':
        return 'This Quarter';
      default:
        return 'This Month';
    }
  };

  const formatDuration = (minutes) => {
    if (!minutes) return 'N/A';
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const StatCard = ({ title, value, subtitle, color = '#2196F3', icon }) => (
    <Surface style={[styles.statCard, { borderLeftColor: color }]}>
      <View style={styles.statHeader}>
        <MaterialIcons name={icon} size={24} color={color} />
        <Text style={styles.statTitle}>{title}</Text>
      </View>
      <Text style={[styles.statValue, { color }]}>{value}</Text>
      {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
    </Surface>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Loading statistics...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
      {/* Timeframe Selector */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.sectionTitle}>Timeframe</Title>
          <View style={styles.timeframeContainer}>
            {['week', 'month', 'quarter'].map((period) => (
              <Chip
                key={period}
                mode={timeframe === period ? 'flat' : 'outlined'}
                selected={timeframe === period}
                onPress={() => setTimeframe(period)}
                style={styles.timeframeChip}
              >
                {period === 'week' ? 'Week' : period === 'month' ? 'Month' : 'Quarter'}
              </Chip>
            ))}
          </View>
        </Card.Content>
      </Card>

      {/* Summary Statistics */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.sectionTitle}>Summary - {getTimeframeTitle()}</Title>
          
          <View style={styles.statsGrid}>
            <StatCard
              title="Total Visits"
              value={statistics?.total_visits || 0}
              icon="assignment"
              color="#2196F3"
            />
            
            <StatCard
              title="Completed"
              value={statistics?.completed_visits || 0}
              subtitle={`${Math.round(statistics?.completion_rate || 0)}% completion rate`}
              icon="check-circle"
              color="#4CAF50"
            />
            
            <StatCard
              title="Planned"
              value={statistics?.planned_visits || 0}
              icon="schedule"
              color="#FF9800"
            />
            
            <StatCard
              title="In Progress"
              value={statistics?.in_progress_visits || 0}
              icon="play-circle-filled"
              color="#9C27B0"
            />
          </View>
        </Card.Content>
      </Card>

      {/* Performance Metrics */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.sectionTitle}>Performance Metrics</Title>
          
          <View style={styles.metricItem}>
            <View style={styles.metricHeader}>
              <MaterialIcons name="timer" size={20} color="#666" />
              <Text style={styles.metricLabel}>Average Visit Duration</Text>
            </View>
            <Text style={styles.metricValue}>
              {formatDuration(statistics?.avg_visit_duration)}
            </Text>
          </View>

          <View style={styles.metricItem}>
            <View style={styles.metricHeader}>
              <MaterialIcons name="trending-up" size={20} color="#666" />
              <Text style={styles.metricLabel}>Completion Rate</Text>
            </View>
            <Text style={[styles.metricValue, styles.percentageValue]}>
              {Math.round(statistics?.completion_rate || 0)}%
            </Text>
          </View>

          <View style={styles.metricItem}>
            <View style={styles.metricHeader}>
              <MaterialIcons name="today" size={20} color="#666" />
              <Text style={styles.metricLabel}>Visits per Day</Text>
            </View>
            <Text style={styles.metricValue}>
              {statistics?.total_visits 
                ? Math.round((statistics.total_visits / (timeframe === 'week' ? 7 : timeframe === 'month' ? 30 : 90)) * 10) / 10
                : 0
              }
            </Text>
          </View>
        </Card.Content>
      </Card>

      {/* Visual Progress */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.sectionTitle}>Progress Overview</Title>
          
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { 
                    width: `${Math.min(statistics?.completion_rate || 0, 100)}%`,
                    backgroundColor: statistics?.completion_rate >= 80 ? '#4CAF50' : 
                                   statistics?.completion_rate >= 60 ? '#FF9800' : '#F44336'
                  }
                ]} 
              />
            </View>
            <Text style={styles.progressText}>
              {Math.round(statistics?.completion_rate || 0)}% completion rate
            </Text>
          </View>

          <View style={styles.goalContainer}>
            <Text style={styles.goalText}>
              {statistics?.completed_visits || 0} of {statistics?.total_visits || 0} visits completed
            </Text>
          </View>
        </Card.Content>
      </Card>

      {/* Quick Insights */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.sectionTitle}>Quick Insights</Title>
          
          <View style={styles.insightItem}>
            <MaterialIcons name="lightbulb-outline" size={16} color="#FF9800" />
            <Text style={styles.insightText}>
              {statistics?.completion_rate >= 80 
                ? 'Great job! You\'re maintaining a high completion rate.'
                : statistics?.completion_rate >= 60
                ? 'Good progress. Consider focusing on completing planned visits.'
                : 'There\'s room for improvement in visit completion.'
              }
            </Text>
          </View>

          {statistics?.avg_visit_duration && statistics.avg_visit_duration > 120 && (
            <View style={styles.insightItem}>
              <MaterialIcons name="lightbulb-outline" size={16} color="#2196F3" />
              <Text style={styles.insightText}>
                Your average visit duration is {formatDuration(statistics.avg_visit_duration)}. 
                Consider if visits can be more efficient.
              </Text>
            </View>
          )}

          {statistics?.total_visits < 5 && timeframe === 'week' && (
            <View style={styles.insightItem}>
              <MaterialIcons name="lightbulb-outline" size={16} color="#F44336" />
              <Text style={styles.insightText}>
                Consider planning more visits this week to improve your activity.
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  card: {
    margin: 16,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  timeframeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  timeframeChip: {
    minWidth: 80,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    width: '48%',
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    elevation: 1,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statTitle: {
    fontSize: 14,
    marginLeft: 8,
    color: '#666',
  },
  statValue: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statSubtitle: {
    fontSize: 12,
    color: '#999',
  },
  metricItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  metricLabel: {
    fontSize: 16,
    marginLeft: 8,
    color: '#333',
  },
  metricValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  percentageValue: {
    color: '#4CAF50',
  },
  progressContainer: {
    marginVertical: 16,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    textAlign: 'center',
    marginTop: 8,
    fontSize: 14,
    color: '#666',
  },
  goalContainer: {
    alignItems: 'center',
    marginTop: 8,
  },
  goalText: {
    fontSize: 14,
    color: '#333',
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  insightText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
    color: '#555',
    lineHeight: 20,
  },
});

export default VisitStatisticsScreen;
