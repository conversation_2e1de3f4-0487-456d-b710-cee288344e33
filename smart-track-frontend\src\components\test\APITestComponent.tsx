import React, { useState } from 'react';
import { <PERSON>ton, Card, Input, Alert, Space, Typography } from 'antd';
import { api } from '@/services/api';

const { Title, Text } = Typography;

export default function APITestComponent() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testLogin = async () => {
    try {
      setLoading(true);
      addResult('🔄 Testing login...');
      
      const response = await api.login({
        username: 'supervisor1',
        password: 'password123'
      });
      
      addResult(`✅ Login successful! User: ${response.data.user.full_name}`);
      return response.data.user.id;
    } catch (error: any) {
      addResult(`❌ Login failed: ${error.message}`);
      throw error;
    }
  };

  const testSupervisorEndpoints = async (supervisorId: number) => {
    try {
      addResult('🔄 Testing supervisor team endpoint...');
      const teamResponse = await api.getSupervisorTeam(supervisorId);
      addResult(`✅ Team endpoint success! Found ${teamResponse.data.length} team members`);

      addResult('🔄 Testing weekly plans endpoint...');
      const plansResponse = await api.getSupervisorWeeklyPlans(supervisorId);
      addResult(`✅ Weekly plans endpoint success! Found ${plansResponse.data.team_plans?.length || 0} plans`);

      addResult('🔄 Testing supervisor customers endpoint...');
      const customersResponse = await api.getSupervisorCustomers(supervisorId);
      addResult(`✅ Customers endpoint success! Found ${customersResponse.data?.length || 0} customers`);
      
    } catch (error: any) {
      addResult(`❌ Endpoint test failed: ${error.message}`);
      if (error.response) {
        addResult(`   Status: ${error.response.status}`);
        addResult(`   Data: ${JSON.stringify(error.response.data)}`);
      } else if (error.request) {
        addResult(`   No response received from server`);
      }
    }
  };

  const runFullTest = async () => {
    setTestResults([]);
    setLoading(true);
    
    try {
      const supervisorId = await testLogin();
      await testSupervisorEndpoints(supervisorId);
    } catch (error) {
      addResult('❌ Test sequence failed');
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <Card title="API Connection Test" style={{ margin: '20px' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Space>
          <Button 
            type="primary" 
            onClick={runFullTest} 
            loading={loading}
          >
            Run API Tests
          </Button>
          <Button onClick={clearResults}>Clear Results</Button>
        </Space>
        
        {testResults.length > 0 && (
          <Card size="small" title="Test Results">
            <div style={{ 
              backgroundColor: '#f5f5f5', 
              padding: '10px', 
              borderRadius: '4px',
              fontFamily: 'monospace',
              fontSize: '12px',
              maxHeight: '400px',
              overflow: 'auto'
            }}>
              {testResults.map((result, index) => (
                <div key={index} style={{ marginBottom: '5px' }}>
                  {result}
                </div>
              ))}
            </div>
          </Card>
        )}
      </Space>
    </Card>
  );
}
