'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { 
  Card, 
  Table, 
  Select, 
  Button, 
  Tag, 
  Space, 
  Row, 
  Col, 
  Statistic, 
  Typography, 
  Spin, 
  Empty,
  Avatar,
  Tooltip,
  Badge,
  Input,
  DatePicker,
  message
} from 'antd';
import { 
  ReloadOutlined, 
  EyeOutlined, 
  EditOutlined, 
  UserOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  SearchOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { User, Visit } from '@/types';
import { api } from '@/services/api';
import { useAuthStore } from '@/store/auth';
import styles from './VisitTracking.module.css';
import AuthDebugger from '../debug/AuthDebugger';
import '../../styles/visit-tracking-global.css';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface VisitTrackingProps {
  user?: User; // Optional user prop from parent component
}

interface VisitWithUser extends Visit {
  user?: User;
}

// Data validation helpers
const validateVisitData = (visit: any): boolean => {
  return !!(
    visit &&
    visit.id &&
    typeof visit.user_id === 'number' &&
    typeof visit.customer_id === 'number' &&
    visit.planned_date &&
    visit.status
  );
};

const validateCustomerData = (customer: any): boolean => {
  return !!(customer && customer.id && customer.name);
};

const validateUserData = (user: any): boolean => {
  return !!(user && user.id && user.full_name);
};

// Data enrichment helpers
const enrichVisitWithMissingData = (visit: any, teamMembers: User[]): any => {
  const enrichedVisit = { ...visit };
  
  // Enrich user data if missing
  if (!enrichedVisit.user && enrichedVisit.user_id) {
    const userData = teamMembers.find((member: User) => member.id === enrichedVisit.user_id);
    if (userData) {
      enrichedVisit.user = userData;
    }
  }
  
  // Ensure customer data structure
  if (enrichedVisit.customer && !validateCustomerData(enrichedVisit.customer)) {
    console.warn(`Invalid customer data for visit ${enrichedVisit.id}:`, enrichedVisit.customer);
  }
  
  return enrichedVisit;
};

export default function VisitTracking({ user: userProp }: VisitTrackingProps = {}) {
  const { user: authUser, isAuthenticated } = useAuthStore();
  const [visits, setVisits] = useState<VisitWithUser[]>([]);
  const [team, setTeam] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [userLoading, setUserLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'planned' | 'in_progress' | 'completed' | 'cancelled'>('all');
  const [selectedUser, setSelectedUser] = useState<number | null>(null);
  const [searchText, setSearchText] = useState('');

  // Use prop user or auth store user
  const user = userProp || authUser;

  // Check authentication state on mount
  useEffect(() => {
    const checkUser = () => {
      console.log('🔍 Checking authentication state...');
      console.log('  - User prop:', userProp);
      console.log('  - Auth store user:', authUser);
      console.log('  - Final user:', user);
      console.log('  - Is Authenticated:', isAuthenticated);
      console.log('  - User ID:', user?.id);
      
      if (user?.id) {
        console.log('✅ User available, proceeding with data fetch');
        setUserLoading(false);
        fetchData();
      } else if (userProp) {
        console.log('✅ User provided as prop, proceeding with data fetch');
        setUserLoading(false);
        fetchData();
      } else if (isAuthenticated === false) {
        console.log('❌ User not authenticated');
        setUserLoading(false);
        setLoading(false);
        message.error('Please log in to access visit tracking data');
      } else {
        console.log('⏳ Authentication state still loading...');
        // Wait a bit more for auth state to load
        setTimeout(checkUser, 500);
      }
    };

    checkUser();
  }, [user, authUser, userProp, isAuthenticated]);

  const fetchData = async () => {
    if (!user?.id) {
      console.error('❌ No user ID available');
      console.error('  - User object:', user);
      console.error('  - Is authenticated:', isAuthenticated);
      message.error('Authentication error: No user ID available. Please log in again.');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      console.log('🔍 Fetching supervisor visit tracking data...');
      console.log('  - Supervisor ID:', user.id);
      console.log('  - User role:', (user as any).profile?.role_type);
      
      // Fetch team members first
      console.log('📋 Step 1: Fetching team members...');
      const teamResponse = await api.getSupervisorTeam(user.id);
      console.log('  - Team members found:', teamResponse.data.length);
      
      if (teamResponse.data.length === 0) {
        message.warning('No team members found for this supervisor');
        setTeam([]);
        setVisits([]);
        return;
      }
      
      setTeam(teamResponse.data);
      console.log('  - Team members loaded:', teamResponse.data.map((m: User) => m.full_name));

      // Fetch visits for all team members using the supervisor endpoint
      console.log('📅 Step 2: Fetching team visits...');
      const visitsResponse = await api.getSupervisorTeamVisits(user.id, 0, 1000);
      console.log('  - Team visits found:', visitsResponse.data.length);
      
      // Validate visit data structure
      const validVisits = visitsResponse.data.filter((visit: any) => {
        const isValid = validateVisitData(visit);
        if (!isValid) {
          console.warn('⚠️ Invalid visit data:', visit);
        }
        return isValid;
      });
      
      console.log('  - Valid visits after filtering:', validVisits.length);
      
      // Log sample visit structure for debugging
      if (validVisits.length > 0) {
        const sampleVisit = validVisits[0];
        console.log('📋 Sample visit structure:');
        console.log('  - Visit ID:', sampleVisit.id);
        console.log('  - User ID:', sampleVisit.user_id);
        console.log('  - Customer ID:', sampleVisit.customer_id);
        console.log('  - Status:', sampleVisit.status);
        console.log('  - Customer data:', sampleVisit.customer ? 'Present' : 'Missing');
        console.log('  - User data:', sampleVisit.user ? 'Present' : 'Missing');
        
        if (sampleVisit.customer) {
          console.log('    - Customer name:', sampleVisit.customer.name);
          console.log('    - Customer city:', sampleVisit.customer.city);
          console.log('    - Customer valid:', validateCustomerData(sampleVisit.customer));
        }
        
        if (sampleVisit.user) {
          console.log('    - User name:', sampleVisit.user.full_name);
          console.log('    - User email:', sampleVisit.user.email);
          console.log('    - User valid:', validateUserData(sampleVisit.user));
        }
      }
      
      // Enrich visit data if needed
      const enrichedVisits = validVisits.map((visit: any) => {
        const enrichedVisit = enrichVisitWithMissingData(visit, teamResponse.data);
        
        // Final validation
        if (!enrichedVisit.customer && enrichedVisit.customer_id) {
          console.warn(`⚠️ Visit ${enrichedVisit.id} missing customer data for customer_id ${enrichedVisit.customer_id}`);
        }
        
        if (!enrichedVisit.user && enrichedVisit.user_id) {
          console.warn(`⚠️ Visit ${enrichedVisit.id} missing user data for user_id ${enrichedVisit.user_id}`);
        }
        
        return enrichedVisit;
      });
      
      setVisits(enrichedVisits);
      
      // Show success message with data summary
      const statusCounts = enrichedVisits.reduce((acc, visit) => {
        acc[visit.status] = (acc[visit.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      console.log('📊 Visit status summary:', statusCounts);
      message.success(
        `Visit data refreshed: ${enrichedVisits.length} visits loaded (${statusCounts.completed || 0} completed, ${statusCounts.planned || 0} planned, ${statusCounts.in_progress || 0} in progress)`
      );
      
    } catch (error: any) {
      console.error('❌ Error fetching visit tracking data:', error);
      
      // Provide more specific error information
      if (error.response) {
        console.error('  - Status:', error.response.status);
        console.error('  - Data:', error.response.data);
        
        if (error.response.status === 403) {
          message.error('Access denied: You do not have permission to view this data');
        } else if (error.response.status === 404) {
          message.error('Supervisor or team data not found');
        } else if (error.response.status === 500) {
          message.error('Server error: Please try again later');
        } else {
          message.error(`Failed to fetch visit data: ${error.response.status}`);
        }
      } else if (error.request) {
        console.error('  - No response received:', error.request);
        message.error('Network error: Unable to connect to server');
      } else {
        console.error('  - Error:', error.message);
        message.error('Unexpected error occurred');
      }
      
      setVisits([]);
      setTeam([]);
    } finally {
      setLoading(false);
    }
  };

  const filteredVisits = visits.filter(visit => {
    if (filter !== 'all' && visit.status !== filter) return false;
    if (selectedUser && visit.user_id !== selectedUser) return false;
    if (searchText && !visit.customer?.name?.toLowerCase().includes(searchText.toLowerCase())) return false;
    return true;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'processing';
      case 'planned': return 'warning';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'in_progress': return '🔄';
      case 'planned': return '📅';
      case 'cancelled': return '❌';
      default: return '❓';
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  const formatTime = (dateString: string) => {
    return format(new Date(dateString), 'h:mm a');
  };

  const getVisitDuration = (visit: Visit) => {
    if (!visit.actual_start_time || !visit.actual_end_time) return null;
    
    const start = new Date(visit.actual_start_time);
    const end = new Date(visit.actual_end_time);
    const durationMs = end.getTime() - start.getTime();
    const durationMin = Math.round(durationMs / (1000 * 60));
    
    if (durationMin < 60) return `${durationMin}m`;
    
    const hours = Math.floor(durationMin / 60);
    const minutes = durationMin % 60;
    return `${hours}h ${minutes}m`;
  };

  const columns = [
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <UserOutlined />
          Sales Rep
        </div>
      ),
      key: 'user',
      width: 200,
      render: (_: any, visit: VisitWithUser) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <Avatar 
            size="default" 
            icon={<UserOutlined />} 
            style={{ backgroundColor: '#1890ff' }}
          />
          <div>
            <div style={{ fontWeight: 600, color: '#262626' }}>
              {visit.user?.full_name || 'Unknown'}
            </div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {visit.user?.email || ''}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          🏢 Customer
        </div>
      ),
      key: 'customer',
      width: 200,
      render: (_: any, visit: VisitWithUser) => (
        <div>
          <div style={{ fontWeight: 600, color: '#262626' }}>
            {visit.customer?.name || 'Unknown Customer'}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 4, marginTop: 2 }}>
            <span style={{ fontSize: 12 }}>📍</span>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {visit.customer?.city || 'Unknown Location'}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <CalendarOutlined />
          Date & Time
        </div>
      ),
      key: 'datetime',
      width: 180,
      render: (_: any, visit: VisitWithUser) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6, marginBottom: 4 }}>
            <CalendarOutlined style={{ color: '#1890ff' }} />
            <span style={{ fontWeight: 500 }}>{formatDate(visit.planned_date)}</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <ClockCircleOutlined style={{ color: '#faad14' }} />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {visit.actual_start_time 
                ? `Started: ${formatTime(visit.actual_start_time)}`
                : 'Not started'
              }
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          📊 Status
        </div>
      ),
      key: 'status',
      width: 120,
      render: (_: any, visit: VisitWithUser) => (
        <Tag 
          color={getStatusColor(visit.status)} 
          style={{ 
            borderRadius: 16, 
            padding: '4px 12px',
            fontWeight: 500,
            fontSize: 12
          }}
        >
          <span style={{ marginRight: 4 }}>{getStatusIcon(visit.status)}</span>
          {visit.status.charAt(0).toUpperCase() + visit.status.slice(1).replace('_', ' ')}
        </Tag>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          ⏱️ Duration
        </div>
      ),
      key: 'duration',
      width: 100,
      render: (_: any, visit: VisitWithUser) => {
        const duration = getVisitDuration(visit);
        return duration ? (
          <Badge 
            count={duration} 
            style={{ 
              backgroundColor: '#52c41a',
              borderRadius: 12,
              fontSize: 11,
              height: 20,
              lineHeight: '20px',
              padding: '0 8px'
            }}
          />
        ) : (
          <Text type="secondary">-</Text>
        );
      },
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          🎯 Purpose
        </div>
      ),
      key: 'purpose',
      width: 150,
      render: (_: any, visit: VisitWithUser) => (
        <Tooltip title={visit.purpose || 'No purpose specified'}>
          <Text ellipsis style={{ maxWidth: 130 }}>
            {visit.purpose || '-'}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          ⚙️ Actions
        </div>
      ),
      key: 'actions',
      width: 120,
      fixed: 'right' as const,
      render: (_: any, visit: VisitWithUser) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="text"
              icon={<EyeOutlined />}
              className={styles.actionButton}
              onClick={() => {
                message.info(`Viewing details for ${visit.customer?.name}`);
              }}
              style={{ color: '#1890ff' }}
            />
          </Tooltip>
          {visit.status === 'planned' && (
            <Tooltip title="Edit Visit">
              <Button
                type="text"
                icon={<EditOutlined />}
                className={styles.actionButton}
                onClick={() => {
                  message.info(`Editing visit for ${visit.customer?.name}`);
                }}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  if (userLoading) {
    return (
      <div className={styles.visitTrackingContainer}>
        <Card className={styles.headerCard}>
          <div className={styles.loadingContainer}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              <Text>Loading authentication state...</Text>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  if (!user) {
    return (
      <div className={styles.visitTrackingContainer}>
        <Card className={styles.headerCard}>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <Text type="secondary" style={{ fontSize: 16 }}>
              Authentication required to access visit tracking data
            </Text>
            <div style={{ marginTop: 16 }}>
              <Button type="primary" onClick={() => window.location.href = '/login'}>
                Go to Login
              </Button>
              <Button 
                style={{ marginLeft: 8 }}
                onClick={() => {
                  console.log('🔍 Authentication Debug:');
                  console.log('  - User prop:', userProp);
                  console.log('  - Auth store user:', authUser);
                  console.log('  - Is Authenticated:', isAuthenticated);
                  message.info('Check console for debug info');
                }}
              >
                Debug
              </Button>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  if (loading) {
    return (
      <div className={styles.visitTrackingContainer}>
        <Card className={styles.headerCard}>
          <div className={styles.loadingContainer}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              <Text>Loading visit tracking data...</Text>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className={styles.visitTrackingContainer}>
      <AuthDebugger />
      <Card className={styles.headerCard}>
        <div style={{ marginBottom: 24 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={3} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: 8 }}>
              <DashboardOutlined style={{ color: '#1890ff' }} />
              Visit Tracking Dashboard
            </Title>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={fetchData}
              loading={loading}
              size="large"
              style={{ borderRadius: 8, marginRight: 8 }}
            >
              Refresh Data
            </Button>
            <Button
              icon={<EyeOutlined />}
              onClick={() => {
                console.log('🔍 Debug Authentication State:');
                console.log('  - User prop:', userProp);
                console.log('  - Auth store user:', authUser);
                console.log('  - Final user:', user);
                console.log('  - Is Authenticated:', isAuthenticated);
                console.log('  - User ID:', user?.id);
                console.log('  - User Full Name:', user?.full_name);
                message.info('Check console for authentication debug info');
              }}
              size="large"
              style={{ borderRadius: 8 }}
            >
              Debug Auth
            </Button>
          </div>

          {/* Statistics Cards */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card className={styles.statsCard}>
                <Statistic
                  title="Total Visits"
                  value={filteredVisits.length}
                  prefix="📈"
                  valueStyle={{ color: '#1890ff', fontWeight: 'bold' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card className={styles.statsCard}>
                <Statistic
                  title="Completed"
                  value={filteredVisits.filter(v => v.status === 'completed').length}
                  prefix="✅"
                  valueStyle={{ color: '#52c41a', fontWeight: 'bold' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card className={styles.statsCard}>
                <Statistic
                  title="In Progress"
                  value={filteredVisits.filter(v => v.status === 'in_progress').length}
                  prefix="🔄"
                  valueStyle={{ color: '#faad14', fontWeight: 'bold' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card className={styles.statsCard}>
                <Statistic
                  title="Planned"
                  value={filteredVisits.filter(v => v.status === 'planned').length}
                  prefix="📅"
                  valueStyle={{ color: '#722ed1', fontWeight: 'bold' }}
                />
              </Card>
            </Col>
          </Row>

          {/* Filters */}
          <Card title="🔍 Filters & Search" size="small" style={{ marginBottom: 24 }} className={styles.filterCard}>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8} md={6}>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>Status Filter:</Text>
                  <Select
                    value={filter}
                    onChange={(value) => setFilter(value)}
                    style={{ width: '100%', marginTop: 4 }}
                    placeholder="Select status"
                    size="large"
                  >
                    <Option value="all">All Statuses</Option>
                    <Option value="planned">📅 Planned</Option>
                    <Option value="in_progress">🔄 In Progress</Option>
                    <Option value="completed">✅ Completed</Option>
                    <Option value="cancelled">❌ Cancelled</Option>
                  </Select>
                </div>
              </Col>
              <Col xs={24} sm={8} md={6}>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>Team Member:</Text>
                  <Select
                    value={selectedUser}
                    onChange={(value) => setSelectedUser(value)}
                    style={{ width: '100%', marginTop: 4 }}
                    placeholder="All team members"
                    allowClear
                    size="large"
                  >
                    {team.map((member) => (
                      <Option key={member.id} value={member.id}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                          <Avatar size="small" icon={<UserOutlined />} />
                          {member.full_name}
                        </div>
                      </Option>
                    ))}
                  </Select>
                </div>
              </Col>
              <Col xs={24} sm={8} md={6}>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>Search Customer:</Text>
                  <Input
                    placeholder="Search by customer name"
                    prefix={<SearchOutlined />}
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    style={{ marginTop: 4 }}
                    allowClear
                    size="large"
                  />
                </div>
              </Col>
            </Row>
          </Card>

          {/* Visits Table */}
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                📋 Team Visits ({filteredVisits.length} total)
              </div>
            } 
            className={styles.tableCard}
          >
            {filteredVisits.length === 0 ? (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  <span>
                    No visits found matching the current filters.
                    <br />
                    <Text type="secondary">Try adjusting your filter criteria.</Text>
                  </span>
                }
                style={{ padding: '40px 0' }}
              />
            ) : (
              <Table
                columns={columns}
                dataSource={filteredVisits}
                rowKey="id"
                scroll={{ x: true }}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) =>
                    `${range[0]}-${range[1]} of ${total} visits`,
                  pageSizeOptions: ['5', '10', '20', '50'],
                }}
                rowClassName={(record) => {
                  if (record.status === 'completed') return 'ant-table-row-success';
                  if (record.status === 'in_progress') return 'ant-table-row-processing';
                  if (record.status === 'cancelled') return 'ant-table-row-error';
                  return '';
                }}
                size="middle"
              />
            )}
          </Card>

          {/* Data Verification Section */}
          {visits.length > 0 && (
            <Card 
              title="🔍 Data Verification" 
              size="small" 
              style={{ marginTop: 16, background: '#fafafa' }}
            >
              <Row gutter={[16, 8]}>
                <Col span={24}>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    Data completeness check for retrieved records:
                  </Text>
                </Col>
                <Col xs={24} sm={8}>
                  <div style={{ fontSize: 12 }}>
                    <Text strong>Total Visits:</Text> {visits.length}
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div style={{ fontSize: 12 }}>
                    <Text strong>With Customer Data:</Text> {visits.filter(v => v.customer).length}
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div style={{ fontSize: 12 }}>
                    <Text strong>With User Data:</Text> {visits.filter(v => v.user).length}
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div style={{ fontSize: 12 }}>
                    <Text strong>Team Members:</Text> {team.length}
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div style={{ fontSize: 12 }}>
                    <Text strong>Last Updated:</Text> {new Date().toLocaleTimeString()}
                  </div>
                </Col>
                <Col xs={24} sm={8}>
                  <div style={{ fontSize: 12 }}>
                    <Text strong>Database Status:</Text> 
                    <Badge status="success" text="Connected" style={{ marginLeft: 4 }} />
                  </div>
                </Col>
              </Row>
            </Card>
          )}
        </div>
      </Card>
    </div>
  );
}
