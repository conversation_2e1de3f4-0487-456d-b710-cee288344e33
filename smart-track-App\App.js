import React from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {PaperProvider} from 'react-native-paper';
import {QueryClient, QueryClientProvider} from '@tanstack/react-query';
import {View, ActivityIndicator} from 'react-native';
import {StatusBar} from 'expo-status-bar';
import {SafeAreaProvider} from 'react-native-safe-area-context';

import AuthNavigator from './src/navigation/AuthNavigator.js';
import MainNavigator from './src/navigation/MainNavigator.js';
import {useAuthStore} from './src/stores/authStore.js';
import theme from './src/theme/theme.js';

const queryClient = new QueryClient();

export default function App() {
  const {isAuthenticated, _hasHydrated, setHasHydrated} = useAuthStore();

  // Fallback hydration check
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (!_hasHydrated) {
        console.log('⚠️ Forcing hydration after timeout');
        setHasHydrated(true);
      }
    }, 3000); // 3 second timeout

    return () => clearTimeout(timer);
  }, [_hasHydrated, setHasHydrated]);

  // Debug logging
  React.useEffect(() => {
    console.log('🚀 App state:', { isAuthenticated, _hasHydrated });
  }, [isAuthenticated, _hasHydrated]);

  // Show loading screen while hydrating
  if (!_hasHydrated) {
    console.log('⏳ Waiting for hydration...');
    return (
      <SafeAreaProvider>
        <PaperProvider theme={theme}>
          <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#000'}}>
            <ActivityIndicator size="large" color="#007AFF" />
          </View>
        </PaperProvider>
      </SafeAreaProvider>
    );
  }

  console.log(`🧭 Navigation: ${isAuthenticated ? 'MainNavigator' : 'AuthNavigator'}`);

  return (
    <SafeAreaProvider>
      <QueryClientProvider client={queryClient}>
        <PaperProvider theme={theme}>
          <NavigationContainer>
            <StatusBar style="light" />
            {isAuthenticated ? <MainNavigator /> : <AuthNavigator />}
          </NavigationContainer>
        </PaperProvider>
      </QueryClientProvider>
    </SafeAreaProvider>
  );
}
