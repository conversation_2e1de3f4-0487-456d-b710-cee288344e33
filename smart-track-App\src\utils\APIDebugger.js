/**
 * Debug utility for testing API connections in the mobile app
 * This can be imported and used in any screen for debugging
 */

import { api } from '../services/api';

export class APIDebugger {
  static async runFullDiagnostic() {
    console.log('🔍 === STARTING FULL API DIAGNOSTIC ===');
    
    const results = {};
    
    try {
      // Test 1: Basic connectivity
      console.log('🔍 Test 1: Basic backend connectivity...');
      const connectivityTest = await api.testConnection();
      results.connectivity = connectivityTest;
      console.log('🔍 Connectivity result:', connectivityTest.success ? '✅ PASS' : '❌ FAIL');
      
      // Test 2: Authentication status
      console.log('🔍 Test 2: Authentication status...');
      const tokens = await api.getStoredTokens();
      results.authentication = {
        hasAccessToken: !!tokens.accessToken,
        hasRefreshToken: !!tokens.refreshToken,
        accessTokenLength: tokens.accessToken?.length || 0
      };
      console.log('🔍 Auth result:', results.authentication.hasAccessToken ? '✅ PASS' : '❌ FAIL');
      
      // Test 3: Dashboard API
      console.log('🔍 Test 3: Dashboard API test...');
      const dashboardTest = await api.testDashboardAPI();
      results.dashboard = dashboardTest;
      console.log('🔍 Dashboard result:', dashboardTest.success ? '✅ PASS' : '❌ FAIL');
      
      // Test 4: User profile
      console.log('🔍 Test 4: User profile access...');
      try {
        const userProfile = await api.getCurrentUser();
        results.userProfile = { success: true, hasUser: !!userProfile };
        console.log('🔍 User profile result: ✅ PASS');
      } catch (error) {
        results.userProfile = { 
          success: false, 
          error: error.message,
          status: error.response?.status 
        };
        console.log('🔍 User profile result: ❌ FAIL');
      }
      
    } catch (error) {
      console.error('🔍 Diagnostic failed:', error);
      results.error = error.message;
    }
    
    console.log('🔍 === DIAGNOSTIC COMPLETE ===');
    console.log('🔍 Full results:', JSON.stringify(results, null, 2));
    
    return results;
  }
  
  static async testDashboardOnly() {
    console.log('📊 === DASHBOARD-ONLY TEST ===');
    
    try {
      // Get current tokens
      const tokens = await api.getStoredTokens();
      console.log('📊 Token status:', {
        hasAccess: !!tokens.accessToken,
        hasRefresh: !!tokens.refreshToken,
        accessLength: tokens.accessToken?.length || 0
      });
      
      if (!tokens.accessToken) {
        console.log('📊 ❌ No access token found');
        return { success: false, error: 'No access token' };
      }
      
      // Test dashboard directly
      console.log('📊 Testing dashboard endpoint...');
      const result = await api.testDashboardAPI();
      
      console.log('📊 Dashboard test complete:', result.success ? '✅ PASS' : '❌ FAIL');
      
      if (!result.success) {
        console.log('📊 Error details:', {
          status: result.status,
          error: result.error,
          details: result.details
        });
      }
      
      return result;
      
    } catch (error) {
      console.error('📊 Dashboard test failed:', error);
      return { success: false, error: error.message };
    }
  }
  
  static async forceTokenRefresh() {
    console.log('🔄 === FORCING TOKEN REFRESH ===');
    
    try {
      const tokens = await api.getStoredTokens();
      
      if (!tokens.refreshToken) {
        console.log('🔄 ❌ No refresh token found');
        return { success: false, error: 'No refresh token' };
      }
      
      console.log('🔄 Attempting token refresh...');
      const newTokens = await api.refreshToken(tokens.refreshToken);
      
      console.log('🔄 Token refresh successful');
      return { success: true, data: newTokens };
      
    } catch (error) {
      console.error('🔄 Token refresh failed:', error);
      return { success: false, error: error.message };
    }
  }
}

export default APIDebugger;
