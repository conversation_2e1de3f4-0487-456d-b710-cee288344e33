'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  Card, 
  Table, 
  Button, 
  Input, 
  Space, 
  Typography, 
  Avatar, 
  Tag, 
  Badge, 
  Tooltip, 
  Popconfirm, 
  message, 
  Spin, 
  Empty, 
  Row, 
  Col, 
  Divider,
  Select,
  DatePicker
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ReloadOutlined, 
  CalendarOutlined, 
  SearchOutlined,
  UserOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

import { apiService } from '../../services/api';
import { Visit, Customer } from '../../types';
import AddVisitModal from './AddVisitModal';
import EditVisitModal from './EditVisitModal';
import DeleteVisitModal from './DeleteVisitModal';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

export default function VisitManagement() {
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedVisit, setSelectedVisit] = useState<Visit | null>(null);

  const handleOpenModal = useCallback(() => {
    console.log('🎯 Add New Visit button clicked');
    setIsAddModalOpen(true);
  }, []);

  // Get visits data from API
  const { data: visitsData, isLoading, refetch } = useQuery({
    queryKey: ['visits', page, statusFilter, searchTerm],
    queryFn: () => apiService.getVisits(page, 10, statusFilter || undefined, searchTerm || undefined),
    staleTime: 0,
    gcTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  // Backend returns array directly
  const visits = visitsData?.data || [];
  console.log('📊 Raw visits data received:', visitsData);
  console.log('📊 Extracted visits array:', visits);
  console.log('📊 Number of visits:', visits.length);
  console.log('📊 Current page:', page);
  
  // Track when visit data changes
  useEffect(() => {
    if (visits.length > 0) {
      console.log('📊 Visit data updated:', visits.map(v => ({ 
        id: v.id, 
        customer: v.customer?.name || 'Unknown', 
        status: v.status,
        planned_date: v.planned_date
      })));
    }
  }, [visits]);
  
  const pageSize = 10;
  const hasNextPage = visits.length === pageSize;
  const hasPrevPage = page > 1;

  const handleVisitCreated = useCallback(() => {
    console.log('🎉 Visit creation success callback triggered');
    console.log('🔄 Forcing page reset to 1 and refetch');
    setPage(1);
    setTimeout(() => {
      refetch();
    }, 100);
    message.success('Visit created successfully!');
  }, [refetch]);

  const handleCloseModal = useCallback(() => {
    console.log('🔒 Closing Add Visit modal');
    setIsAddModalOpen(false);
    setPage(1);
    refetch();
  }, [refetch]);

  const handleEditVisit = useCallback((visit: Visit) => {
    setSelectedVisit(visit);
    setIsEditModalOpen(true);
  }, []);

  const handleCloseEditModal = useCallback(async () => {
    console.log('🔄 Closing edit modal and refetching data...');
    setIsEditModalOpen(false);
    setSelectedVisit(null);
    
    const result = await refetch();
    console.log('🔄 Refetch completed after edit modal close:', result.data?.data?.length, 'visits loaded');
  }, [refetch]);

  const handleDeleteVisit = useCallback((visit: Visit) => {
    setSelectedVisit(visit);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(false);
    setSelectedVisit(null);
    refetch();
    message.success('Visit deleted successfully!');
  }, [refetch]);

  const handleRefresh = useCallback(async () => {
    console.log('🔄 Manual refresh triggered');
    console.log('🕐 Current time:', new Date().toISOString());
    
    try {
      queryClient.removeQueries({ 
        queryKey: ['visits'],
        exact: false 
      });
      
      const result = await refetch();
      console.log('🔄 Refetch result:', result);
      
      message.success('Data refreshed!');
    } catch (error) {
      console.error('❌ Refresh error:', error);
      message.error('Failed to refresh data');
    }
  }, [refetch, queryClient]);

  const handleTestAPI = useCallback(async () => {
    console.log('🧪 Testing API directly...');
    try {
      const result = await apiService.getVisits(1, 10);
      console.log('🧪 Direct API result:', result);
      console.log('🧪 Number of visits from API:', result.data.length);
      message.info(`API returned ${result.data.length} visits`);
    } catch (error) {
      console.error('❌ Direct API test failed:', error);
      message.error('Direct API test failed');
    }
  }, []);

  // Use visits directly from backend (filtering is done server-side)
  const filteredVisits = visits;

  // Get status color and text
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'planned':
        return { color: 'blue', text: 'Planned' };
      case 'in_progress':
        return { color: 'orange', text: 'In Progress' };
      case 'completed':
        return { color: 'green', text: 'Completed' };
      case 'cancelled':
        return { color: 'red', text: 'Cancelled' };
      default:
        return { color: 'default', text: status };
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return dayjs(dateString).format('MMM DD, YYYY HH:mm');
  };

  // Define table columns
  const columns = [
    {
      title: 'Visit Details',
      key: 'details',
      render: (_: any, visit: Visit) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            size={40} 
            style={{ backgroundColor: '#1890ff', marginRight: 12 }}
            icon={<CalendarOutlined />}
          >
            {visit.id}
          </Avatar>
          <div>
            <div style={{ fontWeight: 500, marginBottom: 2 }}>
              {visit.customer?.name || 'Unknown Customer'}
            </div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              ID: {visit.id} | Customer ID: {visit.customer_id}
            </div>
            <div style={{ color: '#999', fontSize: '11px' }}>
              {visit.purpose || 'No purpose specified'}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Schedule',
      key: 'schedule',
      render: (_: any, visit: Visit) => (
        <div>
          <div style={{ marginBottom: 2, display: 'flex', alignItems: 'center' }}>
            <ClockCircleOutlined style={{ marginRight: 4, color: '#666' }} />
            {formatDate(visit.planned_date)}
          </div>
          {visit.actual_start_time && (
            <div style={{ color: '#666', fontSize: '12px' }}>
              Started: {formatDate(visit.actual_start_time)}
            </div>
          )}
          {visit.actual_end_time && (
            <div style={{ color: '#666', fontSize: '12px' }}>
              Ended: {formatDate(visit.actual_end_time)}
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Customer Info',
      key: 'customer',
      render: (_: any, visit: Visit) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 2 }}>
            <UserOutlined style={{ marginRight: 4, color: '#666' }} />
            {visit.customer?.name || 'Unknown'}
          </div>
          <div style={{ color: '#666', fontSize: '12px', display: 'flex', alignItems: 'center' }}>
            <EnvironmentOutlined style={{ marginRight: 4 }} />
            {visit.customer?.city || 'No location'}
          </div>
          {visit.customer?.phone && (
            <div style={{ color: '#666', fontSize: '12px' }}>
              {visit.customer.phone}
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const config = getStatusConfig(status);
        return (
          <Tag color={config.color}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: 'Outcome',
      key: 'outcome',
      render: (_: any, visit: Visit) => (
        <div>
          {visit.outcome && (
            <div style={{ marginBottom: 4 }}>
              <Badge status="success" text="Outcome Recorded" />
            </div>
          )}
          {visit.next_action && (
            <div style={{ color: '#666', fontSize: '12px' }}>
              Next: {visit.next_action.substring(0, 30)}...
            </div>
          )}
          {!visit.outcome && !visit.next_action && (
            <span style={{ color: '#999', fontSize: '12px' }}>No outcome recorded</span>
          )}
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, visit: Visit) => (
        <Space size="small">
          <Tooltip title="Edit visit">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEditVisit(visit)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="Delete visit">
            <Popconfirm
              title="Are you sure you want to delete this visit?"
              description="This action cannot be undone."
              onConfirm={() => handleDeleteVisit(visit)}
              okText="Yes, Delete"
              cancelText="Cancel"
              okButtonProps={{ danger: true }}
            >
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  if (isLoading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '60px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16, color: '#666' }}>Loading visits...</div>
        </div>
      </Card>
    );
  }

  return (
    <div>
      <Card 
        title={
          <Title level={3} style={{ margin: 0 }}>
            Visit Management
          </Title>
        }
        extra={
          <Space>
            <Tooltip title="Test API directly">
              <Button 
                onClick={handleTestAPI}
                type="default"
              >
                Test API
              </Button>
            </Tooltip>
            <Tooltip title="Refresh data">
              <Button 
                icon={<ReloadOutlined />} 
                onClick={handleRefresh}
                loading={isLoading}
              >
                Refresh
              </Button>
            </Tooltip>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleOpenModal}
            >
              Add New Visit
            </Button>
          </Space>
        }
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* Search and Filters Section */}
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="Search visits..."
                allowClear
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                prefix={<SearchOutlined />}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Select
                placeholder="Filter by status"
                allowClear
                value={statusFilter || undefined}
                onChange={(value) => setStatusFilter(value || '')}
                style={{ width: '100%' }}
              >
                <Option value="planned">Planned</Option>
                <Option value="in_progress">In Progress</Option>
                <Option value="completed">Completed</Option>
                <Option value="cancelled">Cancelled</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={10}>
              <div style={{ textAlign: 'right', color: '#666', fontSize: '14px' }}>
                {isLoading ? (
                  'Loading visits...'
                ) : (
                  `Showing ${visits.length} visit(s) on page ${page}`
                )}
              </div>
            </Col>
          </Row>

          <Divider style={{ margin: '12px 0' }} />

          {/* Table Section */}
          {filteredVisits.length === 0 ? (
            <Empty
              description={
                searchTerm 
                  ? `No visits found matching "${searchTerm}"` 
                  : statusFilter
                  ? `No visits found with status "${statusFilter}"`
                  : "No visits found"
              }
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              {!searchTerm && !statusFilter && (
                <Button type="primary" icon={<PlusOutlined />} onClick={handleOpenModal}>
                  Add First Visit
                </Button>
              )}
            </Empty>
          ) : (
            <>
              <Table
                columns={columns}
                dataSource={filteredVisits}
                rowKey="id"
                pagination={false}
                scroll={{ x: 1000 }}
                size="middle"
              />

              {/* Custom Pagination */}
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center', 
                marginTop: 16 
              }}>
                <div style={{ color: '#666', fontSize: '14px' }}>
                  Showing {((page - 1) * pageSize) + 1} to{' '}
                  {Math.min(page * pageSize, ((page - 1) * pageSize) + filteredVisits.length)} of{' '}
                  {searchTerm || statusFilter ? `${filteredVisits.length} filtered` : 'many'} visits
                </div>
                <Space>
                  <Button 
                    disabled={!hasPrevPage} 
                    onClick={() => setPage(Math.max(1, page - 1))}
                  >
                    Previous
                  </Button>
                  <span style={{ 
                    padding: '0 16px', 
                    color: '#1890ff', 
                    fontWeight: 500 
                  }}>
                    Page {page}
                  </span>
                  <Button 
                    disabled={!hasNextPage} 
                    onClick={() => setPage(page + 1)}
                  >
                    Next
                  </Button>
                </Space>
              </div>
            </>
          )}
        </Space>
      </Card>

      {/* Modals */}
      <AddVisitModal 
        isOpen={isAddModalOpen} 
        onClose={handleCloseModal}
        onSuccess={handleVisitCreated}
      />
      
      <EditVisitModal 
        isOpen={isEditModalOpen} 
        onClose={handleCloseEditModal}
        visit={selectedVisit}
      />
      
      <DeleteVisitModal 
        isOpen={isDeleteModalOpen} 
        onClose={handleCloseDeleteModal}
        visit={selectedVisit}
      />
    </div>
  );
}
