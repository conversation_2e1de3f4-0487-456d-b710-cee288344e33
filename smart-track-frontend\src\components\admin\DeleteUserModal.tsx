'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiService } from '@/services/api';
import { User } from '@/types';
import {
  Modal,
  Button,
  Typography,
  Space,
  Alert,
  message,
} from 'antd';
import {
  DeleteOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface DeleteUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
}

export default function DeleteUserModal({ isOpen, onClose, user }: DeleteUserModalProps) {
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);

  const deleteUserMutation = useMutation({
    mutationFn: (userId: number) => {
      console.log('🗑️ DeleteUser mutation called for userId:', userId);
      return apiService.deleteUser(userId);
    },
    onSuccess: () => {
      console.log('✅ User deleted successfully');
      queryClient.invalidateQueries({ 
        queryKey: ['users'],
        exact: false 
      });
      queryClient.refetchQueries({ 
        queryKey: ['users'],
        exact: false 
      });
      message.success('User deleted successfully!');
      handleClose();
    },
    onError: (error: any) => {
      console.error('❌ User deletion failed:', error);
      if (error.response?.data?.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error('Failed to delete user');
      }
      setLoading(false);
    },
  });

  const handleClose = () => {
    setLoading(false);
    onClose();
  };

  const handleDelete = () => {
    if (user) {
      setLoading(true);
      deleteUserMutation.mutate(user.id);
    }
  };

  return (
    <Modal
      title={
        <Space>
          <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
          <Title level={4} style={{ margin: 0 }}>Delete User</Title>
        </Space>
      }
      open={isOpen}
      onCancel={handleClose}
      footer={[
        <Button key="cancel" onClick={handleClose} size="large">
          Cancel
        </Button>,
        <Button
          key="delete"
          type="primary"
          danger
          loading={loading}
          onClick={handleDelete}
          icon={<DeleteOutlined />}
          size="large"
        >
          {loading ? 'Deleting...' : 'Delete User'}
        </Button>,
      ]}
      width={500}
      centered
    >
      <div style={{ padding: '24px 0' }}>
        <Alert
          message="Warning"
          description="This action cannot be undone. The user will be permanently removed from the system."
          type="warning"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        {user && (
          <div style={{ 
            padding: '16px', 
            backgroundColor: '#f5f5f5', 
            borderRadius: '8px',
            border: '1px solid #d9d9d9'
          }}>
            <Text strong>User to be deleted:</Text>
            <div style={{ marginTop: '8px' }}>
              <Space direction="vertical" size={4}>
                <div>
                  <UserOutlined style={{ marginRight: '8px', color: '#666' }} />
                  <Text strong>{user.full_name}</Text>
                </div>
                <div style={{ paddingLeft: '24px' }}>
                  <Text type="secondary">{user.email}</Text>
                </div>
                <div style={{ paddingLeft: '24px' }}>
                  <Text type="secondary">@{user.username}</Text>
                </div>
                <div style={{ paddingLeft: '24px' }}>
                  <Text type="secondary">
                    Role: {user.role?.name || user.profile?.role_type || 'No Role'}
                  </Text>
                </div>
              </Space>
            </div>
          </div>
        )}

        <div style={{ marginTop: '16px' }}>
          <Text>
            Are you sure you want to delete this user? This will remove all associated data and access permissions.
          </Text>
        </div>
      </div>
    </Modal>
  );
}
