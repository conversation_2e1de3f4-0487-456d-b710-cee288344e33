from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class VisitStatus(str, Enum):
    PLANNED = "planned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class PhotoType(str, Enum):
    GENERAL = "general"
    PRODUCT_DISPLAY = "product_display"
    COMPETITOR = "competitor"
    ISSUE = "issue"

class NoteType(str, Enum):
    GENERAL = "general"
    ISSUE = "issue"
    OPPORTUNITY = "opportunity"
    FEEDBACK = "feedback"

class Priority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

# Visit Schemas
class VisitBase(BaseModel):
    customer_id: int
    planned_date: datetime
    planned_time: Optional[str] = None  # Time in HH:MM format
    purpose: Optional[str] = None
    notes: Optional[str] = None

class VisitCreate(VisitBase):
    pass

class VisitAdminCreate(VisitBase):
    user_id: int
    status: Optional[VisitStatus] = VisitStatus.PLANNED

class VisitUpdate(BaseModel):
    planned_date: Optional[datetime] = None
    planned_time: Optional[str] = None
    purpose: Optional[str] = None
    notes: Optional[str] = None
    outcome: Optional[str] = None
    next_action: Optional[str] = None
    status: Optional[VisitStatus] = None

class VisitCheckIn(BaseModel):
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)

class VisitCheckOut(BaseModel):
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    outcome: Optional[str] = None
    next_action: Optional[str] = None

# Visit Photo Schemas
class VisitPhotoBase(BaseModel):
    photo_type: PhotoType = PhotoType.GENERAL
    caption: Optional[str] = None
    latitude: Optional[float] = Field(None, ge=-90, le=90)
    longitude: Optional[float] = Field(None, ge=-180, le=180)

class VisitPhotoCreate(VisitPhotoBase):
    pass

class VisitPhoto(VisitPhotoBase):
    id: int
    visit_id: int
    photo_url: str
    timestamp: datetime

    class Config:
        from_attributes = True

# Visit Note Schemas
class VisitNoteBase(BaseModel):
    note_type: NoteType = NoteType.GENERAL
    title: str = Field(..., max_length=100)
    content: str
    priority: Priority = Priority.MEDIUM

class VisitNoteCreate(VisitNoteBase):
    pass

class VisitNoteUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=100)
    content: Optional[str] = None
    priority: Optional[Priority] = None
    is_resolved: Optional[bool] = None

class VisitNote(VisitNoteBase):
    id: int
    visit_id: int
    is_resolved: bool
    created_at: datetime

    class Config:
        from_attributes = True

# Visit Response Schemas
class Visit(VisitBase):
    id: int
    user_id: int
    status: VisitStatus
    actual_start_time: Optional[datetime] = None
    actual_end_time: Optional[datetime] = None
    checkin_latitude: Optional[float] = None
    checkin_longitude: Optional[float] = None
    checkout_latitude: Optional[float] = None
    checkout_longitude: Optional[float] = None
    outcome: Optional[str] = None
    next_action: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    customer: Optional[dict] = None  # Customer information will be populated from join

    class Config:
        from_attributes = True

class VisitDetail(Visit):
    photos: List[VisitPhoto] = []
    notes_records: List[VisitNote] = []
    customer: Optional[dict] = None  # Will be populated from customer relationship

    class Config:
        from_attributes = True

class VisitStatistics(BaseModel):
    total_visits: int
    completed_visits: int
    planned_visits: int
    in_progress_visits: int
    completion_rate: float
    avg_visit_duration: Optional[float] = None  # in minutes

# Location Validation Schemas
class LocationValidation(BaseModel):
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    customer_id: int

class LocationValidationResponse(BaseModel):
    is_valid: bool
    distance_meters: float
    customer_latitude: float
    customer_longitude: float
    message: str

class NearbyCustomer(BaseModel):
    customer_id: int
    customer_name: str
    distance_meters: float
    latitude: float
    longitude: float

class NearbyCustomersResponse(BaseModel):
    customers: List[NearbyCustomer]
    total_count: int


