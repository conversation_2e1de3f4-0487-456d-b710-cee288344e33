"""
Test security and authentication functions
"""
import pytest
from app.core.security import (
    create_access_token, 
    verify_password, 
    get_password_hash,
    decode_access_token
)
from datetime import timedel<PERSON>

def test_password_hashing():
    """Test password hashing and verification"""
    password = "testpassword123"
    hashed = get_password_hash(password)
    
    # Verify the hash is different from the original password
    assert hashed != password
    
    # Verify password verification works
    assert verify_password(password, hashed) is True
    
    # Verify wrong password fails
    assert verify_password("wrongpassword", hashed) is False

def test_access_token_creation():
    """Test JWT access token creation"""
    data = {"sub": "testuser", "user_id": 1}
    token = create_access_token(data=data)
    
    # Token should be a string
    assert isinstance(token, str)
    # Token should not be empty
    assert len(token) > 0
    # Token should contain dots (JWT format)
    assert "." in token

def test_access_token_with_expiry():
    """Test JWT access token with custom expiry"""
    data = {"sub": "testuser", "user_id": 1}
    expires_delta = timedelta(minutes=30)
    token = create_access_token(data=data, expires_delta=expires_delta)
    
    assert isinstance(token, str)
    assert len(token) > 0

def test_token_decode():
    """Test JWT token decoding"""
    data = {"sub": "testuser", "user_id": 1}
    token = create_access_token(data=data)
    
    # Decode the token
    decoded = decode_access_token(token)
    
    # Verify the data is correct
    assert decoded is not None
    assert decoded.get("sub") == "testuser"
    assert decoded.get("user_id") == 1

def test_invalid_token_decode():
    """Test decoding invalid token"""
    invalid_token = "invalid.token.here"
    decoded = decode_access_token(invalid_token)
    
    # Should return None for invalid token
    assert decoded is None

def test_password_hash_uniqueness():
    """Test that same password produces different hashes"""
    password = "samepassword"
    hash1 = get_password_hash(password)
    hash2 = get_password_hash(password)
    
    # Hashes should be different due to salt
    assert hash1 != hash2
    
    # But both should verify correctly
    assert verify_password(password, hash1) is True
    assert verify_password(password, hash2) is True
