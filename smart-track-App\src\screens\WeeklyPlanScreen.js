import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import {
  Text,
  Card,
  Title,
  Button,
  Chip,
  ProgressBar,
  Searchbar,
  FAB,
  Modal,
  Portal,
  TextInput,
  Surface,
  Badge,
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { format, startOfWeek, addDays, isSameDay, parseISO, getDay, startOfMonth, endOfMonth, eachDayOfInterval } from 'date-fns';
import { visitService } from '../services/visitService';

const { width } = Dimensions.get('window');

const WeeklyPlanScreen = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('All');
  const [showAddModal, setShowAddModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [viewMode, setViewMode] = useState('calendar'); // 'calendar' or 'list'
  const [selectedDate, setSelectedDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [currentWeek, setCurrentWeek] = useState(startOfWeek(new Date(), { weekStartsOn: 1 })); // Monday start
  const [selectedDayVisits, setSelectedDayVisits] = useState([]);
  const [loadingDayVisits, setLoadingDayVisits] = useState(false);
  const [newVisit, setNewVisit] = useState({
    customer: '',
    region: '',
    day: '',
    time: '',
  });

  const [weeklyPlan, setWeeklyPlan] = useState({
    Monday: [],
    Tuesday: [],
    Wednesday: [],
    Thursday: [],
    Friday: [],
    Saturday: [],
    Sunday: [],
  });

  // Get regions dynamically from the actual weekly plan data
  const getRegions = () => {
    if (!weeklyPlan) return ['All'];
    
    const regions = Object.values(weeklyPlan)
      .flat()
      .map(visit => visit.region)
      .filter(region => region && region.trim() !== '')
      .filter((region, index, array) => array.indexOf(region) === index); // Remove duplicates
    
    return ['All', ...regions.sort()];
  };

  const regions = getRegions();
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  // Calendar helper functions
  const getDayDate = (dayName) => {
    const dayIndex = days.indexOf(dayName);
    const weekStart = startOfWeek(new Date(), { weekStartsOn: 1 });
    return format(addDays(weekStart, dayIndex), 'yyyy-MM-dd');
  };

  const getDayNameFromDate = (dateString) => {
    const date = parseISO(dateString);
    const weekStart = startOfWeek(new Date(), { weekStartsOn: 1 });
    
    for (let i = 0; i < 7; i++) {
      if (isSameDay(addDays(weekStart, i), date)) {
        return days[i];
      }
    }
    return 'Monday'; // fallback
  };

  const getVisitsForDate = (dateString) => {
    const dayName = getDayNameFromDate(dateString);
    return weeklyPlan[dayName] || [];
  };

  // Custom Calendar Component - Weekly View
  const renderCustomCalendar = () => {
    const weekStart = currentWeek;
    const weekEnd = addDays(weekStart, 6);
    const weekDays = [];
    
    // Generate the 7 days of the current week
    for (let i = 0; i < 7; i++) {
      weekDays.push(addDays(weekStart, i));
    }
    
    const weekDayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const today = new Date();
    
    const navigateWeek = (direction) => {
      const newWeek = addDays(currentWeek, direction * 7);
      setCurrentWeek(newWeek);
      // Optionally reload data for the new week
      loadWeeklyPlan();
    };
    
    const renderCalendarDay = (day, index) => {
      const dayStr = format(day, 'yyyy-MM-dd');
      const isToday = isSameDay(day, today);
      const isSelected = dayStr === selectedDate;
      const dayVisits = getVisitsForDate(dayStr);
      const hasVisits = dayVisits.length > 0;
      const completedCount = dayVisits.filter(v => v.status === 'completed').length;
      const pendingCount = dayVisits.filter(v => v.status === 'pending').length;
      const cancelledCount = dayVisits.filter(v => v.status === 'cancelled').length;
      
      const handleDayPress = () => {
        setSelectedDate(dayStr);
        loadVisitsForDay(dayStr);
      };
      
      // Determine day background color based on visit status
      let dayBackgroundColor = 'transparent';
      if (hasVisits) {
        if (completedCount === dayVisits.length) {
          dayBackgroundColor = '#E8F5E8'; // Light green for all completed
        } else if (pendingCount > 0) {
          dayBackgroundColor = '#FFF3E0'; // Light orange for pending visits
        } else if (cancelledCount > 0) {
          dayBackgroundColor = '#FFEBEE'; // Light red for cancelled
        }
      }
      
      return (
        <TouchableOpacity
          key={dayStr}
          style={[
            styles.weekCalendarDay,
            { backgroundColor: isSelected ? '#2196F3' : dayBackgroundColor },
            isToday && !isSelected && styles.todayDay,
          ]}
          onPress={handleDayPress}
        >
          <Text style={styles.weekDayName}>{weekDayNames[index]}</Text>
          <Text style={[
            styles.weekCalendarDayText,
            isSelected && styles.selectedDayText,
            isToday && !isSelected && styles.todayDayText,
          ]}>
            {format(day, 'd')}
          </Text>
          
          {hasVisits && (
            <View style={styles.weekVisitIndicators}>
              <View style={styles.statusIndicatorRow}>
                {completedCount > 0 && (
                  <View style={styles.statusIndicator}>
                    <View style={styles.completedDot} />
                    <Text style={[styles.statusCount, isSelected && styles.selectedStatusCount]}>
                      {completedCount}
                    </Text>
                  </View>
                )}
                {pendingCount > 0 && (
                  <View style={styles.statusIndicator}>
                    <View style={styles.pendingDot} />
                    <Text style={[styles.statusCount, isSelected && styles.selectedStatusCount]}>
                      {pendingCount}
                    </Text>
                  </View>
                )}
                {cancelledCount > 0 && (
                  <View style={styles.statusIndicator}>
                    <View style={styles.cancelledDot} />
                    <Text style={[styles.statusCount, isSelected && styles.selectedStatusCount]}>
                      {cancelledCount}
                    </Text>
                  </View>
                )}
              </View>
              <Text style={[styles.totalVisitCount, isSelected && styles.selectedStatusCount]}>
                Total: {dayVisits.length}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      );
    };
    
    return (
      <Surface style={styles.calendarContainer}>
        <View style={styles.calendarHeader}>
          <TouchableOpacity 
            style={styles.calendarNavButton}
            onPress={() => navigateWeek(-1)}
          >
            <Ionicons name="chevron-back" size={24} color="#2196F3" />
          </TouchableOpacity>
          
          <Text style={styles.calendarWeekTitle}>
            {format(weekStart, 'MMM d')} - {format(weekEnd, 'MMM d, yyyy')}
          </Text>
          
          <TouchableOpacity 
            style={styles.calendarNavButton}
            onPress={() => navigateWeek(1)}
          >
            <Ionicons name="chevron-forward" size={24} color="#2196F3" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.weekCalendarGrid}>
          {weekDays.map(renderCalendarDay)}
        </View>
      </Surface>
    );
  };

  useEffect(() => {
    loadWeeklyPlan();
    // Load visits for today when component mounts
    loadVisitsForDay(selectedDate);
  }, []);

  // Update visits when selected date changes
  useEffect(() => {
    if (viewMode === 'calendar') {
      loadVisitsForDay(selectedDate);
    }
  }, [selectedDate, viewMode]);

  // Update calendar when current week changes
  useEffect(() => {
    // This effect can be used for additional calendar updates if needed
  }, [weeklyPlan, currentWeek]);

  const loadWeeklyPlan = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get current week's visits
      const today = new Date();
      const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 1)); // Monday
      const endOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 7)); // Sunday

      const params = {
        start_date: startOfWeek.toISOString().split('T')[0],
        end_date: endOfWeek.toISOString().split('T')[0],
      };

      const response = await visitService.getVisits(params);
      
      if (response && Array.isArray(response)) {
        // Group visits by day of week
        const groupedVisits = {
          Monday: [],
          Tuesday: [],
          Wednesday: [],
          Thursday: [],
          Friday: [],
          Saturday: [],
          Sunday: [],
        };

        response.forEach(visit => {
          const visitDate = new Date(visit.planned_date);
          const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
          const dayName = dayNames[visitDate.getDay()];
          
          if (groupedVisits[dayName]) {
            groupedVisits[dayName].push({
              id: visit.id,
              customer: visit.customer?.name || 'Unknown Customer',
              region: visit.customer?.city || 'Unknown',
              time: visit.planned_time || 'TBD',
              status: visit.status || 'pending',
              customer_id: visit.customer_id,
              planned_date: visit.planned_date,
            });
          }
        });

        setWeeklyPlan(groupedVisits);
      } else {
        console.warn('No weekly plan data available from API');
        setError('Unable to load weekly plan from server');
        const emptyPlan = { Monday: [], Tuesday: [], Wednesday: [], Thursday: [], Friday: [], Saturday: [], Sunday: [] };
        setWeeklyPlan(emptyPlan);
      }
    } catch (error) {
      console.error('Failed to load weekly plan:', error);
      setError(error.message);
      const emptyPlan = { Monday: [], Tuesday: [], Wednesday: [], Thursday: [], Friday: [], Saturday: [], Sunday: [] };
      setWeeklyPlan(emptyPlan);
    } finally {
      setLoading(false);
    }
  };

  const loadVisitsForDay = async (dateString) => {
    try {
      setLoadingDayVisits(true);
      
      const params = {
        start_date: dateString,
        end_date: dateString,
      };

      const response = await visitService.getVisits(params);
      
      if (response && Array.isArray(response)) {
        const dayVisits = response.map(visit => ({
          id: visit.id,
          customer: visit.customer?.name || 'Unknown Customer',
          region: visit.customer?.city || 'Unknown',
          time: visit.planned_time || 'TBD',
          status: visit.status || 'pending',
          customer_id: visit.customer_id,
          planned_date: visit.planned_date,
        }));
        
        setSelectedDayVisits(dayVisits);
      } else {
        setSelectedDayVisits([]);
      }
    } catch (error) {
      console.error('Failed to load visits for day:', error);
      setSelectedDayVisits([]);
    } finally {
      setLoadingDayVisits(false);
    }
  };

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await loadWeeklyPlan();
    setRefreshing(false);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return '#4CAF50';
      case 'pending': return '#FF9800';
      case 'cancelled': return '#F44336';
      default: return '#2196F3';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return 'checkmark-circle';
      case 'pending': return 'time';
      case 'cancelled': return 'close-circle';
      default: return 'help-circle';
    }
  };

  const calculateDayProgress = (visits) => {
    if (visits.length === 0) return 0;
    const completed = visits.filter(visit => visit.status === 'completed').length;
    return completed / visits.length;
  };

  const getTotalProgress = () => {
    let totalVisits = 0;
    let completedVisits = 0;
    
    Object.values(weeklyPlan).forEach(dayVisits => {
      totalVisits += dayVisits.length;
      completedVisits += dayVisits.filter(visit => visit.status === 'completed').length;
    });
    
    return totalVisits > 0 ? completedVisits / totalVisits : 0;
  };

  const filterVisitsByRegion = (visits) => {
    if (selectedRegion === 'All') return visits;
    return visits.filter(visit => visit.region === selectedRegion);
  };

  const filterVisitsBySearch = (visits) => {
    if (!searchQuery) return visits;
    return visits.filter(visit => 
      visit.customer.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const addNewVisit = () => {
    if (!newVisit.customer || !newVisit.region || !newVisit.day || !newVisit.time) {
      return;
    }

    const visit = {
      id: Date.now(),
      customer: newVisit.customer,
      region: newVisit.region,
      time: newVisit.time,
      status: 'pending',
    };

    const updatedPlan = {
      ...weeklyPlan,
      [newVisit.day]: [...weeklyPlan[newVisit.day], visit],
    };

    setWeeklyPlan(updatedPlan);
    setMarkedDates(generateMarkedDates(updatedPlan));

    setNewVisit({ customer: '', region: '', day: '', time: '' });
    setShowAddModal(false);
  };

  const updateVisitStatus = async (dayName, visitId, newStatus) => {
    try {
      // Update visit status on backend
      await visitService.updateVisit(visitId, { status: newStatus });
      
      // Update local state
      const updatedPlan = {
        ...weeklyPlan,
        [dayName]: weeklyPlan[dayName].map(visit =>
          visit.id === visitId ? { ...visit, status: newStatus } : visit
        ),
      };
      
      setWeeklyPlan(updatedPlan);
    } catch (error) {
      console.error('Failed to update visit status:', error);
      // Still update local state for better UX, will sync later
      const updatedPlan = {
        ...weeklyPlan,
        [dayName]: weeklyPlan[dayName].map(visit =>
          visit.id === visitId ? { ...visit, status: newStatus } : visit
        ),
      };
      
      setWeeklyPlan(updatedPlan);
    }
  };

  // Calendar view components
  const renderCalendarView = () => {
    return (
      <View style={styles.calendarViewContainer}>
        {renderCustomCalendar()}
        
        <View style={styles.selectedDayContainer}>
          <Surface style={styles.selectedDayHeader}>
            <View style={styles.selectedDayTitleContainer}>
              <Text style={styles.selectedDayTitle}>
                {format(parseISO(selectedDate), 'EEEE, MMMM d, yyyy')}
              </Text>
              {selectedDayVisits.length > 0 && (
                <Badge style={styles.visitBadge}>
                  {selectedDayVisits.length}
                </Badge>
              )}
            </View>
            
            {selectedDayVisits.length > 0 && (
              <View style={styles.statusSummary}>
                {selectedDayVisits.filter(v => v.status === 'completed').length > 0 && (
                  <View style={styles.statusSummaryItem}>
                    <View style={styles.completedDot} />
                    <Text style={styles.statusSummaryText}>
                      {selectedDayVisits.filter(v => v.status === 'completed').length} Completed
                    </Text>
                  </View>
                )}
                {selectedDayVisits.filter(v => v.status === 'pending').length > 0 && (
                  <View style={styles.statusSummaryItem}>
                    <View style={styles.pendingDot} />
                    <Text style={styles.statusSummaryText}>
                      {selectedDayVisits.filter(v => v.status === 'pending').length} Pending
                    </Text>
                  </View>
                )}
                {selectedDayVisits.filter(v => v.status === 'cancelled').length > 0 && (
                  <View style={styles.statusSummaryItem}>
                    <View style={styles.cancelledDot} />
                    <Text style={styles.statusSummaryText}>
                      {selectedDayVisits.filter(v => v.status === 'cancelled').length} Cancelled
                    </Text>
                  </View>
                )}
              </View>
            )}
          </Surface>
          
          <ScrollView style={styles.selectedDayVisits}>
            {loadingDayVisits ? (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>Loading visits...</Text>
              </View>
            ) : (
              renderDayVisits(selectedDayVisits, getDayNameFromDate(selectedDate))
            )}
          </ScrollView>
        </View>
      </View>
    );
  };

  const renderDayVisits = (visits, dayName) => {
    const filteredVisits = filterVisitsBySearch(filterVisitsByRegion(visits));
    
    if (filteredVisits.length === 0) {
      return (
        <View style={styles.noVisitsContainer}>
          <Ionicons name="calendar-outline" size={48} color="#ccc" />
          <Text style={styles.noVisitsText}>No visits planned for this day</Text>
        </View>
      );
    }

    return filteredVisits.map((visit) => (
      <Card key={visit.id} style={[styles.visitCard, { borderLeftWidth: 4, borderLeftColor: getStatusColor(visit.status) }]}>
        <Card.Content style={styles.visitCardContent}>
          <View style={styles.visitHeader}>
            <View style={styles.visitTimeContainer}>
              <Ionicons name="time-outline" size={16} color="#666" />
              <Text style={styles.visitTime}>{visit.time}</Text>
            </View>
            <View style={styles.visitStatusContainer}>
              <Ionicons
                name={getStatusIcon(visit.status)}
                size={20}
                color={getStatusColor(visit.status)}
              />
              <Text style={[styles.visitStatusText, { color: getStatusColor(visit.status) }]}>
                {visit.status.toUpperCase()}
              </Text>
            </View>
          </View>
          
          <Text style={styles.visitCustomer}>{visit.customer}</Text>
          
          <View style={styles.visitLocation}>
            <Ionicons name="location-outline" size={14} color="#666" />
            <Text style={styles.visitRegion}>{visit.region}</Text>
          </View>
          
          {visit.status === 'pending' && (
            <View style={styles.visitCardActions}>
              <Button
                mode="outlined"
                compact
                onPress={() => updateVisitStatus(dayName, visit.id, 'completed')}
                style={styles.visitCardButton}
                labelStyle={styles.visitCardButtonLabel}
              >
                Complete
              </Button>
              <Button
                mode="contained"
                compact
                onPress={() => navigation.navigate('Visit Entry')}
                style={[styles.visitCardButton, styles.primaryButton]}
                labelStyle={styles.visitCardButtonLabel}
              >
                Start Visit
              </Button>
            </View>
          )}
          
          {visit.status === 'completed' && (
            <View style={styles.completedVisitInfo}>
              <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
              <Text style={styles.completedText}>Visit Completed</Text>
            </View>
          )}
          
          {visit.status === 'cancelled' && (
            <View style={styles.cancelledVisitInfo}>
              <Ionicons name="close-circle" size={16} color="#F44336" />
              <Text style={styles.cancelledText}>Visit Cancelled</Text>
            </View>
          )}
        </Card.Content>
      </Card>
    ));
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <Title style={styles.headerTitle}>Weekly Plan</Title>
            <View style={styles.headerActions}>
              <Button
                mode="outlined"
                compact
                onPress={() => navigation.navigate('Visits', { screen: 'PendingVisits' })}
                style={styles.pendingButton}
                icon="clock-alert"
                labelStyle={styles.pendingButtonLabel}
              >
                Pending
              </Button>
              <View style={styles.viewModeToggle}>
                <Button
                  mode={viewMode === 'calendar' ? 'contained' : 'outlined'}
                  compact
                  onPress={() => setViewMode('calendar')}
                  style={[styles.toggleButton, viewMode === 'calendar' && styles.activeToggle]}
                  labelStyle={styles.toggleLabel}
                >
                  Calendar
                </Button>
                <Button
                  mode={viewMode === 'list' ? 'contained' : 'outlined'}
                  compact
                  onPress={() => setViewMode('list')}
                  style={[styles.toggleButton, viewMode === 'list' && styles.activeToggle]}
                  labelStyle={styles.toggleLabel}
                >
                  List
                </Button>
              </View>
            </View>
          </View>
          
          <View style={styles.progressContainer}>
            <Text style={styles.progressText}>
              Week Progress: {Math.round(getTotalProgress() * 100)}%
            </Text>
            <ProgressBar
              progress={getTotalProgress()}
              color="#4CAF50"
              style={styles.weekProgress}
            />
          </View>
          
        {error && (
          <Text style={styles.errorText}>
            {error} - Using offline data
          </Text>
        )}
      </View>

      <View style={styles.filters}>
        <Searchbar
          placeholder="Search customers..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />
        
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.regionFilters}
        >
          {regions.map((region) => (
            <Chip
              key={region}
              selected={selectedRegion === region}
              onPress={() => setSelectedRegion(region)}
              style={[
                styles.regionChip,
                selectedRegion === region && styles.selectedChip
              ]}
              textStyle={selectedRegion === region && styles.selectedChipText}
            >
              {region}
            </Chip>
          ))}
        </ScrollView>
      </View>

      {viewMode === 'calendar' ? (
        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {renderCalendarView()}
        </ScrollView>
      ) : (
        <ScrollView
          style={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {days.map((day) => {
            const dayVisits = weeklyPlan[day];
            const filteredVisits = filterVisitsBySearch(filterVisitsByRegion(dayVisits));
            const dayProgress = calculateDayProgress(dayVisits);

            return (
              <Card key={day} style={styles.dayCard}>
                <Card.Content>
                  <View style={styles.dayHeader}>
                    <View style={styles.dayInfo}>
                      <Text style={styles.dayName}>{day}</Text>
                      <Text style={styles.visitCount}>
                        {filteredVisits.length} visit{filteredVisits.length !== 1 ? 's' : ''}
                      </Text>
                    </View>
                    <View style={styles.dayProgress}>
                      <Text style={styles.progressPercent}>
                        {Math.round(dayProgress * 100)}%
                      </Text>
                      <ProgressBar
                        progress={dayProgress}
                        color="#4CAF50"
                        style={styles.dayProgressBar}
                      />
                    </View>
                  </View>

                  {filteredVisits.length === 0 ? (
                    <Text style={styles.noVisits}>No visits planned</Text>
                  ) : (
                    filteredVisits.map((visit) => (
                      <View key={visit.id} style={styles.visitItem}>
                        <View style={styles.visitDetails}>
                          <View style={styles.visitInfo}>
                            <Text style={styles.customerName}>{visit.customer}</Text>
                            <View style={styles.visitMeta}>
                              <Ionicons name="location" size={16} color="#666" />
                              <Text style={styles.region}>{visit.region}</Text>
                              <Ionicons name="time" size={16} color="#666" style={styles.timeIcon} />
                              <Text style={styles.time}>{visit.time}</Text>
                            </View>
                          </View>
                          <View style={styles.visitStatus}>
                            <Ionicons
                              name={getStatusIcon(visit.status)}
                              size={24}
                              color={getStatusColor(visit.status)}
                            />
                            <Chip
                              style={[styles.statusChip, { backgroundColor: getStatusColor(visit.status) }]}
                              textStyle={styles.statusText}
                            >
                              {visit.status.toUpperCase()}
                            </Chip>
                          </View>
                        </View>
                        
                        {visit.status === 'pending' && (
                          <View style={styles.visitActions}>
                            <Button
                              mode="outlined"
                              compact
                              onPress={() => updateVisitStatus(day, visit.id, 'completed')}
                              style={styles.actionButton}
                            >
                              Mark Complete
                            </Button>
                            <Button
                              mode="contained"
                              compact
                              onPress={() => navigation.navigate('Visit Entry')}
                              style={styles.actionButton}
                            >
                              Start Visit
                            </Button>
                          </View>
                        )}
                      </View>
                    ))
                  )}
                </Card.Content>
              </Card>
            );
          })}
        </ScrollView>
      )}

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => setShowAddModal(true)}
        label="Add Visit"
      />

      <Portal>
        <Modal
          visible={showAddModal}
          onDismiss={() => setShowAddModal(false)}
          contentContainerStyle={styles.modal}
        >
          <Title style={styles.modalTitle}>Add New Visit</Title>
          
          <TextInput
            label="Customer Name"
            value={newVisit.customer}
            onChangeText={(text) => setNewVisit(prev => ({ ...prev, customer: text }))}
            mode="outlined"
            style={styles.modalInput}
          />
          
          <TextInput
            label="Region"
            value={newVisit.region}
            onChangeText={(text) => setNewVisit(prev => ({ ...prev, region: text }))}
            mode="outlined"
            style={styles.modalInput}
          />
          
          <TextInput
            label="Day"
            value={newVisit.day}
            onChangeText={(text) => setNewVisit(prev => ({ ...prev, day: text }))}
            mode="outlined"
            style={styles.modalInput}
            placeholder="Monday, Tuesday, etc."
          />
          
          <TextInput
            label="Time"
            value={newVisit.time}
            onChangeText={(text) => setNewVisit(prev => ({ ...prev, time: text }))}
            mode="outlined"
            style={styles.modalInput}
            placeholder="10:00 AM"
          />
          
          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => setShowAddModal(false)}
              style={styles.modalButton}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={addNewVisit}
              style={styles.modalButton}
            >
              Add Visit
            </Button>
          </View>
        </Modal>
      </Portal>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    paddingTop: 10,
    elevation: 2,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  pendingButton: {
    borderColor: '#FF9800',
    borderRadius: 20,
  },
  pendingButtonLabel: {
    color: '#FF9800',
    fontSize: 12,
  },
  viewModeToggle: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    padding: 2,
  },
  toggleButton: {
    marginHorizontal: 2,
    minWidth: 70,
  },
  activeToggle: {
    backgroundColor: '#2196F3',
  },
  toggleLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  progressContainer: {
    marginBottom: 10,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 12,
    color: '#f44336',
    marginTop: 4,
    fontStyle: 'italic',
  },
  weekProgress: {
    height: 6,
    borderRadius: 3,
  },
  filters: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingBottom: 15,
  },
  searchbar: {
    marginBottom: 15,
    elevation: 1,
  },
  regionFilters: {
    paddingRight: 20,
  },
  regionChip: {
    marginRight: 10,
    backgroundColor: '#f0f0f0',
  },
  selectedChip: {
    backgroundColor: '#2196F3',
  },
  selectedChipText: {
    color: '#fff',
  },
  
  // Calendar View Styles
  calendarViewContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  calendarContainer: {
    backgroundColor: '#fff',
    margin: 10,
    borderRadius: 8,
    elevation: 2,
    padding: 10,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  calendarNavButton: {
    padding: 8,
  },
  calendarWeekTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  weekCalendarGrid: {
    flexDirection: 'row',
    paddingVertical: 10,
  },
  weekCalendarDay: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 15,
    marginHorizontal: 2,
    borderRadius: 8,
    minHeight: 100,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  weekDayName: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
    marginBottom: 5,
  },
  weekCalendarDayText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  weekVisitIndicators: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 5,
  },
  statusIndicatorRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 4,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 2,
    marginVertical: 1,
  },
  statusCount: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#666',
    marginLeft: 2,
  },
  selectedStatusCount: {
    color: '#fff',
  },
  totalVisitCount: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#333',
  },
  visitCount: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#666',
    marginLeft: 4,
  },
  calendarMonthTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  calendarWeekHeader: {
    flexDirection: 'row',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  calendarWeekDay: {
    flex: 1,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingTop: 5,
  },
  calendarDay: {
    width: `${100/7}%`,
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
    position: 'relative',
  },
  selectedDay: {
    backgroundColor: '#2196F3',
    borderRadius: 20,
  },
  todayDay: {
    borderWidth: 2,
    borderColor: '#2196F3',
    borderRadius: 20,
  },
  otherMonthDay: {
    opacity: 0.3,
  },
  calendarDayText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  selectedDayText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  todayDayText: {
    color: '#2196F3',
    fontWeight: 'bold',
  },
  otherMonthDayText: {
    color: '#999',
  },
  visitIndicators: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  completedDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginHorizontal: 1,
  },
  pendingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF9800',
    marginHorizontal: 1,
  },
  cancelledDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#F44336',
    marginHorizontal: 1,
  },
  calendar: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingBottom: 10,
  },
  selectedDayContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  selectedDayHeader: {
    backgroundColor: '#fff',
    padding: 16,
    margin: 10,
    marginBottom: 5,
    borderRadius: 8,
    elevation: 2,
  },
  selectedDayTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  selectedDayTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  statusSummary: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
  },
  statusSummaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 4,
    marginVertical: 2,
  },
  statusSummaryText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
    fontWeight: '500',
  },
  visitBadge: {
    backgroundColor: '#2196F3',
  },
  selectedDayVisits: {
    flex: 1,
    paddingHorizontal: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    fontStyle: 'italic',
  },
  noVisitsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  noVisitsText: {
    fontSize: 16,
    color: '#999',
    marginTop: 10,
    textAlign: 'center',
  },
  visitCard: {
    marginBottom: 10,
    elevation: 2,
    borderRadius: 8,
  },
  visitCardContent: {
    paddingVertical: 12,
  },
  visitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  visitTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  visitTime: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  visitStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  visitStatusText: {
    marginLeft: 4,
    fontSize: 12,
    fontWeight: 'bold',
  },
  visitCustomer: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 6,
  },
  visitLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  visitRegion: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  completedVisitInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E8',
    padding: 8,
    borderRadius: 4,
    marginTop: 8,
  },
  completedText: {
    marginLeft: 6,
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '500',
  },
  cancelledVisitInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEBEE',
    padding: 8,
    borderRadius: 4,
    marginTop: 8,
  },
  cancelledText: {
    marginLeft: 6,
    fontSize: 12,
    color: '#F44336',
    fontWeight: '500',
  },
  visitCardActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  visitCardButton: {
    marginLeft: 8,
    minWidth: 80,
  },
  primaryButton: {
    backgroundColor: '#2196F3',
  },
  visitCardButtonLabel: {
    fontSize: 12,
  },
  
  // List View Styles (existing)
  listContent: {
    flex: 1,
    paddingHorizontal: 15,
  },
  dayCard: {
    marginTop: 15,
    elevation: 3,
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  dayInfo: {
    flex: 1,
  },
  dayName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  visitCount: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  dayProgress: {
    alignItems: 'flex-end',
    minWidth: 80,
  },
  progressPercent: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 5,
  },
  dayProgressBar: {
    width: 80,
    height: 4,
  },
  noVisits: {
    textAlign: 'center',
    color: '#999',
    fontStyle: 'italic',
    paddingVertical: 20,
  },
  visitItem: {
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  visitDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  visitInfo: {
    flex: 1,
  },
  customerName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  visitMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  region: {
    marginLeft: 5,
    color: '#666',
  },
  timeIcon: {
    marginLeft: 15,
  },
  time: {
    marginLeft: 5,
    color: '#666',
  },
  visitStatus: {
    alignItems: 'center',
  },
  statusChip: {
    marginTop: 5,
  },
  statusText: {
    color: '#fff',
    fontSize: 10,
  },
  visitActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    marginLeft: 10,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#2196F3',
  },
  modal: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  modalTitle: {
    marginBottom: 20,
    textAlign: 'center',
  },
  modalInput: {
    marginBottom: 15,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 5,
  },
});

export default WeeklyPlanScreen;
