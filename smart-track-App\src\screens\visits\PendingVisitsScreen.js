import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  Paragraph,
  Chip,
  Text,
  Searchbar,
  IconButton,
  Button,
  Divider,
  Banner,
} from 'react-native-paper';
import { format, parseISO, differenceInDays } from 'date-fns';
import { visitService } from '../../services/visitService.js';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const PendingVisitsScreen = ({ navigation }) => {
  const [pendingVisits, setPendingVisits] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const loadPendingVisits = useCallback(async () => {
    try {
      setLoading(true);
      const data = await visitService.getPendingVisits({ limit: 100 });
      setPendingVisits(data);
    } catch (error) {
      Alert.alert('Error', 'Failed to load pending visits');
      console.error('Load pending visits error:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadPendingVisits();
    setRefreshing(false);
  }, [loadPendingVisits]);

  useEffect(() => {
    loadPendingVisits();
  }, [loadPendingVisits]);

  const getPriorityColor = (daysOverdue) => {
    if (daysOverdue > 21) return '#F44336'; // Red for very overdue
    if (daysOverdue > 14) return '#FF9800'; // Orange for overdue
    return '#FFC107'; // Yellow for moderate delay
  };

  const getPriorityLevel = (daysOverdue) => {
    if (daysOverdue > 21) return 'Critical';
    if (daysOverdue > 14) return 'High';
    return 'Medium';
  };

  const formatDate = (dateString) => {
    const date = parseISO(dateString);
    return format(date, 'MMM dd, yyyy HH:mm');
  };

  const getDaysOverdue = (plannedDate) => {
    const date = parseISO(plannedDate);
    return Math.max(0, differenceInDays(new Date(), date));
  };

  const filteredVisits = pendingVisits.filter(visit =>
    !searchQuery || 
    visit.customer?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    visit.purpose?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleVisitPress = (visit) => {
    navigation.navigate('VisitDetail', { visitId: visit.id });
  };

  const handleRescheduleVisit = (visit) => {
    Alert.alert(
      'Reschedule Visit',
      `Do you want to reschedule the visit to ${visit.customer?.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reschedule',
          onPress: () => {
            // Navigate to create visit screen with pre-filled data
            navigation.navigate('CreateVisit', {
              customerId: visit.customer_id,
              existingVisitId: visit.id,
              isRescheduling: true
            });
          }
        },
      ]
    );
  };

  const handleCheckInVisit = (visit) => {
    navigation.navigate('VisitCheckIn', { visitId: visit.id });
  };

  const renderVisitCard = ({ item: visit }) => {
    const daysOverdue = getDaysOverdue(visit.planned_date);
    const priorityColor = getPriorityColor(daysOverdue);
    const priorityLevel = getPriorityLevel(daysOverdue);

    return (
      <Card style={[styles.visitCard, { borderLeftColor: priorityColor, borderLeftWidth: 4 }]}>
        <TouchableOpacity onPress={() => handleVisitPress(visit)}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <View style={styles.customerInfo}>
                <Title style={styles.customerName}>{visit.customer?.name || 'Unknown Customer'}</Title>
                <Paragraph style={styles.customerAddress}>
                  {visit.customer?.address || 'No address available'}
                </Paragraph>
              </View>
              <Chip 
                mode="outlined" 
                style={[styles.priorityChip, { borderColor: priorityColor }]}
                textStyle={{ color: priorityColor, fontSize: 12 }}
              >
                {priorityLevel}
              </Chip>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.visitDetails}>
              <View style={styles.detailRow}>
                <MaterialCommunityIcons name="calendar-clock" size={16} color="#666" />
                <Text style={styles.detailText}>
                  Planned: {formatDate(visit.planned_date)}
                </Text>
              </View>
              
              <View style={styles.detailRow}>
                <MaterialCommunityIcons name="clock-alert" size={16} color={priorityColor} />
                <Text style={[styles.detailText, { color: priorityColor }]}>
                  {daysOverdue} days overdue
                </Text>
              </View>
              
              {visit.purpose && (
                <View style={styles.detailRow}>
                  <MaterialCommunityIcons name="target" size={16} color="#666" />
                  <Text style={styles.detailText}>{visit.purpose}</Text>
                </View>
              )}
            </View>

            <View style={styles.actionButtons}>
              <Button
                mode="contained"
                onPress={() => handleCheckInVisit(visit)}
                style={[styles.actionButton, styles.checkInButton]}
                icon="map-marker-check"
                compact
              >
                Check In
              </Button>
              <Button
                mode="outlined"
                onPress={() => handleRescheduleVisit(visit)}
                style={[styles.actionButton, styles.rescheduleButton]}
                icon="calendar-edit"
                compact
              >
                Reschedule
              </Button>
            </View>
          </Card.Content>
        </TouchableOpacity>
      </Card>
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <Banner
        visible={true}
        icon="clock-alert"
        style={styles.banner}
      >
        <Text style={styles.bannerText}>
          These visits were planned 2+ weeks ago and haven't been completed. 
          Please check in or reschedule them.
        </Text>
      </Banner>
      
      <Searchbar
        placeholder="Search pending visits..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
      />
      
      {filteredVisits.length > 0 && (
        <Text style={styles.resultsCount}>
          {filteredVisits.length} pending visit{filteredVisits.length !== 1 ? 's' : ''} found
        </Text>
      )}
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <MaterialCommunityIcons name="calendar-check" size={64} color="#4CAF50" />
      <Title style={styles.emptyTitle}>No Pending Visits</Title>
      <Paragraph style={styles.emptyDescription}>
        Great! You don't have any visits that are pending from 2+ weeks ago.
      </Paragraph>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={filteredVisits}
        renderItem={renderVisitCard}
        keyExtractor={(item) => item.id.toString()}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={!loading ? renderEmptyState : null}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={filteredVisits.length === 0 ? styles.emptyContainer : styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  listContainer: {
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  banner: {
    marginBottom: 16,
    backgroundColor: '#FFF3E0',
  },
  bannerText: {
    fontSize: 14,
    color: '#E65100',
  },
  searchBar: {
    marginBottom: 12,
    elevation: 2,
  },
  resultsCount: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    textAlign: 'center',
  },
  visitCard: {
    marginBottom: 12,
    elevation: 3,
    backgroundColor: 'white',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  customerInfo: {
    flex: 1,
    marginRight: 12,
  },
  customerName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  customerAddress: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
  },
  priorityChip: {
    borderWidth: 1,
  },
  divider: {
    marginVertical: 12,
  },
  visitDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  detailText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  checkInButton: {
    backgroundColor: '#4CAF50',
  },
  rescheduleButton: {
    borderColor: '#FF9800',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default PendingVisitsScreen;
