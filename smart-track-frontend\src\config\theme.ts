import { ThemeConfig } from 'antd'

const theme: ThemeConfig = {
  token: {
    // Seed Token
    colorPrimary: '#1890ff',
    borderRadius: 6,
    
    // <PERSON><PERSON>
    colorBgContainer: '#ffffff',
    colorText: '#262626',
    colorTextSecondary: '#595959',
    colorBorder: '#d9d9d9',
    
    // Font
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif',
    fontSize: 14,
    fontSizeHeading1: 32,
    fontSizeHeading2: 24,
    fontSizeHeading3: 20,
    fontSizeHeading4: 16,
    fontSizeHeading5: 14,
    
    // Layout
    sizeStep: 4,
    sizeUnit: 4,
    
    // Line height
    lineHeight: 1.5714285714285714,
    lineHeightHeading1: 1.210526315789474,
    lineHeightHeading2: 1.2631578947368421,
    lineHeightHeading3: 1.3,
    lineHeightHeading4: 1.4,
    lineHeightHeading5: 1.5714285714285714,
  },
  components: {
    Layout: {
      headerBg: '#ffffff',
      headerHeight: 64,
      headerPadding: '0 24px',
      siderBg: '#ffffff',
      bodyBg: '#f5f5f5',
    },
    Menu: {
      itemBg: 'transparent',
      itemSelectedBg: '#e6f7ff',
      itemHoverBg: '#f5f5f5',
      itemSelectedColor: '#1890ff',
      itemColor: '#595959',
    },
    Button: {
      borderRadius: 6,
      controlHeight: 36,
    },
    Card: {
      borderRadius: 8,
      headerBg: '#fafafa',
    },
    Table: {
      headerBg: '#fafafa',
      borderRadius: 8,
    },
    Input: {
      borderRadius: 6,
      controlHeight: 36,
    },
    Select: {
      borderRadius: 6,
      controlHeight: 36,
    },
    DatePicker: {
      borderRadius: 6,
      controlHeight: 36,
    },
  },
}

export default theme
