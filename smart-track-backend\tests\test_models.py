"""
Test database models and relationships
"""
import pytest
from sqlalchemy.orm import Session
from app.models.user import User, Role
from app.models.customer import Customer
from app.core.security import get_password_hash

def test_user_model_creation(db_session: Session):
    """Test creating a user model"""
    # Create a role first
    role = Role(name="sales_rep", description="Sales Representative")
    db_session.add(role)
    db_session.commit()
    
    # Create a user
    user = User(
        sap_code="TST001",
        username="testuser",
        email="<EMAIL>",
        full_name="Test User",
        hashed_password=get_password_hash("testpassword"),
        role_id=role.id
    )
    db_session.add(user)
    db_session.commit()
    
    # Verify user was created
    saved_user = db_session.query(User).filter(User.username == "testuser").first()
    assert saved_user is not None
    assert saved_user.email == "<EMAIL>"
    assert saved_user.full_name == "Test User"
    assert saved_user.role.name == "sales_rep"

def test_customer_model_creation(db_session: Session):
    """Test creating a customer model"""
    customer = Customer(
        code="CUST001",
        name="Test Customer Ltd",
        address="123 Test Street",
        city="Test City",
        phone="+1234567890",
        email="<EMAIL>",
        credit_limit=50000.00,
        customer_type="retail",
        latitude=40.7128,
        longitude=-74.0060
    )
    db_session.add(customer)
    db_session.commit()
    
    # Verify customer was created
    saved_customer = db_session.query(Customer).filter(Customer.code == "CUST001").first()
    assert saved_customer is not None
    assert saved_customer.name == "Test Customer Ltd"
    assert saved_customer.credit_limit == 50000.00
    assert saved_customer.latitude == 40.7128

def test_user_role_relationship(db_session: Session):
    """Test user-role relationship"""
    # Create roles
    admin_role = Role(name="admin", description="Administrator")
    sales_role = Role(name="sales_rep", description="Sales Representative")
    db_session.add_all([admin_role, sales_role])
    db_session.commit()
    
    # Create users with different roles
    admin_user = User(
        sap_code="ADM001",
        username="admin",
        email="<EMAIL>",
        full_name="Admin User",
        hashed_password=get_password_hash("adminpass"),
        role_id=admin_role.id
    )
    sales_user = User(
        sap_code="SLS001",
        username="salesrep",
        email="<EMAIL>",
        full_name="Sales Rep",
        hashed_password=get_password_hash("salespass"),
        role_id=sales_role.id
    )
    db_session.add_all([admin_user, sales_user])
    db_session.commit()
    
    # Test relationships
    assert admin_user.role.name == "admin"
    assert sales_user.role.name == "sales_rep"
    assert len(admin_role.users) == 1
    assert len(sales_role.users) == 1

def test_customer_unique_constraints(db_session: Session):
    """Test customer unique constraints"""
    # Create first customer
    customer1 = Customer(
        code="CUST001",
        name="Customer One",
        address="Address 1",
        city="City 1"
    )
    db_session.add(customer1)
    db_session.commit()
    
    # Try to create another customer with same code
    customer2 = Customer(
        code="CUST001",  # Same code - should fail
        name="Customer Two",
        address="Address 2",
        city="City 2"
    )
    db_session.add(customer2)
    
    with pytest.raises(Exception):  # Should raise integrity error
        db_session.commit()
