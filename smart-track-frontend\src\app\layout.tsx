import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import ClientWrapper from '@/components/ClientWrapper'
import { AntdRegistry } from '@ant-design/nextjs-registry'
import { ThemeProvider } from '@/contexts/ThemeContext'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Smart Track Dashboard',
  description: 'Sales Force Automation Dashboard',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ThemeProvider>
          <AntdRegistry>
            <ClientWrapper>
              {children}
            </ClientWrapper>
          </AntdRegistry>
        </ThemeProvider>
      </body>
    </html>
  )
}
