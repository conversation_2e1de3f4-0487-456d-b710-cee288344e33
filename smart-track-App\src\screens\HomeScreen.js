import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Title,
  Button,
  Chip,
  ProgressBar,
  Avatar,
  Caption,
  Subheading,
} from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuthStore } from '../stores/authStore';
import dashboardService from '../services/dashboardService';

const { width } = Dimensions.get('window');

const HomeScreen = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const user = useAuthStore((state) => state.user);
  const logout = useAuthStore((state) => state.logout);

  // Load dashboard data on component mount and when user changes
  useEffect(() => {
    if (user && user.id) {
      loadDashboardData();
    } else {
      console.warn('📊 No user found, cannot load dashboard data');
      setError('User not authenticated. Please log in again.');
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Log current user info for debugging
      console.log('📊 Loading dashboard data for user:', user?.username || 'No user');
      
      const response = await dashboardService.getDashboardData();
      
      if (response.success) {
        const transformedData = response.data;
        
        // Log the received data to verify it's user-specific
        console.log('📊 Dashboard data received for user:', {
          completedVisits: transformedData.kpis.completedVisits,
          targetPercentage: transformedData.kpis.targetPercentage,
          customersVisited: transformedData.kpis.customersVisited,
          upcomingVisitsCount: transformedData.upcomingVisits?.length || 0
        });
        
        // Final safety check to ensure all numeric values are valid
        const safeData = {
          ...transformedData,
          kpis: {
            ...transformedData.kpis,
            completedVisits: Number.isFinite(transformedData.kpis.completedVisits) ? transformedData.kpis.completedVisits : 0,
            targetVisits: Number.isFinite(transformedData.kpis.targetVisits) ? transformedData.kpis.targetVisits : 0,
            targetPercentage: Number.isFinite(transformedData.kpis.targetPercentage) ? transformedData.kpis.targetPercentage : 0,
            monthlyTarget: Number.isFinite(transformedData.kpis.monthlyTarget) ? transformedData.kpis.monthlyTarget : 0,
            revenue: Number.isFinite(transformedData.kpis.revenue) ? transformedData.kpis.revenue : 0,
            revenueTarget: Number.isFinite(transformedData.kpis.revenueTarget) ? transformedData.kpis.revenueTarget : 0,
            customersVisited: Number.isFinite(transformedData.kpis.customersVisited) ? transformedData.kpis.customersVisited : 0,
            totalCustomers: Number.isFinite(transformedData.kpis.totalCustomers) ? transformedData.kpis.totalCustomers : 0,
          }
        };
        
        setDashboardData(safeData);
      } else {
        // Don't use fallback data - show error instead
        console.warn('API error, no fallback data used:', response.error);
        setError(response.error || 'Failed to load dashboard data');
        setDashboardData(null);
      }
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      setError(error.message || 'Network error loading dashboard data');
      setDashboardData(null);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          },
        },
      ]
    );
  };

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  }, []);

  const getGreeting = () => {
    return 'Welcome';
  };

  const getProgressColor = (percentage) => {
    // Handle NaN and invalid values
    if (!isFinite(percentage) || isNaN(percentage)) {
      return '#f44336'; // Default to red for invalid values
    }
    
    if (percentage >= 80) return '#4caf50';
    if (percentage >= 60) return '#ff9800';
    return '#f44336';
  };

  const getPriorityColor = (priority) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return { backgroundColor: '#ffebee', textColor: '#c62828' };
      case 'medium':
        return { backgroundColor: '#fff3e0', textColor: '#ef6c00' };
      case 'low':
        return { backgroundColor: '#e8f5e8', textColor: '#2e7d32' };
      default:
        return { backgroundColor: '#f5f5f5', textColor: '#757575' };
    }
  };

  const renderGreetingCard = () => (
    <Card style={styles.greetingCard}>
      <LinearGradient
        colors={['#2196F3', '#1976D2']}
        style={styles.greetingGradient}
      >
        <View style={styles.greetingContent}>
          <View style={styles.greetingHeader}>
            <View style={styles.greetingTextContainer}>
              <Text style={styles.greetingText}>
                {getGreeting()}, {user?.full_name || 'User'}!
              </Text>
              <Text style={styles.greetingSubtext}>
                {currentTime.toLocaleDateString('en-US', {
                  weekday: 'long',
                  month: 'long',
                  day: 'numeric',
                })}
              </Text>
              {error && (
                <Text style={[styles.greetingSubtext, { color: '#ffcdd2' }]}>
                  Using offline data
                </Text>
              )}
            </View>
            <View style={styles.rightSection}>
              <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
                <MaterialCommunityIcons 
                  name="logout" 
                  size={20} 
                  color="white" 
                />
                <Text style={styles.logoutText}>Logout</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </LinearGradient>
    </Card>
  );

  const renderKPICards = () => {
    if (!dashboardData?.kpis) return null;
    
    const { kpis } = dashboardData;
    
    // Safe division helper to prevent NaN
    const safeDivision = (numerator, denominator) => {
      if (!denominator || denominator === 0 || !isFinite(numerator) || !isFinite(denominator)) {
        return 0;
      }
      const result = (numerator / denominator) * 100;
      return isNaN(result) || !isFinite(result) ? 0 : Math.min(result, 100);
    };
    
    // Safe progress helper for ProgressBar component (0-1 range)
    const safeProgress = (value) => {
      const progress = value / 100;
      return isNaN(progress) || !isFinite(progress) ? 0 : Math.max(0, Math.min(1, progress));
    };
    
    const visitProgress = safeDivision(kpis.completedVisits, kpis.targetVisits);
    const revenueProgress = safeDivision(kpis.revenue, kpis.revenueTarget);
    const customerProgress = safeDivision(kpis.customersVisited, kpis.totalCustomers);

    return (
      <View style={styles.kpiContainer}>
        <View style={styles.sectionTitleContainer}>
          <MaterialCommunityIcons name="chart-line" size={24} color="#2196F3" />
          <Title style={styles.sectionTitle}>Performance Overview</Title>
        </View>
        
        <View style={styles.kpiRow}>
          <Card style={[styles.kpiCard, styles.visitKpiCard]}>
            <Card.Content style={styles.kpiContent}>
              <View style={styles.kpiIconContainer}>
                <MaterialCommunityIcons 
                  name="calendar-check" 
                  size={28} 
                  color="#2196F3" 
                />
              </View>
              <Text style={styles.kpiValue}>{kpis.completedVisits}</Text>
              <Text style={styles.kpiLabel}>Completed Visits</Text>
              <View style={styles.progressContainer}>
                <ProgressBar 
                  progress={safeProgress(visitProgress)} 
                  color="#2196F3"
                  style={styles.kpiProgress}
                />
                <Text style={styles.progressText}>{isFinite(visitProgress) ? visitProgress.toFixed(0) : '0'}%</Text>
              </View>
              <Caption style={styles.kpiCaption}>
                {kpis.completedVisits} of {kpis.targetVisits} visits
              </Caption>
            </Card.Content>
          </Card>

          <Card style={[styles.kpiCard, styles.targetKpiCard]}>
            <Card.Content style={styles.kpiContent}>
              <View style={styles.kpiIconContainer}>
                <MaterialCommunityIcons 
                  name="target" 
                  size={28} 
                  color="#4CAF50" 
                />
              </View>
              <Text style={styles.kpiValue}>{isFinite(visitProgress) ? visitProgress.toFixed(0) : '0'}%</Text>
              <Text style={styles.kpiLabel}>Target Achievement</Text>
              <View style={styles.progressContainer}>
                <ProgressBar 
                  progress={safeProgress(visitProgress)} 
                  color="#4CAF50"
                  style={styles.kpiProgress}
                />
                <Text style={styles.progressText}>Goal</Text>
              </View>
              <Caption style={styles.kpiCaption}>
                Monthly progress
              </Caption>
            </Card.Content>
          </Card>
        </View>


      </View>
    );
  };

  const renderUpcomingVisits = () => {
    if (!dashboardData?.upcomingVisits || dashboardData.upcomingVisits.length === 0) {
      return (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <MaterialCommunityIcons name="calendar-clock" size={22} color="#2196F3" />
              <Title style={styles.sectionTitle}>Upcoming Visits</Title>
            </View>
            <Button
              mode="outlined"
              onPress={() => navigation.navigate('Visits')}
              compact
              style={styles.viewAllButton}
            >
              View All
            </Button>
          </View>
          <Card style={styles.emptyCard}>
            <Card.Content style={styles.emptyContent}>
              <MaterialCommunityIcons name="calendar-plus" size={48} color="#ccc" />
              <Text style={styles.emptyText}>No upcoming visits</Text>
              <Caption style={styles.emptyCaption}>Schedule a visit to see it here</Caption>
              <Button
                mode="contained"
                onPress={() => navigation.navigate('Visits', { screen: 'CreateVisit' })}
                style={styles.createButton}
                compact
              >
                Schedule Visit
              </Button>
            </Card.Content>
          </Card>
        </View>
      );
    }
    
    return (
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionTitleContainer}>
            <MaterialCommunityIcons name="calendar-clock" size={22} color="#2196F3" />
            <Title style={styles.sectionTitle}>Upcoming Visits</Title>
          </View>
          <Button
            mode="outlined"
            onPress={() => navigation.navigate('Visits')}
            compact
            style={styles.viewAllButton}
          >
            View All
          </Button>
        </View>

        {dashboardData.upcomingVisits.slice(0, 3).map((visit) => {
          const priorityStyle = getPriorityColor(visit.priority);
          return (
            <Card key={visit.id} style={styles.visitCard}>
              <Card.Content>
                <View style={styles.visitHeader}>
                  <View style={styles.visitInfo}>
                    <Title style={styles.visitCustomer}>{visit.customer}</Title>
                    <Caption style={styles.visitTime}>{visit.date} at {visit.time}</Caption>
                  </View>
                  <Chip
                    style={[styles.priorityChip, { backgroundColor: priorityStyle.backgroundColor }]}
                    textStyle={{ color: priorityStyle.textColor }}
                    compact
                  >
                    {visit.priority}
                  </Chip>
                </View>

                <View style={styles.visitDetails}>
                  <View style={styles.visitDetailRow}>
                    <MaterialCommunityIcons name="map-marker" size={16} color="#666" />
                    <Text style={styles.visitDetailText}>{visit.address}</Text>
                  </View>
                  <View style={styles.visitDetailRow}>
                    <MaterialCommunityIcons name="clipboard-text" size={16} color="#666" />
                    <Text style={styles.visitDetailText}>{visit.purpose}</Text>
                  </View>
                </View>

                <View style={styles.visitActions}>
                  <Button
                    mode="contained"
                    onPress={() => navigation.navigate('Visits', { 
                      screen: 'VisitDetail', 
                      params: { visitId: visit.id, visit: visit } 
                    })}
                    compact
                    style={styles.startVisitButton}
                  >
                    Start Visit
                  </Button>
                </View>
              </Card.Content>
            </Card>
          );
        })}
      </View>
    );
  };

  const renderPendingTasks = () => {
    const hasPendingVisits = dashboardData?.pendingTasks?.some(task => 
      task.category === 'Pending Visit' || task.id.includes('pending_visit')
    );

    if (!dashboardData?.pendingTasks || dashboardData.pendingTasks.length === 0) {
      return (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <MaterialCommunityIcons name="calendar-multiple" size={22} color="#FF9800" />
              <Title style={styles.sectionTitle}>Pending Tasks</Title>
            </View>
            <Button
              mode="outlined"
              onPress={() => navigation.navigate('Visits')}
              compact
              style={styles.viewAllButton}
            >
              View All
            </Button>
          </View>
          <Card style={styles.emptyCard}>
            <Card.Content style={styles.emptyContent}>
              <MaterialCommunityIcons name="calendar-clock" size={48} color="#ccc" />
              <Text style={styles.emptyText}>No pending tasks</Text>
              <Caption style={styles.emptyCaption}>All tasks are up to date</Caption>
            </Card.Content>
          </Card>
        </View>
      );
    }

    return (
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionTitleContainer}>
            <MaterialCommunityIcons name="calendar-multiple" size={22} color="#FF9800" />
            <Title style={styles.sectionTitle}>Pending Tasks</Title>
          </View>
          <View style={styles.headerButtons}>
            {hasPendingVisits && (
              <Button
                mode="outlined"
                onPress={() => navigation.navigate('Visits', { screen: 'PendingVisits' })}
                compact
                style={[styles.viewAllButton, styles.pendingVisitsButton]}
                icon="clock-alert"
              >
                Pending
              </Button>
            )}
            <Button
              mode="outlined"
              onPress={() => navigation.navigate('Visits')}
              compact
              style={styles.viewAllButton}
            >
              View All
            </Button>
          </View>
        </View>

        {dashboardData.pendingTasks.slice(0, 4).map((task) => {
          const priorityStyle = getPriorityColor(task.priority);
          return (
            <Card key={task.id} style={styles.taskCard}>
              <Card.Content>
                <View style={styles.taskHeader}>
                  <View style={styles.taskInfo}>
                    <Text style={styles.taskTitle}>{task.title}</Text>
                    <Caption style={styles.taskDue}>{task.dueDate}</Caption>
                  </View>
                  <View style={styles.taskBadges}>
                    <Chip
                      style={[styles.priorityChip, { backgroundColor: priorityStyle.backgroundColor }]}
                      textStyle={{ color: priorityStyle.textColor }}
                      compact
                    >
                      {task.priority}
                    </Chip>
                  </View>
                </View>

                <View style={styles.taskDetails}>
                  <Chip
                    style={styles.categoryChip}
                    compact
                  >
                    {task.category || 'Task'}
                  </Chip>
                  <Chip
                    style={styles.statusChip}
                    compact
                  >
                    {task.status}
                  </Chip>
                </View>
              </Card.Content>
            </Card>
          );
        })}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, styles.centerContent]}>
        <Text>Loading dashboard...</Text>
      </SafeAreaView>
    );
  }

  if (!dashboardData) {
    return (
      <SafeAreaView style={[styles.container, styles.centerContent]}>
        <Text style={styles.errorText}>Unable to load dashboard data</Text>
        <Text style={styles.errorSubtext}>Please check your connection and try again</Text>
        <Button mode="contained" onPress={loadDashboardData} style={{ marginTop: 16 }}>
          Retry
        </Button>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderGreetingCard()}
        {renderKPICards()}
        {renderUpcomingVisits()}
        {renderPendingTasks()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#f44336',
    textAlign: 'center',
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  greetingCard: {
    margin: 16,
    marginBottom: 20,
    elevation: 8,
    borderRadius: 16,
    overflow: 'hidden',
  },
  greetingGradient: {
    borderRadius: 16,
  },
  greetingContent: {
    padding: 20,
  },
  greetingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greetingTextContainer: {
    flex: 1,
  },
  greetingText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  greetingSubtext: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  rightSection: {
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 16,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  logoutText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  avatar: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  kpiContainer: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  logoutText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  kpiRow: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 12,
  },
  kpiCard: {
    flex: 1,
    elevation: 4,
    borderRadius: 12,
    backgroundColor: '#fff',
  },
  visitKpiCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  targetKpiCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  kpiContent: {
    alignItems: 'center',
    padding: 16,
  },
  kpiIconContainer: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 24,
    marginBottom: 12,
  },
  kpiValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  kpiLabel: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 12,
    fontWeight: '500',
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 8,
  },
  kpiProgress: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    marginBottom: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
  },
  kpiCaption: {
    fontSize: 12,
    textAlign: 'center',
    color: '#999',
  },
  section: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  viewAllButton: {
    borderColor: '#2196F3',
    borderRadius: 20,
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  pendingVisitsButton: {
    borderColor: '#FF9800',
  },
  visitCard: {
    marginBottom: 12,
    elevation: 3,
    borderRadius: 12,
    backgroundColor: '#fff',
  },
  visitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  visitInfo: {
    flex: 1,
  },
  visitCustomer: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  visitTime: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  priorityChip: {
    marginLeft: 12,
  },
  visitDetails: {
    marginBottom: 12,
  },
  visitDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  visitDetailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  visitActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  startVisitButton: {
    minWidth: 120,
  },
  taskCard: {
    marginBottom: 8,
    elevation: 2,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  taskInfo: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  taskDue: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  taskBadges: {
    marginLeft: 12,
  },
  taskDetails: {
    flexDirection: 'row',
    gap: 8,
  },
  categoryChip: {
    backgroundColor: '#e3f2fd',
  },
  statusChip: {
    backgroundColor: '#f5f5f5',
  },
  emptyCard: {
    elevation: 2,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  emptyContent: {
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
    marginTop: 12,
    marginBottom: 4,
  },
  emptyCaption: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginBottom: 16,
  },
  createButton: {
    minWidth: 140,
  },
});

export default HomeScreen;
