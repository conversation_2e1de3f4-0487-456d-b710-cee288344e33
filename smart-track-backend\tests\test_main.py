"""
Test FastAPI application startup and basic functionality
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from app.main import app

def test_app_startup():
    """Test that the FastAPI app starts correctly"""
    client = TestClient(app)
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Smart Track API"
    assert data["version"] == "1.0.0"
    assert data["status"] == "running"

def test_api_docs_accessible():
    """Test that API documentation is accessible"""
    client = TestClient(app)
    response = client.get("/docs")
    assert response.status_code == 200

def test_openapi_schema():
    """Test that OpenAPI schema is generated"""
    client = TestClient(app)
    response = client.get("/openapi.json")
    assert response.status_code == 200
    schema = response.json()
    assert schema["info"]["title"] == "Smart Track API"
    assert schema["info"]["version"] == "1.0.0"

def test_cors_headers():
    """Test CORS headers are present"""
    client = TestClient(app)
    response = client.options("/")
    # CORS headers should be present
    assert response.status_code in [200, 405]  # Some implementations return 405 for OPTIONS

def test_health_check():
    """Test basic health check functionality"""
    client = TestClient(app)
    response = client.get("/")
    assert response.status_code == 200
    assert "message" in response.json()
    assert "status" in response.json()
