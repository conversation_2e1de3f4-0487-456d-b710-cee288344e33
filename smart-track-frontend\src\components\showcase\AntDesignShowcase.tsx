'use client'

import React, { useState } from 'react'
import {
  <PERSON>ton,
  Card,
  Space,
  Typography,
  Input,
  Select,
  DatePicker,
  Table,
  Tag,
  Avatar,
  Badge,
  Alert,
  Modal,
  Form,
  Checkbox,
  Radio,
  Slider,
  Switch,
  Upload,
  Rate,
  Breadcrumb,
  Steps,
  Tabs,
  Collapse,
  Drawer,
  notification,
  message,
  Popconfirm,
  Tooltip,
  Row,
  Col,
  Divider,
  Progress,
  Spin,
  Empty,
  Result,
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
  UserOutlined,
  StarOutlined,
  HomeOutlined,
  SearchOutlined,
  BellOutlined,
  SettingOutlined,
  InboxOutlined,
} from '@ant-design/icons'

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { Panel } = Collapse
const { TabPane } = Tabs
const { Step } = Steps
const { Dragger } = Upload

const AntDesignShowcase: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDrawerVisible, setIsDrawerVisible] = useState(false)
  const [form] = Form.useForm()

  // Sample data for table
  const dataSource = [
    {
      key: '1',
      name: '<PERSON>',
      age: 32,
      address: 'New York No. 1 Lake Park',
      tags: ['nice', 'developer'],
    },
    {
      key: '2',
      name: 'Jim Green',
      age: 42,
      address: 'London No. 1 Lake Park',
      tags: ['loser'],
    },
    {
      key: '3',
      name: 'Joe Black',
      age: 32,
      address: 'Sidney No. 1 Lake Park',
      tags: ['cool', 'teacher'],
    },
  ]

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <a>{text}</a>,
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: 'Tags',
      key: 'tags',
      dataIndex: 'tags',
      render: (tags: string[]) => (
        <>
          {tags.map(tag => {
            let color = tag.length > 5 ? 'geekblue' : 'green'
            if (tag === 'loser') {
              color = 'volcano'
            }
            return (
              <Tag color={color} key={tag}>
                {tag.toUpperCase()}
              </Tag>
            )
          })}
        </>
      ),
    },
    {
      title: 'Action',
      key: 'action',
      render: (text: any, record: any) => (
        <Space size="middle">
          <Button type="link" icon={<EditOutlined />}>
            Edit
          </Button>
          <Popconfirm
            title="Are you sure to delete this user?"
            onConfirm={() => message.success('Deleted successfully')}
            okText="Yes"
            cancelText="No"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const showNotification = () => {
    notification.open({
      message: 'Notification Title',
      description:
        'This is the content of the notification. This is the content of the notification.',
      icon: <BellOutlined style={{ color: '#108ee9' }} />,
    })
  }

  const showMessage = () => {
    message.success('This is a success message')
  }

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={1}>Ant Design Component Showcase</Title>
        <Paragraph>
          This page demonstrates various Ant Design components that can be used throughout your Smart Track application.
        </Paragraph>

        {/* Navigation Components */}
        <Card title="Navigation Components" style={{ marginBottom: '24px' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div>
              <Title level={4}>Breadcrumb</Title>
              <Breadcrumb>
                <Breadcrumb.Item href="">
                  <HomeOutlined />
                </Breadcrumb.Item>
                <Breadcrumb.Item href="">
                  <UserOutlined />
                  <span>Application List</span>
                </Breadcrumb.Item>
                <Breadcrumb.Item>Application</Breadcrumb.Item>
              </Breadcrumb>
            </div>

            <div>
              <Title level={4}>Steps</Title>
              <Steps current={1}>
                <Step title="Finished" description="This is a description." />
                <Step title="In Progress" description="This is a description." />
                <Step title="Waiting" description="This is a description." />
              </Steps>
            </div>

            <div>
              <Title level={4}>Tabs</Title>
              <Tabs defaultActiveKey="1">
                <TabPane tab="Tab 1" key="1">
                  Content of Tab Pane 1
                </TabPane>
                <TabPane tab="Tab 2" key="2">
                  Content of Tab Pane 2
                </TabPane>
                <TabPane tab="Tab 3" key="3">
                  Content of Tab Pane 3
                </TabPane>
              </Tabs>
            </div>
          </Space>
        </Card>

        {/* Data Entry Components */}
        <Card title="Data Entry Components" style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={8}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Title level={4}>Form Controls</Title>
                <Input placeholder="Basic input" />
                <Input.Password placeholder="Password input" />
                <Input.Search placeholder="Search input" onSearch={showMessage} />
                <Select placeholder="Select option" style={{ width: '100%' }}>
                  <Option value="option1">Option 1</Option>
                  <Option value="option2">Option 2</Option>
                  <Option value="option3">Option 3</Option>
                </Select>
                <DatePicker style={{ width: '100%' }} />
              </Space>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Title level={4}>Other Controls</Title>
                <Checkbox.Group
                  options={['Apple', 'Pear', 'Orange']}
                  defaultValue={['Apple']}
                />
                <Radio.Group defaultValue="a">
                  <Radio.Button value="a">Hangzhou</Radio.Button>
                  <Radio.Button value="b">Shanghai</Radio.Button>
                  <Radio.Button value="c">Beijing</Radio.Button>
                </Radio.Group>
                <Slider defaultValue={30} />
                <Switch defaultChecked />
                <Rate defaultValue={3} />
              </Space>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <Title level={4}>Upload</Title>
              <Dragger
                name="file"
                multiple
                action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                onChange={(info) => {
                  const { status } = info.file
                  if (status === 'done') {
                    message.success(`${info.file.name} file uploaded successfully.`)
                  } else if (status === 'error') {
                    message.error(`${info.file.name} file upload failed.`)
                  }
                }}
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">Click or drag file to this area to upload</p>
                <p className="ant-upload-hint">
                  Support for a single or bulk upload. Strictly prohibit from uploading company data or other
                  band files
                </p>
              </Dragger>
            </Col>
          </Row>
        </Card>

        {/* Display Components */}
        <Card title="Display Components" style={{ marginBottom: '24px' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div>
              <Title level={4}>Avatars and Badges</Title>
              <Space size="large">
                <Avatar size={64} icon={<UserOutlined />} />
                <Avatar size="large" src="https://joeschmoe.io/api/v1/random" />
                <Badge count={5}>
                  <Avatar shape="square" size="large" />
                </Badge>
                <Badge dot>
                  <Avatar shape="square" size="large" />
                </Badge>
              </Space>
            </div>

            <div>
              <Title level={4}>Tags</Title>
              <Space size={[0, 8]} wrap>
                <Tag>Tag 1</Tag>
                <Tag color="magenta">magenta</Tag>
                <Tag color="red">red</Tag>
                <Tag color="volcano">volcano</Tag>
                <Tag color="orange">orange</Tag>
                <Tag color="gold">gold</Tag>
                <Tag color="lime">lime</Tag>
                <Tag color="green">green</Tag>
                <Tag color="cyan">cyan</Tag>
                <Tag color="blue">blue</Tag>
                <Tag color="geekblue">geekblue</Tag>
                <Tag color="purple">purple</Tag>
              </Space>
            </div>

            <div>
              <Title level={4}>Progress</Title>
              <Progress percent={30} />
              <Progress percent={50} status="active" />
              <Progress percent={70} status="exception" />
              <Progress percent={100} />
            </div>
          </Space>
        </Card>

        {/* Data Display */}
        <Card title="Data Display - Table" style={{ marginBottom: '24px' }}>
          <Table dataSource={dataSource} columns={columns} />
        </Card>

        {/* Feedback Components */}
        <Card title="Feedback Components" style={{ marginBottom: '24px' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div>
              <Title level={4}>Alerts</Title>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Alert message="Success Text" type="success" />
                <Alert message="Info Text" type="info" />
                <Alert message="Warning Text" type="warning" />
                <Alert message="Error Text" type="error" />
              </Space>
            </div>

            <div>
              <Title level={4}>Buttons</Title>
              <Space wrap>
                <Button type="primary">Primary Button</Button>
                <Button>Default Button</Button>
                <Button type="dashed">Dashed Button</Button>
                <Button type="text">Text Button</Button>
                <Button type="link">Link Button</Button>
                <Button type="primary" icon={<PlusOutlined />}>
                  Add
                </Button>
                <Button type="primary" icon={<DownloadOutlined />} size="large">
                  Download
                </Button>
                <Button danger>Danger Button</Button>
                <Button loading>Loading Button</Button>
                <Tooltip title="This is a tooltip">
                  <Button>Tooltip Button</Button>
                </Tooltip>
              </Space>
            </div>

            <div>
              <Title level={4}>Interactive Elements</Title>
              <Space wrap>
                <Button onClick={() => setIsModalVisible(true)}>Show Modal</Button>
                <Button onClick={() => setIsDrawerVisible(true)}>Show Drawer</Button>
                <Button onClick={showNotification}>Show Notification</Button>
                <Button onClick={showMessage}>Show Message</Button>
              </Space>
            </div>
          </Space>
        </Card>

        {/* Other Components */}
        <Card title="Other Components" style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Title level={4}>Collapse</Title>
              <Collapse defaultActiveKey={['1']}>
                <Panel header="This is panel header 1" key="1">
                  <p>Panel content goes here...</p>
                </Panel>
                <Panel header="This is panel header 2" key="2">
                  <p>Panel content goes here...</p>
                </Panel>
                <Panel header="This is panel header 3" key="3">
                  <p>Panel content goes here...</p>
                </Panel>
              </Collapse>
            </Col>
            <Col xs={24} md={12}>
              <Title level={4}>Empty State</Title>
              <Empty description="No data available" />
              <Divider />
              <Title level={4}>Loading</Title>
              <Spin size="large" />
            </Col>
          </Row>
        </Card>

        {/* Modal */}
        <Modal
          title="Basic Modal"
          visible={isModalVisible}
          onOk={() => setIsModalVisible(false)}
          onCancel={() => setIsModalVisible(false)}
        >
          <p>Some contents...</p>
          <p>Some contents...</p>
          <p>Some contents...</p>
        </Modal>

        {/* Drawer */}
        <Drawer
          title="Basic Drawer"
          placement="right"
          onClose={() => setIsDrawerVisible(false)}
          visible={isDrawerVisible}
        >
          <p>Some contents...</p>
          <p>Some contents...</p>
          <p>Some contents...</p>
        </Drawer>
      </div>
    </div>
  )
}

export default AntDesignShowcase
