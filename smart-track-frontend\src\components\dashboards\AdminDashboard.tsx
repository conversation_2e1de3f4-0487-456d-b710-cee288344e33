'use client';

import { useState } from 'react';
import { User } from '@/types';
import { 
  Layout, 
  Menu, 
  Card, 
  Row, 
  Col, 
  Progress, 
  Typography, 
  Timeline,
  Statistic,
  Space
} from 'antd';
import { 
  DashboardOutlined,
  UserOutlined,
  ShopOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import DashboardLayout from '@/components/layout/DashboardLayout';
import StatsCards from '@/components/dashboard/StatsCards';
import UserManagement from '@/components/admin/UserManagement';
import CustomerManagement from '@/components/admin/CustomerManagement';
import VisitManagement from '@/components/admin/VisitManagement';

const { Content, Sider } = Layout;
const { Title, Text } = Typography;

interface AdminDashboardProps {
  user: User;
}

type AdminTab = 'overview' | 'users' | 'customers' | 'visits';

export default function AdminDashboard({ user }: AdminDashboardProps) {
  const [activeTab, setActiveTab] = useState<AdminTab>('overview');

  const menuItems = [
    {
      key: 'overview',
      icon: <DashboardOutlined />,
      label: 'Overview',
    },
    {
      key: 'users',
      icon: <UserOutlined />,
      label: 'Users',
    },
    {
      key: 'customers',
      icon: <ShopOutlined />,
      label: 'Customers',
    },
    {
      key: 'visits',
      icon: <FileTextOutlined />,
      label: 'Visits',
    },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <StatsCards />
            <Row gutter={[24, 24]}>
              <Col xs={24} lg={12}>
                <Card title="Recent Activities" bordered={false}>
                  <Timeline
                    items={[
                      {
                        dot: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
                        children: (
                          <div>
                            <Text strong>New user registered</Text>
                            <br />
                            <Text type="secondary">2 hours ago</Text>
                          </div>
                        ),
                      },
                      {
                        dot: <ClockCircleOutlined style={{ color: '#1890ff' }} />,
                        children: (
                          <div>
                            <Text strong>Visit completed</Text>
                            <br />
                            <Text type="secondary">4 hours ago</Text>
                          </div>
                        ),
                      },
                      {
                        dot: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
                        children: (
                          <div>
                            <Text strong>Customer added</Text>
                            <br />
                            <Text type="secondary">6 hours ago</Text>
                          </div>
                        ),
                      },
                    ]}
                  />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="System Health" bordered={false}>
                  <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                    <div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                        <Text strong>Database</Text>
                        <Text style={{ color: '#52c41a' }}>Healthy</Text>
                      </div>
                      <Progress percent={95} status="success" showInfo={false} />
                    </div>
                    <div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                        <Text strong>API Response</Text>
                        <Text style={{ color: '#52c41a' }}>Fast</Text>
                      </div>
                      <Progress percent={88} status="success" showInfo={false} />
                    </div>
                    <div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                        <Text strong>Mobile App</Text>
                        <Text style={{ color: '#faad14' }}>Good</Text>
                      </div>
                      <Progress percent={78} status="active" showInfo={false} />
                    </div>
                  </Space>
                </Card>
              </Col>
            </Row>
          </Space>
        );
      case 'users':
        return <UserManagement />;
      case 'customers':
        return <CustomerManagement />;
      case 'visits':
        return <VisitManagement />;
      default:
        return null;
    }
  };

  return (
    <DashboardLayout user={user} title="Admin Dashboard">
      <Layout style={{ height: '100%' }}>
        <Sider width={256} style={{ background: '#fff' }}>
          <Menu
            mode="inline"
            selectedKeys={[activeTab]}
            style={{ height: '100%', borderRight: 0, paddingTop: 24 }}
            items={menuItems}
            onClick={({ key }) => setActiveTab(key as AdminTab)}
          />
        </Sider>
        <Layout style={{ padding: '24px', overflow: 'hidden' }}>
          <Content
            style={{
              padding: 0,
              margin: 0,
              minHeight: 280,
              background: '#f5f5f5',
              borderRadius: 8,
              overflow: 'auto',
              height: '100%',
            }}
          >
            <div style={{ padding: '24px', minHeight: '100%' }}>
              {renderContent()}
            </div>
          </Content>
        </Layout>
      </Layout>
    </DashboardLayout>
  );
}
