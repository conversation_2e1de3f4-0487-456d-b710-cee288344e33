[project]
name = "smart-track-backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "alembic>=1.16.4",
    "bcrypt>=4.2.1",
    "celery>=5.5.3",
    "fastapi>=0.116.1",
    "gunicorn>=23.0.0",
    "httpx>=0.28.1",
    "passlib[bcrypt]>=1.7.4",
    "psycopg2-binary>=2.9.10",
    "pydantic[email]>=2.11.7",
    "pydantic-settings>=2.10.1",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "python-jose>=3.5.0",
    "python-multipart>=0.0.20",
    "redis>=6.2.0",
    "sqlalchemy>=2.0.42",
    "structlog>=25.4.0",
    "uvicorn>=0.35.0",
    "pytest-cov>=6.2.1",
    "faker>=37.5.3",
]
