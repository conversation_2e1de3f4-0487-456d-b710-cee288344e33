from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import math

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.customer import Customer
from app.schemas.visit import (
    LocationValidation, LocationValidationResponse,
    NearbyCustomersResponse, NearbyCustomer
)

router = APIRouter()

def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """Calculate distance between two GPS coordinates in meters"""
    R = 6371000  # Earth's radius in meters
    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lon = math.radians(lon2 - lon1)
    
    a = (math.sin(delta_lat / 2) * math.sin(delta_lat / 2) +
         math.cos(lat1_rad) * math.cos(lat2_rad) *
         math.sin(delta_lon / 2) * math.sin(delta_lon / 2))
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    
    return R * c

@router.post("/validate", response_model=LocationValidationResponse)
async def validate_location(
    location_data: LocationValidation,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Validate GPS coordinates against customer location"""
    customer = db.query(Customer).filter(Customer.id == location_data.customer_id).first()
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    if not customer.latitude or not customer.longitude:
        return LocationValidationResponse(
            is_valid=True,  # Allow if customer location not set
            distance_meters=0,
            customer_latitude=0,
            customer_longitude=0,
            message="Customer location not available. Check-in allowed."
        )
    
    distance = calculate_distance(
        location_data.latitude, location_data.longitude,
        customer.latitude, customer.longitude
    )
    
    # Allow check-in within 500 meters
    is_valid = distance <= 500
    
    message = (
        f"You are {distance:.0f} meters from customer location."
        if is_valid else
        f"You are {distance:.0f} meters away. Please get within 500 meters to check in."
    )
    
    return LocationValidationResponse(
        is_valid=is_valid,
        distance_meters=distance,
        customer_latitude=customer.latitude,
        customer_longitude=customer.longitude,
        message=message
    )

@router.get("/nearby", response_model=NearbyCustomersResponse)
async def get_nearby_customers(
    latitude: float,
    longitude: float,
    radius_km: float = 5.0,
    limit: int = 20,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get nearby customers within specified radius"""
    # Get all customers with location data
    customers = db.query(Customer).filter(
        Customer.latitude.isnot(None),
        Customer.longitude.isnot(None)
    ).all()
    
    nearby_customers = []
    
    for customer in customers:
        distance = calculate_distance(
            latitude, longitude,
            customer.latitude, customer.longitude
        )
        
        # Convert radius from km to meters
        if distance <= (radius_km * 1000):
            nearby_customers.append(
                NearbyCustomer(
                    customer_id=customer.id,
                    customer_name=customer.name,
                    distance_meters=distance,
                    latitude=customer.latitude,
                    longitude=customer.longitude
                )
            )
    
    # Sort by distance and limit results
    nearby_customers.sort(key=lambda x: x.distance_meters)
    nearby_customers = nearby_customers[:limit]
    
    return NearbyCustomersResponse(
        customers=nearby_customers,
        total_count=len(nearby_customers)
    )
