'use client';

import { useState } from 'react';
import { 
  Layout, 
  Menu, 
  Avatar, 
  Typography, 
  Space, 
  Card,
  Divider
} from 'antd';
import {
  TeamOutlined,
  CalendarOutlined,
  EnvironmentOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { User } from '@/types';
import TeamOverview from '@/components/supervisor/TeamOverview';
import WeeklyPlanManager from '@/components/supervisor/WeeklyPlanManager';
import VisitTracking from '@/components/supervisor/VisitTracking';
import styles from './SupervisorDashboard.module.css';
import '@/styles/supervisor-dashboard-global.css';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

interface SupervisorDashboardProps {
  user: User;
}

type SupervisorTab = 'team' | 'weekly-plans' | 'visits';

export default function SupervisorDashboard({ user }: SupervisorDashboardProps) {
  const [activeTab, setActiveTab] = useState<SupervisorTab>('team');

  const menuItems = [
    {
      key: 'team',
      icon: <TeamOutlined />,
      label: 'My Team',
    },
    {
      key: 'weekly-plans',
      icon: <CalendarOutlined />,
      label: 'Weekly Plans',
    },
    {
      key: 'visits',
      icon: <EnvironmentOutlined />,
      label: 'Visit Tracking',
    },
  ];

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      danger: true,
    },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'team':
        return <TeamOverview user={user} />;
      case 'weekly-plans':
        return <WeeklyPlanManager user={user} />;
      case 'visits':
        return <VisitTracking user={user} />;
      default:
        return null;
    }
  };

  return (
    <Layout className={styles.supervisorDashboard}>
      {/* Enhanced Header */}
      <Header className={styles.headerContainer}>
        <div className={styles.headerContent}>
          {/* Logo and Title */}
          <div className={styles.logoSection}>
            <DashboardOutlined className={styles.logoIcon} />
            <div className={styles.titleSection}>
              <Title level={4} className={styles.mainTitle}>
                Smart Track
              </Title>
              <Text className={styles.subtitle}>
                Supervisor Dashboard
              </Text>
            </div>
          </div>

          {/* Header Info */}
          <div className={styles.headerInfo}>
            <Text className={styles.welcomeText}>
              Welcome back, {user.full_name}
            </Text>
          </div>
        </div>
      </Header>

      <Layout>
        {/* Enhanced Sidebar */}
        <Sider 
          width={280}
          className={styles.siderContainer}
        >
          {/* User Info Card */}
          <Card 
            className={styles.userCard}
            bordered={false}
          >
            <div className={styles.userCardBody}>
              <Avatar 
                size={64} 
                icon={<UserOutlined />}
                className={styles.userAvatar}
              />
              <Title level={5} className={styles.userCardName}>
                {user.full_name}
              </Title>
              <Text type="secondary" className={styles.userCardEmail}>
                {user.email}
              </Text>
            </div>
          </Card>

          <Divider style={{ margin: '0 16px 16px 16px' }} />

          {/* Navigation Menu */}
          <Menu
            mode="vertical"
            selectedKeys={[activeTab]}
            onClick={({ key }) => setActiveTab(key as SupervisorTab)}
            items={menuItems.map(item => ({
              ...item,
              className: styles.menuItem
            }))}
            className={styles.menuContainer}
          />

          {/* Quick Stats Card */}
          <div className={styles.quickStats}>
            <Card 
              size="small" 
              className={styles.quickStatsCard}
              title={
                <span style={{ color: 'white' }}>Quick Stats</span>
              }
              headStyle={{ 
                background: 'linear-gradient(135deg, #52c41a 0%, #73d13d 100%)',
                borderRadius: '8px 8px 0 0'
              }}
            >
              <div className={styles.statItem}>
                <span>Team Members:</span>
                <span className={`${styles.statValue} ${styles.statValueSuccess}`}>5</span>
              </div>
              <Divider style={{ margin: '8px 0' }} />
              <div className={styles.statItem}>
                <span>Active Visits:</span>
                <span className={`${styles.statValue} ${styles.statValueSuccess}`}>12</span>
              </div>
              <Divider style={{ margin: '8px 0' }} />
              <div className={styles.statItem}>
                <span>Completion Rate:</span>
                <span className={`${styles.statValue} ${styles.statValueSuccess}`}>85%</span>
              </div>
            </Card>
          </div>
        </Sider>

        {/* Main Content */}
        <Content className={styles.contentArea}>
          <div className={styles.contentPadding}>
            {renderContent()}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
}
