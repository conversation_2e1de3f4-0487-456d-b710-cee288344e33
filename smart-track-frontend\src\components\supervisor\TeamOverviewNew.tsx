'use client';

import { useState, useEffect } from 'react';
import { User, Visit } from '@/types';
import { api } from '@/services/api';
import { 
  Card, 
  Row, 
  Col, 
  Avatar, 
  Progress, 
  Button, 
  Typography, 
  Statistic, 
  List, 
  Badge, 
  Space, 
  Skeleton,
  Empty,
  Tag,
  Tooltip,
  Collapse,
  Divider
} from 'antd';
import { 
  UserOutlined, 
  TeamOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined,
  ReloadOutlined,
  PlusOutlined,
  CalendarOutlined,
  EnvironmentOutlined,
  EyeOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Panel } = Collapse;

interface TeamMemberStats {
  user: User;
  totalVisits: number;
  completedVisits: number;
  pendingVisits: number;
  completionRate: number;
  lastVisitDate: string | null;
  recentActivity: Visit[];
}

interface TeamOverviewProps {
  user?: User;
}

export default function TeamOverview({ user: propUser }: TeamOverviewProps) {
  const [team, setTeam] = useState<User[]>([]);
  const [teamStats, setTeamStats] = useState<TeamMemberStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMember, setSelectedMember] = useState<number | null>(null);

  // Use prop user if available
  const user = propUser;

  useEffect(() => {
    // Only fetch data if user is available
    if (user?.id) {
      fetchTeamData();
    } else {
      setLoading(false);
    }
  }, [user?.id]);

  const fetchTeamData = async () => {
    if (!user?.id) {
      console.error('No user ID available');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      // Use the actual logged-in supervisor's ID
      const supervisorId = user.id;
      console.log(`🔍 Fetching team data for supervisor ID: ${supervisorId} (username: ${user.username || 'unknown'})`);
      
      // Fetch team members using the new endpoint
      const teamResponse = await api.getSupervisorTeam(supervisorId);
      console.log('✅ Team response received:', teamResponse.data);
      setTeam(teamResponse.data);

      // Fetch stats for each team member
      const statsPromises = teamResponse.data.map(async (member: User) => {
        try {
          // Get visits for this member
          const visitsResponse = await api.getVisits(1, 100, undefined, undefined);
          const memberVisits = visitsResponse.data.filter(visit => visit.user_id === member.id);
          
          const totalVisits = memberVisits.length;
          const completedVisits = memberVisits.filter(visit => visit.status === 'completed').length;
          const pendingVisits = memberVisits.filter(visit => visit.status === 'planned').length;
          const completionRate = totalVisits > 0 ? (completedVisits / totalVisits) * 100 : 0;
          
          // Get last visit date
          const sortedVisits = memberVisits.sort((a, b) => 
            new Date(b.planned_date).getTime() - new Date(a.planned_date).getTime()
          );
          const lastVisitDate = sortedVisits.length > 0 ? sortedVisits[0].planned_date : null;
          
          // Get recent activity (last 5 visits)
          const recentActivity = sortedVisits.slice(0, 5);

          return {
            user: member,
            totalVisits,
            completedVisits,
            pendingVisits,
            completionRate,
            lastVisitDate,
            recentActivity
          };
        } catch (error) {
          console.error(`Error fetching stats for member ${member.id}:`, error);
          return {
            user: member,
            totalVisits: 0,
            completedVisits: 0,
            pendingVisits: 0,
            completionRate: 0,
            lastVisitDate: null,
            recentActivity: []
          };
        }
      });

      const stats = await Promise.all(statsPromises);
      setTeamStats(stats);
    } catch (error: any) {
      console.error('❌ Error fetching team data:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
        
        // Handle specific error cases
        if (error.response.status === 401) {
          console.error('Authentication failed - user may need to login again');
        } else if (error.response.status === 403) {
          console.error('Access forbidden - user may not have supervisor permissions');
        } else if (error.response.status === 404) {
          console.error('Endpoint not found - check API endpoint configuration');
        }
      }
      setTeam([]);
      setTeamStats([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'processing';
      case 'planned': return 'warning';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Completed';
      case 'in_progress': return 'In Progress';
      case 'planned': return 'Planned';
      case 'cancelled': return 'Cancelled';
      default: return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderTeamStats = () => {
    const totalTeamMembers = teamStats.length;
    const totalVisits = teamStats.reduce((sum, stats) => sum + stats.totalVisits, 0);
    const completedVisits = teamStats.reduce((sum, stats) => sum + stats.completedVisits, 0);
    const pendingVisits = teamStats.reduce((sum, stats) => sum + stats.pendingVisits, 0);
    const avgCompletionRate = totalTeamMembers > 0 
      ? teamStats.reduce((sum, stats) => sum + stats.completionRate, 0) / totalTeamMembers 
      : 0;

    return (
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Team Members"
              value={totalTeamMembers}
              prefix={<TeamOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Visits"
              value={totalVisits}
              prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Completed"
              value={completedVisits}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Avg. Completion"
              value={Math.round(avgCompletionRate)}
              suffix="%"
              prefix={<ClockCircleOutlined style={{ color: '#fa8c16' }} />}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  const renderTeamMemberCard = (memberStats: TeamMemberStats) => {
    const { user: member, totalVisits, completedVisits, pendingVisits, completionRate, lastVisitDate, recentActivity } = memberStats;
    
    return (
      <Card 
        key={member.id}
        hoverable
        style={{ marginBottom: 16 }}
        actions={[
          <Tooltip title="View Details" key="view">
            <Button 
              type="text" 
              icon={<EyeOutlined />}
              onClick={() => setSelectedMember(selectedMember === member.id ? null : member.id)}
            />
          </Tooltip>
        ]}
      >
        <Card.Meta
          avatar={
            <Avatar 
              size={64} 
              style={{ backgroundColor: '#1890ff' }}
              icon={<UserOutlined />}
            >
              {member.full_name?.split(' ').map(n => n[0]).join('') || 'UN'}
            </Avatar>
          }
          title={
            <Space direction="vertical" size={4}>
              <Text strong style={{ fontSize: 16 }}>{member.full_name}</Text>
              <Text type="secondary" style={{ fontSize: 12 }}>{member.email}</Text>
              {member.sap_code && (
                <Text type="secondary" style={{ fontSize: 11 }}>ID: {member.sap_code}</Text>
              )}
            </Space>
          }
          description={
            <Space direction="vertical" size={8} style={{ width: '100%' }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="Total"
                    value={totalVisits}
                    valueStyle={{ fontSize: 16 }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="Done"
                    value={completedVisits}
                    valueStyle={{ fontSize: 16, color: '#52c41a' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="Pending"
                    value={pendingVisits}
                    valueStyle={{ fontSize: 16, color: '#fa8c16' }}
                  />
                </Col>
              </Row>
              
              <div>
                <Text type="secondary" style={{ fontSize: 12, marginBottom: 4, display: 'block' }}>
                  Completion Rate
                </Text>
                <Progress 
                  percent={Math.round(completionRate)} 
                  size="small"
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </div>

              {lastVisitDate && (
                <Text type="secondary" style={{ fontSize: 12 }}>
                  Last Activity: {formatDate(lastVisitDate)}
                </Text>
              )}
            </Space>
          }
        />
        
        {selectedMember === member.id && (
          <div style={{ marginTop: 16 }}>
            <Divider orientation="left">Recent Activity</Divider>
            {recentActivity.length === 0 ? (
              <Empty 
                description="No recent activity"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : (
              <List
                size="small"
                dataSource={recentActivity}
                renderItem={(visit) => (
                  <List.Item key={visit.id}>
                    <List.Item.Meta
                      avatar={<EnvironmentOutlined style={{ color: '#1890ff' }} />}
                      title={
                        <Space>
                          <Text strong>{visit.customer?.name || 'Unknown Customer'}</Text>
                          <Tag color={getStatusColor(visit.status)}>
                            {getStatusText(visit.status)}
                          </Tag>
                        </Space>
                      }
                      description={
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {formatDate(visit.planned_date)}
                        </Text>
                      }
                    />
                  </List.Item>
                )}
              />
            )}
          </div>
        )}
      </Card>
    );
  };

  if (loading) {
    return (
      <div style={{ padding: 24 }}>
        <Title level={2}>My Team Dashboard</Title>
        <Row gutter={[16, 16]}>
          {[1, 2, 3, 4].map(i => (
            <Col xs={24} sm={12} md={6} key={i}>
              <Card>
                <Skeleton active />
              </Card>
            </Col>
          ))}
        </Row>
        <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
          {[1, 2, 3].map(i => (
            <Col xs={24} sm={12} lg={8} key={i}>
              <Card>
                <Skeleton active avatar />
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      {/* Header */}
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <Title level={2} style={{ margin: 0 }}>
            My Team Dashboard
          </Title>
          <Text type="secondary">
            Monitor your team's performance and activity
          </Text>
        </Col>
        <Col>
          <Space>
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchTeamData}
              loading={loading}
            >
              Refresh
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
            >
              Add Member
            </Button>
          </Space>
        </Col>
      </Row>

      {teamStats.length === 0 ? (
        <Card style={{ textAlign: 'center', padding: 48 }}>
          <Empty
            description={
              <div>
                <Title level={4}>No Team Members</Title>
                <Text type="secondary">
                  No team members are currently assigned to this supervisor.
                </Text>
              </div>
            }
          >
            <Button type="primary" icon={<PlusOutlined />}>
              Assign Team Members
            </Button>
          </Empty>
        </Card>
      ) : (
        <div>
          {/* Team Statistics */}
          {renderTeamStats()}

          {/* Team Members Grid */}
          <Title level={3} style={{ marginBottom: 16 }}>
            Team Members ({teamStats.length})
          </Title>
          
          <Row gutter={[16, 16]}>
            {teamStats.map((memberStats) => (
              <Col xs={24} sm={12} lg={8} key={memberStats.user.id}>
                {renderTeamMemberCard(memberStats)}
              </Col>
            ))}
          </Row>
        </div>
      )}
    </div>
  );
}
