from pydantic_settings import BaseSettings
from pydantic import ConfigDict
from typing import List
from urllib.parse import quote_plus
import os
import json

class Settings(BaseSettings):
    model_config = ConfigDict(
        env_file=".env",
        case_sensitive=True,
        extra="ignore"  # Ignore extra environment variables
    )
    # Database
    DATABASE_HOSTNAME: str = "localhost"
    DATABASE_PORT: int = 5432
    DATABASE_USERNAME: str
    DATABASE_PASSWORD: str
    DATABASE_NAME: str
    
    
    
    # Redis
    # REDIS_URL: str = "redis://localhost:6379/0"
    
    # JWT Authentication
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # File Upload
    MAX_FILE_SIZE: int = 10485760  # 10MB
    UPLOAD_DIR: str = "uploads"
    
    # CORS
    ALLOWED_ORIGINS: str = '["http://localhost:3000", "http://localhost:8081", "https://smarttrack-dev.acisio.com", "https://smarttrack-api-dev.acisio.com", "*"]'
    
    @property
    def parsed_allowed_origins(self) -> List[str]:
        """Parse ALLOWED_ORIGINS from JSON string to list"""
        try:
            return json.loads(self.ALLOWED_ORIGINS)
        except json.JSONDecodeError:
            # Fallback to splitting by comma if not valid JSON
            return [origin.strip().strip('"\'') for origin in self.ALLOWED_ORIGINS.split(',')]
    
    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # SAP Integration
    SAP_BASE_URL: str = ""
    SAP_USERNAME: str = ""
    SAP_PASSWORD: str = ""

    @property
    def DATABASE_URL(self) -> str:
        """Construct the database URL from individual components"""
        password = quote_plus(self.DATABASE_PASSWORD)
        return f"postgresql+psycopg2://{self.DATABASE_USERNAME}:{password}@{self.DATABASE_HOSTNAME}:{self.DATABASE_PORT}/{self.DATABASE_NAME}"

settings = Settings()
