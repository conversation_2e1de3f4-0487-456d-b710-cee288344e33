import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  TextInput,
  Button,
  List,
  Chip,
  Surface,
  ActivityIndicator,
  IconButton,
} from 'react-native-paper';
import DateTimePicker from '@react-native-community/datetimepicker';
import { MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { visitService } from '../../services/visitService.js';
import { useNavigation } from '@react-navigation/native';

const CreateVisitScreen = ({ navigation }) => {
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [plannedDate, setPlannedDate] = useState(new Date());
  const [purpose, setPurpose] = useState('');
  const [notes, setNotes] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showCustomerSelector, setShowCustomerSelector] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [customerSearchQuery, setCustomerSearchQuery] = useState('');

  useEffect(() => {
    loadCustomers();
  }, []);

  const loadCustomers = async () => {
    try {
      // This would typically call an API to get customers
      // For now, we'll use mock data
      const mockCustomers = [
        { id: 1, name: 'ABC Electronics', address: '123 Main St', city: 'New York' },
        { id: 2, name: 'XYZ Store', address: '456 Oak Ave', city: 'Los Angeles' },
        { id: 3, name: 'Tech Solutions Inc', address: '789 Pine Rd', city: 'Chicago' },
      ];
      setCustomers(mockCustomers);
    } catch (error) {
      console.error('Failed to load customers:', error);
    }
  };

  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      const newDate = new Date(plannedDate);
      newDate.setFullYear(selectedDate.getFullYear());
      newDate.setMonth(selectedDate.getMonth());
      newDate.setDate(selectedDate.getDate());
      setPlannedDate(newDate);
    }
  };

  const handleTimeChange = (event, selectedTime) => {
    setShowTimePicker(false);
    if (selectedTime) {
      const newDate = new Date(plannedDate);
      newDate.setHours(selectedTime.getHours());
      newDate.setMinutes(selectedTime.getMinutes());
      setPlannedDate(newDate);
    }
  };

  const handleCustomerSelect = (customer) => {
    setSelectedCustomer(customer);
    setShowCustomerSelector(false);
    setCustomerSearchQuery('');
  };

  const createVisit = async () => {
    if (!selectedCustomer) {
      Alert.alert('Error', 'Please select a customer');
      return;
    }

    if (!purpose.trim()) {
      Alert.alert('Error', 'Please enter a visit purpose');
      return;
    }

    try {
      setLoading(true);
      
      const visitData = {
        customer_id: selectedCustomer.id,
        planned_date: plannedDate.toISOString(),
        purpose: purpose.trim(),
        notes: notes.trim() || undefined,
      };

      const newVisit = await visitService.createVisit(visitData);
      
      Alert.alert(
        'Success',
        'Visit created successfully!',
        [
          {
            text: 'View Details',
            onPress: () => {
              navigation.replace('VisitDetail', { visitId: newVisit.id });
            },
          },
          {
            text: 'Create Another',
            onPress: () => {
              // Reset form
              setSelectedCustomer(null);
              setPurpose('');
              setNotes('');
              setPlannedDate(new Date());
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to create visit. Please try again.');
      console.error('Create visit error:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(customerSearchQuery.toLowerCase()) ||
    customer.city?.toLowerCase().includes(customerSearchQuery.toLowerCase())
  );

  const purposeSuggestions = [
    'Sales Meeting',
    'Product Demo',
    'Order Collection',
    'Payment Follow-up',
    'Customer Support',
    'Market Research',
    'Relationship Building',
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.sectionTitle}>Visit Details</Title>

          {/* Customer Selection */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Customer *</Text>
            <TouchableOpacity
              style={styles.customerSelector}
              onPress={() => setShowCustomerSelector(true)}
            >
              {selectedCustomer ? (
                <View style={styles.selectedCustomer}>
                  <View style={styles.customerInfo}>
                    <Text style={styles.customerName}>{selectedCustomer.name}</Text>
                    {selectedCustomer.address && (
                      <Text style={styles.customerAddress}>
                        {selectedCustomer.address}, {selectedCustomer.city}
                      </Text>
                    )}
                  </View>
                  <MaterialIcons name="chevron-right" size={24} color="#666" />
                </View>
              ) : (
                <View style={styles.customerPlaceholder}>
                  <Text style={styles.placeholderText}>Select a customer</Text>
                  <MaterialIcons name="chevron-right" size={24} color="#666" />
                </View>
              )}
            </TouchableOpacity>
          </View>

          {/* Date and Time Selection */}
          <View style={styles.dateTimeContainer}>
            <View style={styles.dateTimeSection}>
              <Text style={styles.inputLabel}>Date *</Text>
              <TouchableOpacity
                style={styles.dateTimeButton}
                onPress={() => setShowDatePicker(true)}
              >
                <MaterialIcons name="calendar-today" size={20} color="#2196F3" />
                <Text style={styles.dateTimeText}>
                  {format(plannedDate, 'MMM dd, yyyy')}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.dateTimeSection}>
              <Text style={styles.inputLabel}>Time *</Text>
              <TouchableOpacity
                style={styles.dateTimeButton}
                onPress={() => setShowTimePicker(true)}
              >
                <MaterialIcons name="access-time" size={20} color="#2196F3" />
                <Text style={styles.dateTimeText}>
                  {format(plannedDate, 'HH:mm')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Purpose */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Purpose *</Text>
            <TextInput
              value={purpose}
              onChangeText={setPurpose}
              placeholder="Enter visit purpose"
              style={styles.textInput}
              multiline
            />
            
            <View style={styles.suggestionsContainer}>
              <Text style={styles.suggestionsLabel}>Quick Select:</Text>
              <View style={styles.suggestionsGrid}>
                {purposeSuggestions.map((suggestion, index) => (
                  <Chip
                    key={index}
                    mode="outlined"
                    onPress={() => setPurpose(suggestion)}
                    style={styles.suggestionChip}
                  >
                    {suggestion}
                  </Chip>
                ))}
              </View>
            </View>
          </View>

          {/* Notes */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Notes (Optional)</Text>
            <TextInput
              value={notes}
              onChangeText={setNotes}
              placeholder="Additional notes or preparation details"
              style={[styles.textInput, styles.notesInput]}
              multiline
              numberOfLines={3}
            />
          </View>
        </Card.Content>
      </Card>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <Button
          mode="contained"
          onPress={createVisit}
          loading={loading}
          disabled={loading || !selectedCustomer || !purpose.trim()}
          style={styles.createButton}
        >
          Create Visit
        </Button>

        <Button
          mode="outlined"
          onPress={() => navigation.goBack()}
          disabled={loading}
          style={styles.cancelButton}
        >
          Cancel
        </Button>
      </View>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={plannedDate}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={new Date()}
        />
      )}

      {/* Time Picker */}
      {showTimePicker && (
        <DateTimePicker
          value={plannedDate}
          mode="time"
          display="default"
          onChange={handleTimeChange}
        />
      )}

      {/* Customer Selection Modal */}
      <Modal
        visible={showCustomerSelector}
        animationType="slide"
        transparent
        onRequestClose={() => setShowCustomerSelector(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.customerModal}>
            <View style={styles.modalHeader}>
              <Title>Select Customer</Title>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setShowCustomerSelector(false)}
                style={styles.closeButton}
              />
            </View>

            <TextInput
              placeholder="Search customers..."
              value={customerSearchQuery}
              onChangeText={setCustomerSearchQuery}
              style={styles.searchInput}
              left={<TextInput.Icon icon="magnify" />}
            />

            <ScrollView style={styles.customerList}>
              {filteredCustomers.map((customer) => (
                <List.Item
                  key={customer.id}
                  title={customer.name}
                  description={customer.address ? `${customer.address}, ${customer.city}` : customer.city}
                  onPress={() => handleCustomerSelect(customer)}
                  style={styles.customerItem}
                  left={(props) => <List.Icon {...props} icon="account" />}
                />
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  card: {
    margin: 16,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  textInput: {
    backgroundColor: 'white',
  },
  notesInput: {
    minHeight: 80,
  },
  customerSelector: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: 'white',
    padding: 16,
  },
  selectedCustomer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  customerInfo: {
    flex: 1,
  },
  customerName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  customerAddress: {
    fontSize: 14,
    color: '#666',
  },
  customerPlaceholder: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
  },
  dateTimeContainer: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 20,
  },
  dateTimeSection: {
    flex: 1,
  },
  dateTimeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 16,
    backgroundColor: 'white',
  },
  dateTimeText: {
    fontSize: 16,
    marginLeft: 8,
    color: '#333',
  },
  suggestionsContainer: {
    marginTop: 12,
  },
  suggestionsLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  suggestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  suggestionChip: {
    marginBottom: 4,
  },
  actionContainer: {
    padding: 16,
    gap: 12,
  },
  createButton: {
    backgroundColor: '#4CAF50',
  },
  cancelButton: {
    borderColor: '#666',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  customerModal: {
    backgroundColor: 'white',
    borderRadius: 12,
    margin: 20,
    maxHeight: '80%',
    width: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  closeButton: {
    padding: 4,
  },
  searchInput: {
    margin: 16,
    backgroundColor: 'white',
  },
  customerList: {
    maxHeight: 300,
  },
  customerItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
});

export default CreateVisitScreen;
