"""
Test API endpoints functionality
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from app.main import app

def test_users_endpoint_protection():
    """Test that users endpoints are protected"""
    client = TestClient(app)
    
    # Test getting users without authentication
    response = client.get("/api/v1/users/")
    assert response.status_code in [401, 403]
    
    # Test getting current user without authentication
    response = client.get("/api/v1/users/me")
    assert response.status_code in [401, 403]

def test_customers_endpoint_protection():
    """Test that customer endpoints are protected"""
    client = TestClient(app)
    
    # Test getting customers without authentication
    response = client.get("/api/v1/customers/")
    assert response.status_code in [401, 403]

def test_visits_endpoint_protection():
    """Test that visit endpoints are protected"""
    client = TestClient(app)
    
    # Test getting visits without authentication
    response = client.get("/api/v1/visits/")
    assert response.status_code in [401, 403]
    
    # Test creating visit without authentication
    response = client.post("/api/v1/visits/", json={
        "customer_id": 1,
        "planned_date": "2025-08-04T10:00:00"
    })
    assert response.status_code in [401, 403]

def test_locations_endpoint_exists():
    """Test that location endpoints exist"""
    client = TestClient(app)
    
    # Test location validation endpoint (should require auth)
    response = client.post("/api/v1/locations/validate", json={
        "latitude": 40.7128,
        "longitude": -74.0060
    })
    assert response.status_code in [401, 403, 422]  # Not 404

def test_api_router_structure():
    """Test that API router is properly structured"""
    client = TestClient(app)
    
    # Test that v1 API prefix works
    response = client.get("/api/v1/")
    # Should not return 404 (router exists)
    assert response.status_code != 404
    
def test_invalid_endpoints():
    """Test invalid endpoints return 404"""
    client = TestClient(app)
    
    # Test non-existent endpoint
    response = client.get("/api/v1/nonexistent")
    assert response.status_code == 404
    
    # Test invalid API version
    response = client.get("/api/v2/users")
    assert response.status_code == 404

def test_dashboard_endpoints_exist():
    """Test that dashboard endpoints exist (even if protected)"""
    client = TestClient(app)
    
    # Test dashboard stats endpoint (should require auth, not 404)
    response = client.get("/api/v1/dashboard/stats")
    assert response.status_code in [401, 403, 422]  # Not 404
    
    # Test main dashboard endpoint (should require auth, not 404)
    response = client.get("/api/v1/dashboard/")
    assert response.status_code in [401, 403, 422]  # Not 404
