'use client';

import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Modal, 
  Form, 
  Input, 
  Button, 
  message, 
  Row, 
  Col, 
  InputNumber, 
  Select, 
  Switch 
} from 'antd';
import { 
  UserOutlined, 
  PhoneOutlined, 
  MailOutlined, 
  HomeOutlined,
  BankOutlined,
  DollarOutlined
} from '@ant-design/icons';

import { apiService } from '../../services/api';
import { Customer } from '../../types';

interface AddCustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface CustomerFormData {
  code: string;
  name: string;
  customer_group?: string;
  address?: string;
  city?: string;
  province?: string;
  region?: string;
  phone?: string;
  email?: string;
  customer_type?: string;
  credit_limit?: number;
  current_balance?: number;
  customer_statistics_group?: string;
  sdst?: string;
  latitude?: number;
  longitude?: number;
  is_active: boolean;
}

const customerTypes = [
  { value: 'dealer', label: 'Dealer' },
  { value: 'distributor', label: 'Distributor' },
  { value: 'retailer', label: 'Retailer' },
  { value: 'wholesale', label: 'Wholesale' },
  { value: 'end_user', label: 'End User' },
];

export default function AddCustomerModal({ isOpen, onClose, onSuccess }: AddCustomerModalProps) {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const createCustomerMutation = useMutation({
    mutationFn: (customerData: CustomerFormData) => {
      console.log('🚀 CreateCustomer mutation called with data:', customerData);
      console.log('🚀 customerData.is_active type:', typeof customerData.is_active);
      console.log('🚀 customerData.is_active value:', customerData.is_active);
      
      // Let's also log the entire payload being sent
      const payload = {
        code: customerData.code,
        name: customerData.name,
        customer_group: customerData.customer_group,
        address: customerData.address,
        city: customerData.city,
        province: customerData.province,
        region: customerData.region,
        phone: customerData.phone,
        email: customerData.email,
        customer_type: customerData.customer_type,
        credit_limit: customerData.credit_limit || 0,
        current_balance: customerData.current_balance || 0,
        customer_statistics_group: customerData.customer_statistics_group,
        sdst: customerData.sdst,
        latitude: customerData.latitude,
        longitude: customerData.longitude,
        is_active: customerData.is_active
      };
      console.log('🚀 Complete payload being sent to API:', payload);
      
      return apiService.createCustomer(payload);
    },
    onSuccess: (response) => {
      console.log('✅ Customer created successfully:', response.data);
      console.log('🔄 Invalidating all customer queries...');
      
      // Clear all customer-related queries
      queryClient.removeQueries({ 
        queryKey: ['customers'],
        exact: false 
      });
      
      // Invalidate queries to trigger refetch
      queryClient.invalidateQueries({ 
        queryKey: ['customers'],
        exact: false 
      });
      
      // Force refetch all customer queries
      queryClient.refetchQueries({ 
        queryKey: ['customers'],
        exact: false 
      });
      
      message.success('Customer created successfully!');
      handleClose();
      onSuccess();
    },
    onError: (error: any) => {
      console.error('❌ Customer creation failed:', error);
      if (error.response?.data?.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error('Failed to create customer');
      }
      setLoading(false);
    },
  });

  const handleClose = () => {
    form.resetFields();
    setLoading(false);
    onClose();
  };

  const onFinish = async (values: CustomerFormData) => {
    try {
      setLoading(true);
      console.log('📝 Form submitted with values:', values);
      createCustomerMutation.mutate(values);
    } catch (error) {
      console.error('❌ Form submission error:', error);
      setLoading(false);
    }
  };

  return (
    <Modal
      title="Add New Customer"
      open={isOpen}
      onCancel={handleClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          is_active: true,
          credit_limit: 0,
          current_balance: 0,
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Customer Code"
              name="code"
              rules={[
                { required: true, message: 'Please enter customer code!' },
                { min: 2, message: 'Code must be at least 2 characters' }
              ]}
            >
              <Input 
                prefix={<BankOutlined />} 
                placeholder="Enter customer code"
                autoFocus
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Customer Name"
              name="name"
              rules={[
                { required: true, message: 'Please enter customer name!' },
                { min: 2, message: 'Name must be at least 2 characters' }
              ]}
            >
              <Input 
                prefix={<UserOutlined />} 
                placeholder="Enter customer name"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Email"
              name="email"
              rules={[
                { type: 'email', message: 'Please enter a valid email!' }
              ]}
            >
              <Input 
                prefix={<MailOutlined />} 
                placeholder="Enter email address"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Phone"
              name="phone"
            >
              <Input 
                prefix={<PhoneOutlined />} 
                placeholder="Enter phone number"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label="Address"
              name="address"
            >
              <Input 
                prefix={<HomeOutlined />} 
                placeholder="Enter full address"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label="City"
              name="city"
            >
              <Input placeholder="Enter city" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Province"
              name="province"
            >
              <Input placeholder="Enter province" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Region"
              name="region"
            >
              <Input placeholder="Enter region" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Customer Type"
              name="customer_type"
            >
              <Select 
                placeholder="Select customer type"
                options={customerTypes}
                allowClear
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Customer Group"
              name="customer_group"
            >
              <Input placeholder="Enter customer group" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Credit Limit"
              name="credit_limit"
            >
              <InputNumber 
                prefix={<DollarOutlined />}
                placeholder="0.00"
                min={0}
                precision={2}
                style={{ width: '100%' }}
                formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={((value: string | undefined) => {
                  const parsed = parseFloat(value!.replace(/\$\s?|(,*)/g, ''));
                  return isNaN(parsed) ? 0 : parsed;
                }) as any}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Current Balance"
              name="current_balance"
            >
              <InputNumber 
                prefix={<DollarOutlined />}
                placeholder="0.00"
                min={0}
                precision={2}
                style={{ width: '100%' }}
                formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={((value: string | undefined) => {
                  const parsed = parseFloat(value!.replace(/\$\s?|(,*)/g, ''));
                  return isNaN(parsed) ? 0 : parsed;
                }) as any}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Statistics Group"
              name="customer_statistics_group"
            >
              <Input placeholder="Enter statistics group" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="SDST"
              name="sdst"
            >
              <Input placeholder="Enter SDST" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Latitude"
              name="latitude"
            >
              <InputNumber 
                placeholder="0.000000"
                precision={6}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Longitude"
              name="longitude"
            >
              <InputNumber 
                placeholder="0.000000"
                precision={6}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label="Active Customer"
          name="is_active"
          valuePropName="checked"
        >
          <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, marginTop: 24 }}>
          <Row gutter={8} justify="end">
            <Col>
              <Button onClick={handleClose}>
                Cancel
              </Button>
            </Col>
            <Col>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                disabled={loading}
              >
                Create Customer
              </Button>
            </Col>
          </Row>
        </Form.Item>
      </Form>
    </Modal>
  );
}
