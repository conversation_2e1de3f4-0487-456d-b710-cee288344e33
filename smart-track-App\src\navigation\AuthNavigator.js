import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import LoginScreen from '../screens/LoginScreen';

const AuthStack = createStackNavigator();

const AuthNavigator = () => {
  return (
    <AuthStack.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      <AuthStack.Screen 
        name="Login" 
        component={LoginScreen}
      />
      {/* Future auth screens can be added here:
      <AuthStack.Screen 
        name="Register" 
        component={RegisterScreen}
      />
      <AuthStack.Screen 
        name="ForgotPassword" 
        component={ForgotPasswordScreen}
      />
      */}
    </AuthStack.Navigator>
  );
};

export default AuthNavigator;
