'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { User, Visit } from '@/types';
import { api } from '@/services/api';
import { useAuthStore } from '@/store/auth';

interface VisitWithUser extends Visit {
  user?: User;
}

export default function VisitTracking() {
  const { user } = useAuthStore();
  const [visits, setVisits] = useState<VisitWithUser[]>([]);
  const [team, setTeam] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'planned' | 'in_progress' | 'completed' | 'cancelled'>('all');
  const [selectedUser, setSelectedUser] = useState<number | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    if (!user?.id) {
      console.error('No user ID available');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      // Fetch team members
      const teamResponse = await api.getSupervisorTeam(user.id);
      setTeam(teamResponse.data);

      // Fetch all visits
      const visitsResponse = await api.getVisits(1, 1000);
      
      // Filter visits for team members only
      const teamMemberIds = teamResponse.data.map((member: User) => member.id);
      const teamVisits = visitsResponse.data
        .filter((visit: Visit) => teamMemberIds.includes(visit.user_id))
        .map((visit: Visit) => ({
          ...visit,
          user: teamResponse.data.find((member: User) => member.id === visit.user_id)
        }));

      setVisits(teamVisits);
    } catch (error) {
      console.error('Error fetching visit tracking data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredVisits = visits.filter(visit => {
    if (filter !== 'all' && visit.status !== filter) return false;
    if (selectedUser && visit.user_id !== selectedUser) return false;
    return true;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'planned': return 'text-yellow-600 bg-yellow-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-semibold mb-4">Visit Tracking</h2>
      
      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Status Filter
          </label>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">All Statuses</option>
            <option value="planned">Planned</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Team Member
          </label>
          <select
            value={selectedUser || ''}
            onChange={(e) => setSelectedUser(e.target.value ? parseInt(e.target.value) : null)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="">All Team Members</option>
            {team.map(member => (
              <option key={member.id} value={member.id}>
                {member.full_name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Results Summary */}
      <div className="bg-gray-50 rounded-lg p-4 mb-4">
        <p className="text-sm text-gray-600">
          Showing {filteredVisits.length} visits
          {selectedUser && (
            <span> for {team.find(m => m.id === selectedUser)?.full_name}</span>
          )}
        </p>
      </div>

      {/* Visits Table */}
      {filteredVisits.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>No visits found matching your criteria.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date & Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sales Rep
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Purpose
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredVisits.map((visit) => (
                <tr key={visit.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">
                        {format(new Date(visit.planned_date), 'MMM d, yyyy')}
                      </div>
                      <div className="text-gray-500">
                        {format(new Date(visit.planned_date), 'h:mm a')}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">
                        {visit.user?.full_name || 'Unknown'}
                      </div>
                      <div className="text-gray-500">
                        {visit.user?.email}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    Customer #{visit.customer_id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(visit.status)}`}>
                      {visit.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {visit.purpose || '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
